# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Favick is a Japanese Spring Boot application that allows users to share one favorite item each. The project follows Clean Architecture principles with a hexagonal architecture pattern.

## Key Commands

### Build and Run

- `./mvnw clean compile` - Compile the project
- `./mvnw spring-boot:run` - Run the application in development mode
- `./mvnw clean package` - Build the project
- `./mvnw test` - Run all tests
- `./mvnw test -Dtest=ClassName` - Run a specific test class

### Database

- Database migrations are handled by Flyway automatically on startup
- Migration files are located in `src/main/resources/db/migration/`
- PostgreSQL is used as the database

## Architecture

The project follows Clean Architecture with these main layers:

### Domain Layer (`domain/`)

- **Models**: Value objects and entities (e.g., `User`, `Theme`, `Admin`)
- **Services**: Domain services containing business logic
- **Exceptions**: Custom domain exceptions
- **Features**: Organized by business capability (user, theme, admin, etc.)

### Application Layer (`application/`)

- **Use Cases**: Interactors implementing business use cases
- **Ports**: Interfaces for inbound and outbound operations
- **Services**: Application services for commands and queries
- **DTOs**: Data transfer objects for use case inputs/outputs

### Adapter Layer (`adapter/`)

- **Inbound Adapters** (`adapter/in/`):
  - `web/`: Web controllers and forms for the main application
  - `api/`: REST API controllers (if needed)
  - `security/`: Spring Security configuration
- **Outbound Adapters** (`adapter/out/`):
  - `persistence/`: JPA entities, repositories, and mappers
  - `mail/`: Email event listeners

### Package Structure by Feature

Each feature (user, theme, admin, etc.) has its own packages following the same pattern:

- `{feature}/model/` - Domain models and value objects
- `{feature}/service/` - Domain services
- `{feature}/repository/` - Repository interfaces and

implementations

- `{feature}/entity/` - JPA entities
- `{feature}/mapper/` - Entity-to-domain mappers

## Technology Stack

- **Framework**: Spring Boot 3.5.0
- **Security**: Spring Security 6
- **Database**: PostgreSQL with Flyway migrations
- **Template Engine**: Thymeleaf with layout dialect
- **Build Tool**: Maven
- **Testing**: JUnit 5, Spring Boot Test
- **Java Version**: Liberica JDK 17

### Key Dependencies

- **Lombok**: Reduces boilerplate code with annotations like `@Data`, `@Builder`
- **Spring Boot DevTools**: Enables hot reloading during development
- **Thymeleaf Layout Dialect**: Advanced template layouts and fragments
- **HikariCP**: High-performance JDBC connection pooling with optimized settings
- **Spring Cache**: Strategic caching for themes, roles, and language data
- **HTMX**: Frontend library for dynamic interactions without complex JavaScript
- **Rive**: Animation library for interactive graphics

### Performance & Monitoring

- **Spring Boot Actuator**: Production-ready features and health checks
- **Micrometer + Prometheus**: Metrics collection and monitoring
- **Async Logging**: Non-blocking log processing with Logback
- **Connection Pool Optimization**: HikariCP with leak detection and optimized timeouts

## Configuration

### Environment Profiles

- **Development Profile** (`application-development.properties`):
  - Local PostgreSQL database connection
  - Mailtrap SMTP for email testing
  - Comprehensive debug logging (Spring Security, Hibernate SQL)
  - Exception details included in responses
  - Hot reloading via Spring Boot DevTools

- **Test Profile** (`application-test.properties`):
  - Separate test database (`favick_test_db`)
  - HikariCP connection pool configuration
  - Same detailed logging as development
  - Isolated test environment

- **Production Profile** (`application-production.properties`):
  - Environment variable-based configuration
  - Mailgun SMTP integration
  - Security-focused (no exception details)
  - Optimized for performance

### Configuration Files

- **Properties**: Located in `src/main/resources/application*.properties`
- **i18n**: Messages in `src/main/resources/i18n/`
- **Static Files**: CSS, JS, images in `src/main/resources/static/`

### Logging Configuration

- **Development/Test Environments**:
  - DEBUG level for application code (`com.favick`)
  - Spring Security authentication flows logged at DEBUG
  - Hibernate SQL queries logged with parameters
  - Web request/response logging enabled

- **Production Environment**:
  - INFO level logging for security
  - No sensitive information in logs
  - Optimized for performance

### Frontend Technology

- **HTMX**: Enables dynamic HTML interactions without complex JavaScript frameworks
- **Rive**: Interactive animations and graphics rendering
- **Thymeleaf Layout Dialect**: Advanced template composition with layouts and fragments
- **Static Assets**: Organized in `/static/css/`, `/static/js/`, `/static/img/`

## Development Guidelines

### Naming Conventions

- Use Japanese comments and documentation where appropriate
- Domain models use descriptive names (e.g., `UserEmail`, `ThemeTitle`)
- Repository methods follow domain language
- Use `CommandDto` for input and `ResultDto` for output

### Security

- Custom security expressions in `adapter/in/security/expression/`
- Role-based access control with `@RankAuthorization`
- Separate authentication for admin and regular users

### Testing Structure

- Unit tests mirror the main source structure
- Domain model tests focus on value object validation
- Use case tests verify business logic
- Repository tests use Spring Boot Test slices

### Testing Annotations and Patterns

- **Main Test Annotations**:
  - `@SpringBootTest` - Full application context for integration tests
  - `@ActiveProfiles("test")` - Uses test profile configuration
  - `@DirtiesContext` - Ensures clean test context between tests
  - `@DisplayName` - Japanese descriptions for test methods

- **Test Naming Conventions**:
  - Use descriptive Japanese names with `@DisplayName`
  - Test method names follow `should_action_when_condition` pattern
  - Focus on behavior rather than implementation details

## File Upload

- Maximum file size: 5MB
- Custom validation with `@NotEmptyFile`
- File utilities in `common/util/FileUtils`

## Mail Configuration

- **Development/Test Environments**:
  - **Mailtrap**: Sandbox email testing service
  - SMTP configuration for local development
  - All emails captured in Mailtrap inbox

- **Production Environment**:
  - **Mailgun**: Production email service
  - Environment variable-based SMTP configuration
  - Event-driven email system with listeners

- **Email Events**:
  - User registration confirmation
  - Password reset notifications
  - Event listeners in `adapter/out/mail/`

## Internationalization

- Default locale with fallback handling
- Message files support Japanese and English
- Encoding: UTF-8

## Current Implementation Status

### ✅ Completed Features (90%)

#### Authentication & Authorization
- **User Registration/Login**: Complete with email verification
- **Admin Authentication**: Separate admin login system
- **Password Reset**: Token-based password reset flow
- **Security**: Custom rank authorization, CSP implementation, dual authentication contexts

#### Theme Management (Admin)
- **Theme CRUD**: Complete create, read, update, delete operations
- **Multilingual Support**: Japanese and English localization
- **Theme Status Management**: Automatic UPCOMING/ACTIVE/PAST status handling
- **Business Validation**: Date validation, editability rules

#### Favorite (Fave) Features
- **Fave Submission**: Complete submission via PickController
- **File Upload**: Image upload with 5MB limit and validation
- **Current Theme Integration**: GetCurrentThemePickedFaveInteractor implemented

#### Like System
- **Domain Models**: Complete Like entity and value objects
- **Application Layer**: CreateLike/DeleteLike use cases implemented
- **DTOs & Services**: Full command/query separation

### 🔄 In Progress Features (70%)

#### Like Feature UI Integration
- **Backend**: Complete domain and application layers
- **Frontend**: HTMX integration for dynamic like buttons (pending)
- **Templates**: Like button integration into Fave displays (pending)

#### Archive Functionality
- **Controller**: Basic ArchiveController implemented
- **Templates**: archive/index.html and archive/show.html created
- **Logic**: Past theme Fave listing logic (pending)

#### Account Management
- **Controller**: Basic AccountDashboardController implemented
- **Templates**: account/index.html created
- **Features**: Profile editing, Fave management (pending)

### 🚧 Planned Features (10%)

#### Frontend Enhancements
- **HTMX Integration**: Dynamic like functionality
- **Rive Animations**: Interactive graphics and animations
- **Progressive Enhancement**: Enhanced UX without JavaScript dependency

#### Search & Filter
- **Fave Search**: Theme-based and user-based filtering
- **Sorting**: By like count, submission date

## Performance Optimizations

### Caching Strategy
```java
@Cacheable("themes:getCurrentTheme")  // Current active theme
@Cacheable("themes:list")            // Theme listings
@Cacheable("roles:getById")          // Role data
@Cacheable("languages:list")         // Language options
```

### Database Optimization
- **Connection Pooling**: HikariCP with optimized settings
- **Query Optimization**: Strategic use of fetch strategies
- **Index Strategy**: Optimized database schema with proper indexing

### Logging Strategy
- **Environment-specific**: Different log levels per environment
- **Async Processing**: Non-blocking log operations
- **Log Separation**: Security, performance, and error logs separated
- **Structured Logging**: JSON format for production monitoring
