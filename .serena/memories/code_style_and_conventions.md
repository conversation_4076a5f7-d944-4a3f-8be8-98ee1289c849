# Code Style and Conventions

## Package Organization
### Feature-Based Package Structure
Each business feature (user, theme, admin, fave, etc.) follows the same pattern:
```
{feature}/
├── model/          # Domain models and value objects
├── service/        # Domain services
├── repository/     # Repository interfaces and implementations
├── entity/         # JPA entities
└── mapper/         # Entity-to-domain mappers
```

## Naming Conventions
- **Domain Models**: Descriptive value object names (e.g., `UserEmail`, `ThemeTitle`, `RankName`)
- **DTOs**: Clear input/output naming with `CommandDto` for input and `ResultDto` for output
- **Repository Methods**: Follow domain language conventions
- **Japanese Comments**: Use Japanese in comments and documentation where appropriate
- **Test Names**: Japanese descriptions with `@DisplayName` annotation

## Code Annotations & Patterns
### Lombok Usage
- Extensive use of Lombok annotations: `@Data`, `@Builder`, `@AllArgsConstructor`, `@NoArgsConstructor`
- Reduces boilerplate code significantly

### Validation Patterns
- Domain model validation using custom value objects
- Bean validation with custom annotations (`@NotEmptyFile`, `@MaxFileSize`, etc.)
- Input validation at the controller level with form objects

### Architecture Patterns
- **Clean Architecture**: Strict separation of concerns between layers
- **CQRS Pattern**: Separate command and query services in application layer
- **Hexagonal Architecture**: Adapters for inbound (web/api) and outbound (persistence/mail) operations
- **Event-Driven**: Domain events for cross-cutting concerns like email notifications

## Testing Conventions
### Test Structure
- Tests mirror the main source structure
- Unit tests focus on domain model behavior
- Integration tests use `@SpringBootTest` with test profiles
- Use of `@DisplayName` with Japanese descriptions for clarity

### Test Annotations
- `@SpringBootTest` - Full application context for integration tests
- `@ActiveProfiles("test")` - Uses test profile configuration  
- `@DirtiesContext` - Ensures clean test context between tests
- `@DisplayName` - Japanese descriptions for test methods

### Test Naming
- Method names follow `should_action_when_condition` pattern
- Focus on behavior rather than implementation details
- Clear separation of Arrange, Act, Assert sections

## Security Implementation
- Custom security expressions in `adapter/in/security/expression/`
- Role-based access control with custom annotations
- Separate authentication contexts for admin and regular users
- Security logging utilities for audit trails