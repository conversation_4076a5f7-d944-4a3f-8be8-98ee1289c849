# Technology Stack

## Core Framework
- **Spring Boot 3.5.0** - Main application framework
- **Java 17** - Programming language (Liberica JDK)
- **Maven** - Build tool with wrapper (Maven 3.9.9)

## Database & Persistence
- **PostgreSQL** - Primary database
- **Spring Data JPA** - ORM layer
- **Flyway** - Database migrations (automatic on startup)
- **HikariCP** - High-performance JDBC connection pooling

## Security & Authentication
- **Spring Security 6** - Authentication and authorization
- **Custom security expressions** - Role-based access control with `@RankAuthorization`
- **Separate admin and user authentication** - Dual authentication system

## Web & Templates
- **Spring MVC** - Web framework
- **Thymeleaf** - Template engine with layout dialect
- **HTMX** - Frontend library for dynamic interactions without complex JavaScript
- **Rive** - Interactive animations and graphics rendering

## Validation & Utilities
- **Spring Boot Validation** - Bean validation
- **Lombok** - Reduces boilerplate code with annotations like `@Data`, `@Builder`
- **Custom validation annotations** - `@NotEmptyFile`, `@MaxFileSize`, `@UniqueDate`, etc.

## Mail & Messaging
- **Spring Mail** - Email functionality
- **Mailtrap** - Development/test email sandbox
- **Mailgun** - Production email service
- **Event-driven email system** - With listeners for user registration, password reset

## Monitoring & Operations
- **Spring Boot Actuator** - Production-ready features
- **Micrometer with Prometheus** - Metrics collection
- **Spring Boot DevTools** - Development hot reloading

## Testing
- **JUnit 5** - Testing framework
- **Spring Boot Test** - Integration testing support
- **Mockito** - Mocking framework (implied from Spring Boot Test)

## File Storage
- **Local file storage** - Custom implementation with configurable upload directory
- **File validation** - Custom validators for file size and type