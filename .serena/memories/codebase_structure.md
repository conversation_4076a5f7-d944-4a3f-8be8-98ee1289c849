# Codebase Structure

## Root Directory Structure
```
favick/
├── .mvn/                    # Maven wrapper configuration
├── docs/                    # Project documentation
├── src/                     # Source code
├── pom.xml                  # Maven configuration
├── mvnw, mvnw.cmd          # Maven wrapper scripts
├── CLAUDE.md               # Claude Code instructions
└── README.md               # Project readme
```

## Source Code Organization (`src/`)
```
src/
├── main/
│   ├── java/com/favick/
│   │   ├── config/          # Spring configuration classes
│   │   ├── adapter/         # Hexagonal architecture adapters
│   │   │   ├── in/          # Inbound adapters (web, api, security)
│   │   │   └── out/         # Outbound adapters (persistence, mail)
│   │   ├── application/     # Application layer (use cases, services, DTOs)
│   │   ├── domain/          # Domain layer (models, services, exceptions)
│   │   ├── common/          # Shared utilities and helpers
│   │   └── FavickApplication.java  # Main application class
│   └── resources/
│       ├── db/migration/    # Flyway database migrations
│       ├── templates/       # Thymeleaf templates
│       ├── static/          # CSS, JavaScript, images
│       ├── i18n/           # Internationalization messages
│       └── application*.properties  # Configuration files
└── test/                    # Test code (mirrors main structure)
```

## Key Architectural Layers

### Domain Layer (`domain/feature/`)
Organized by business features:
- `user/` - User management domain
- `theme/` - Theme management domain  
- `fave/` - Favorite items domain
- `admin/` - Admin functionality domain
- `role/`, `rank/` - User roles and ranks
- `token/` - Authentication tokens
- `like/` - Like functionality
- `language/` - Multi-language support

Each feature contains:
- `model/` - Domain entities and value objects
- `service/` - Domain business logic
- `event/` - Domain events (where applicable)

### Application Layer (`application/`)
- `dto/` - Data Transfer Objects (command, query, result)
- `port/` - Interfaces (in for use cases, out for repositories)
- `service/` - Application services (command, query by feature)
- `usecase/` - Use case implementations (Interactors)

### Adapter Layer (`adapter/`)
#### Inbound Adapters (`adapter/in/`)
- `web/` - Web controllers, forms, mappers
- `api/` - REST API controllers  
- `security/` - Spring Security configuration

#### Outbound Adapters (`adapter/out/`)
- `persistence/` - JPA entities, repositories, mappers
- `mail/` - Email event listeners

## Template Structure (`templates/`)
```
templates/
├── layouts/        # Layout templates (default, account, console, error)
├── pages/          # Page templates organized by feature
├── components/     # Reusable template components  
└── pattern/        # Template patterns/fragments
```

## Static Resources (`static/`)
```
static/
├── css/           # Stylesheets
├── js/            # JavaScript files (HTMX, Rive)
├── img/           # Images and graphics
├── animation/     # Rive animation files
└── storage/       # File upload storage
```

## Configuration Files
- `application.properties` - Base configuration
- `application-production.properties` - Production environment
- Database connection, mail, file upload, monitoring settings
- Flyway migration configuration
- Internationalization settings