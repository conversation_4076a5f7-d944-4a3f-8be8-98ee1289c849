# Essential Commands for Favick Development

## Build and Compilation
```bash
./mvnw clean compile          # Compile the project
./mvnw clean package          # Build the project (creates JAR)
./mvnw clean install          # Install to local Maven repository
```

## Running the Application
```bash
./mvnw spring-boot:run        # Run application in development mode
```

## Testing
```bash
./mvnw test                   # Run all tests
./mvnw test -Dtest=ClassName  # Run specific test class
./mvnw test -Dtest="*Service*" # Run tests matching pattern
```

## Database Operations
- **Database migrations**: Handled automatically by Flyway on startup
- **Migration files**: Located in `src/main/resources/db/migration/`
- **Database**: PostgreSQL (connection configured in properties files)

## Development Profiles
The application supports multiple profiles:
- **Development**: `application-development.properties` (local PostgreSQL, Mailtrap)
- **Test**: `application-test.properties` (separate test database)
- **Production**: `application-production.properties` (environment variables)

## File Management
- **Upload directory**: `src/main/resources/static/storage`
- **Static assets**: CSS, JS, images in `src/main/resources/static/`
- **Templates**: Thymeleaf templates in `src/main/resources/templates/`

## Useful Maven Goals
```bash
./mvnw dependency:tree        # Show dependency tree
./mvnw spring-boot:build-info # Generate build information
./mvnw clean                  # Clean build artifacts
```

## System-Specific Commands (macOS)
Since the system is Darwin (macOS), standard Unix commands are available:
```bash
ls                           # List directory contents
cd                           # Change directory  
grep                         # Search text patterns
find                         # Find files and directories
git                          # Version control operations
```

## Development Tools
- **Hot Reloading**: Enabled via Spring Boot DevTools
- **Database Console**: Access via PostgreSQL tools
- **Email Testing**: Mailtrap for development environment
- **Monitoring**: Actuator endpoints at `/admin/monitor/`