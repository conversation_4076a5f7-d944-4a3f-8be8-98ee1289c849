# Task Completion Checklist

When completing any development task in the Favick project, follow this checklist to ensure code quality and project standards:

## Code Quality Checks
✅ **Run Tests**: Execute `./mvnw test` to ensure all tests pass
✅ **Build Verification**: Run `./mvnw clean package` to verify the project builds successfully
✅ **Code Compilation**: Ensure `./mvnw clean compile` completes without errors

## Architecture Compliance
✅ **Clean Architecture**: Verify changes follow the hexagonal architecture pattern
✅ **Layer Separation**: Ensure proper separation between domain, application, and adapter layers  
✅ **Dependency Direction**: Domain should not depend on outer layers
✅ **Feature Organization**: Keep related code organized within feature packages

## Code Standards
✅ **Lombok Usage**: Use appropriate Lombok annotations to reduce boilerplate
✅ **Naming Conventions**: Follow established naming patterns (CommandDto, ResultDto, etc.)
✅ **Japanese Documentation**: Include Japanese comments where appropriate
✅ **Value Objects**: Use descriptive domain value objects (UserEmail, ThemeTitle, etc.)

## Testing Requirements
✅ **Unit Tests**: Write tests for domain models and business logic
✅ **Integration Tests**: Add `@SpringBootTest` tests for complex interactions
✅ **Test Naming**: Use `@DisplayName` with descriptive Japanese names
✅ **Test Structure**: Follow Arrange-Act-Assert pattern

## Security Considerations
✅ **Authorization**: Verify proper role-based access control
✅ **Input Validation**: Validate all user inputs with appropriate annotations
✅ **Security Logging**: Use SecurityLogUtil for audit trails where needed
✅ **No Sensitive Data**: Ensure no secrets or sensitive information in code

## Database & Migration
✅ **Migration Files**: Create Flyway migrations for schema changes in `db/migration/`
✅ **Entity Mappings**: Ensure JPA entities properly map to domain models
✅ **Repository Tests**: Test repository implementations with proper data setup

## Web & Templates
✅ **Template Organization**: Place templates in appropriate feature directories
✅ **Form Validation**: Implement proper form validation with error handling
✅ **Responsive Design**: Ensure templates work across different screen sizes
✅ **Accessibility**: Consider accessibility standards in UI implementation

## Documentation
✅ **Code Comments**: Add meaningful comments explaining business logic
✅ **API Documentation**: Document any new endpoints or significant changes
✅ **Update CLAUDE.md**: Update project instructions if adding new patterns or conventions

## Final Verification
✅ **Manual Testing**: Test functionality manually in development environment
✅ **Error Handling**: Verify appropriate error handling and user feedback
✅ **Performance**: Consider performance implications of changes
✅ **Code Review**: Review code for potential improvements and edge cases

## Important Notes
- **Never commit directly to main** - Use feature branches and pull requests
- **Database changes require migrations** - Always create Flyway migration files
- **Email testing** - Use Mailtrap for development email testing
- **File uploads** - Test with actual file uploads and size limits
- **Multi-language** - Test with both Japanese and English content where applicable