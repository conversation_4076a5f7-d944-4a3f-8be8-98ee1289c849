# Favick Project Overview

## Purpose
Favick is a Japanese Spring Boot application that allows users to share one favorite item each. It's a social platform where each person can pick and share their single favorite item with others.

## Core Features
- User registration and authentication (including admin authentication)
- Theme management - administrators can create themes for users to submit favorites
- Favorite (Fave) submission and management 
- Like system for favorites
- Multi-language support (Japanese/English)
- File upload for photos
- Admin console for theme management
- Archive functionality to view past themes and favorites

## Architecture Pattern
The project follows **Clean Architecture** principles with a **hexagonal architecture** pattern, organized into clear layers:

### Domain Layer (`domain/`)
- Models, services, exceptions organized by business capability
- Features: user, theme, admin, fave, role, token, language, rank, like, etc.
- Domain services contain business logic
- Custom domain exceptions and validation

### Application Layer (`application/`)
- Use cases (Interactors) implementing business use cases
- Ports (interfaces) for inbound and outbound operations
- Services for commands and queries (CQRS pattern)
- DTOs for use case inputs/outputs

### Adapter Layer (`adapter/`)
- **Inbound**: Web controllers, API controllers, security configuration
- **Outbound**: JPA repositories, email event listeners
- Mappers for converting between layers

## Business Domain
Users participate in themes where they can submit their single favorite item. Other users can like these favorites. The system supports different user ranks and has comprehensive theme management capabilities.