# 要件定義書

## 概要

ユーザーがひとりひとつの「お気に入り」を選んでを共有し、コミュニケーションを楽しめる場を提供する。
公平性やシンプルさを重視し、エンゲージメントの高いサービスを目指す。

### サービス名の由来

お気に入り(fave)を選ぶ(pick)

### ジャンル

エンタメ系投稿サービス

## 目的

- 個人開発の成功事例の構築
- 収益化

## 対象ユーザー

- 自分の「お気に入り」を共有したい人
- SNS疲れを感じている人
- 他人との競争ではなく純粋に作品の評価を楽しみたい人
- 投稿や閲覧を通じて、コンテンツを楽しみたい人

## 機能要件

### ユーザー管理関連

#### 認証機能

- ユーザー登録
- ログイン
  - メールアドレス
  - パスワード
- ログアウト
- メールアドレス変更
- パスワードリセット
- ソーシャルログイン

#### 管理機能

- 一般ユーザー
  - 基本情報管理
    - メールアドレス
    - パスワード
  - お気に入り（fave）管理
  - 現在の「気に入った！（like）」の確認
  - 過去のテーマごとのお気に入り（fave）の人気を確認
  - 取得バッジの確認
  - アカウント削除
- 管理ユーザー
  - 基本情報管理
    - メールアドレス
    - パスワード
  - ユーザー管理
  - テーマ管理
    - 月間のテーマ設定
    - テーマに沿った投稿の促進
  - 審査管理
    - お気に入り（fave）をチェックし、不適切なモノを排除
  - 通知管理
  - お問い合わせ管理

### 表示・閲覧関連

#### お気に入り一覧表示

- 現在のテーマ
  - 並び替え
    - 新着順
  - 無限スクロール
  - 公平性を保つため投稿者名は非表示
- 過去のテーマ
  - テーマごとの1位のみを表示した一覧
  - 総投稿数を表示
  - 開催期間でグルーピング
  - ページネーション
  - 過去のテーマ詳細一覧ページ
    - 過去のテーマ一覧ページでテーマをクリック
    - そのテーマの全件一覧

#### 閲覧制限

- 未ログインユーザーは閲覧のみ可能
- 投稿やいいねをするにはログインが必要

### お気に入り（fave）関連

#### お気に入り（fave）を選ぶ（pick）機能

- ユーザーはひとつのテーマでひとつのお気に入り（fave）のみ選ぶ（pick）ことが可能
- テーマ開催中のお気に入り（fave）の変更は不可

### インタラクション関連

#### 気に入った！（like）機能

- ログインユーザーのみ「気に入った！（like）」可能
- ユーザーごとにひとつのお気に入り（fave）にひとつの「気に入った！（like）」まで
- 気に入った！（like）の数のリアルタイム表示

#### 人気機能

- ログインユーザーは管理画面で自身の人気のみ確認可能
- リワードとしてバッジや特典を提供

#### SNS連携機能

- ログインユーザーはSNS連携機能を利用して、お気に入り（fave）をSNSに共有可能

## 非機能要件

### UI/UX

- ミニマルで直感的だがモダンなインターフェース
  - カラー
    - ニュートラルなカラーパレット
      - 白、グレー、淡い青など落ち着いた配色を基調とし、画像が映える背景
    - アクセントカラー
      - アクションボタンやいいね機能に対して鮮やかなカラー（例: コーラルやターコイズ）を採用し、視認性を高める
  - 機能
    - モバイルファースト
    - ダークモード対応
    - 国際化

### パフォーマンス

- ページ読み込み時間
  - 3秒以内
  - 画像最適化による高速表示
- 同時アクセス数
  - 100ユーザー

### セキュリティ

- SSL/TLS対応
- ユーザー認証の厳格化
- お気に入り（fave）データのバックアップ
- 個人情報の適切な保護

### 可用性

- サービス稼働率
  - 99.0%（月間約7時間のダウンタイム許容）
- 定期メンテナンス
  - 週1回、2時間
- メンテナンス時間帯
  - ユーザー分析に基づいて設定（例：深夜2-4時）

### 拡張性

- 審査プロセスを効率化するため、AIモデレーションの導入を検討
- 音声版や動画版を追加する可能性

## 制約事項

- 個人開発案件
- 開発リソースの制限
- 初期段階での収益化の限界

## リスクと対策

### リスク

- 初期コンテンツの不足
- ユーザー離脱
- 不適切コンテンツの投稿
- システム負荷の増大

### 対策案

- 知人からの初期投稿促進
- ゲーミフィケーションによる継続率向上
- 段階的な審査システムの確立
- スケーラブルなインフラ設計
