# ドメイン分析

## 中核サブドメイン

サービスの中心的な価値提供であり、競争優位性の源泉となる領域。

### 1.SNS疲れを解消するシンプルな投稿体験

- テーマごとにひとつだけのお気に入り（fave）を投稿できる制限
- 情報過多を防ぎ、質の高いコンテンツに焦点を当てる設計
- シンプルで直感的なユーザーインターフェース

### 2.匿名性による公平な評価システム

- 投稿者名を非表示にすることで、コンテンツそのものの価値で評価
- 知名度や人気に左右されない公平な評価環境
- 「いいね！」（like）による純粋な作品評価

### 3.健全性の確保

- コンテンツの審査プロセス
- 不適切な投稿の排除
- 安心して利用できる環境の提供

## 補完サブドメイン

競争優位性を生み出さないが、中核サブドメインを支援し、サービスの魅力を高める重要な領域。

### 1.バッジ・ランクシステム

- バッジ（金・銀・銅）による評価の可視化
- ユーザーランク（SEED, SPROUT, BUD, BLOOM, GARDEN）による成長表現
- 継続利用を促進するゲーミフィケーション要素

### 2.テーマ管理

- 月間テーマの企画・作成・管理
- テーマのライフサイクル（予定→開催中→終了）管理
- 投稿の方向性を決める重要な機能

### 3.コンテンツモデレーション

- 投稿の審査フロー管理
- 健全なコミュニティ維持のための監視
- 将来的なAIモデレーション導入の検討

## 一般サブドメイン

差別化要因にはならない、標準的な機能だがサービス運営に必要な領域

### 1.認証・認可

- メールアドレス/パスワード認証
- ソーシャルログイン（Google/Instagram/X）
- 権限管理（一般ユーザー/管理者）

### 2.ユーザー管理

- アカウント情報管理
- プロフィール設定
- メールアドレス変更/パスワードリセット

### 3.通知・コミュニケーション

- システム通知の管理と配信
- イベント告知
- メンテナンス情報の提供

### 4.問い合わせ管理

- ユーザーからの問い合わせ対応
- 問題報告の処理
- 運営とユーザー間のコミュニケーション

### 5.統計・分析

- ユーザー行動分析
- KPI測定
- エンゲージメント分析

### 6.収益化

- 最小限の広告導入
- 将来的な有料会員制度の検討
- 企業コラボレーションの可能性
