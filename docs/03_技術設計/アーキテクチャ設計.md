# アーキテクチャ設計

``` shell
./src/main/java/com/favick/
├── adapter
│   ├── in
│   │   ├── api
│   │   │   ├── controller
│   │   │   ├── error
│   │   │   └── response
│   │   ├── security
│   │   └── web
│   │       ├── controller
│   │       ├── error
│   │       └── helper
│   └── out
│       ├── mail
│       └── persistence
│           └── feature
│               └── {feature-name}/
│                   ├── entity
│                   ├── mapper
│                   └── repository
├── application
│   ├── port
│   │   ├── in
│   │   └── out
│   ├── service
│   │   └── {feature-name}/
│   │       ├── command
│   │       ├── dto
│   │       │   ├── command
│   │       │   └── query
│   │       └── query
│   └── usecase
├── common
│   ├── factory
│   ├── util
│   └── validation
├── config
│   ├── application
│   ├── database
│   └── web
└── domain
    ├── exception
    ├── feature
    │   ├── {feature-name}
    │   │   ├── model
    │   │   └── service
    │   └── ...
    └── validation
```
