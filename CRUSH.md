# CRUSH.md - Favick Project Guidelines

## Build/Test Commands
```bash
./mvnw clean compile              # Compile the project
./mvnw spring-boot:run            # Run application in dev mode
./mvnw test                       # Run all tests
./mvnw test -Dtest=ClassName      # Run specific test class
./mvnw test -Dtest=ClassName#methodName  # Run specific test method
./mvnw clean package              # Build JAR file
./mvnw clean verify               # Run all checks including integration tests
```

## Code Style Guidelines

### Import Organization
1. Java standard library (`java.*`, `javax.*`)
2. Spring Framework (`org.springframework.*`)
3. Third-party libraries (Lombok, Jakarta, etc.)
4. JUnit/Mockito (in test files)
5. Project imports grouped by feature (`com.favick.*`)
6. Static imports last

### Naming Conventions
- **Classes**: PascalCase (`User`, `ThemeCommandService`, `CreateUserCommandDto`)
- **Methods**: camelCase (`findById`, `validateEmailUniqueness`)
- **Variables**: camelCase matching type (`UserId id`, `UserName name`)
- **Constants**: UPPER_SNAKE_CASE (`MAX_LENGTH`, `DEFAULT_TIMEOUT`)
- **Test methods**: `methodName_scenario_expectedBehavior` with Japanese `@DisplayName`

### Error Handling
- Use domain exceptions: `ValueObjectException("field", "message.key", params)`
- Collect multiple errors: `InvalidDomainsException(List<DomainException>)`
- Controllers: Try-catch with error logging and redirect
- Resource not found: `ResourceNotFoundException("entity.type", "details")`

### Testing Patterns
- AAA pattern: Arrange, Act, Assert
- Mock dependencies with `@Mock` and `@InjectMocks`
- Japanese descriptions in `@DisplayName`
- Test data as static final fields or method variables