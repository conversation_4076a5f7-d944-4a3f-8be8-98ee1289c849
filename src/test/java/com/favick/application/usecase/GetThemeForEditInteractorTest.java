package com.favick.application.usecase;

import com.favick.application.dto.query.GetThemeForEditQueryDto;
import com.favick.application.dto.result.ThemeEditResultDto;
import com.favick.application.service.theme.query.ThemeQueryService;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import com.favick.domain.feature.theme.model.*;
import com.favick.domain.feature.theme_localization.model.ThemeDescription;
import com.favick.domain.feature.theme_localization.model.ThemeLocalization;
import com.favick.domain.feature.theme_localization.model.ThemeLocalizationId;
import com.favick.domain.feature.theme_localization.model.ThemeTitle;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import org.springframework.data.util.Pair;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("GetThemeForEditInteractor のテスト")
class GetThemeForEditInteractorTest {

    @Mock
    private ThemeQueryService themeQueryService;


    @InjectMocks
    private GetThemeForEditInteractor getThemeForEditInteractor;

    @Test
    @DisplayName("正常系：存在するテーマIDを指定すると編集用詳細情報を取得できる")
    void handle_existingThemeId_shouldReturnThemeEditDetails() {
        // Arrange
        UUID themeId = UUID.randomUUID();
        UUID localizationJaId = UUID.randomUUID();
        UUID localizationEnId = UUID.randomUUID();
        LocalDate startDate = LocalDate.now().plusMonths(1).withDayOfMonth(1);
        LocalDateTime now = LocalDateTime.now();

        GetThemeForEditQueryDto query = new GetThemeForEditQueryDto(themeId);

        Theme theme = Theme.reconstruct(
            new ThemeId(themeId),
            new ThemeType(ThemeTypeValue.PHOTO),
            new ThemeStartDate(startDate),
            now,
            now
        );

        ThemeLocalization localizationJa = ThemeLocalization.reconstruct(
            new ThemeLocalizationId(localizationJaId),
            new ThemeId(themeId),
            new LanguageCode(LanguageCodeValue.JA),
            new ThemeTitle("テストタイトル（日本語）"),
            new ThemeDescription("テスト説明文（日本語）"),
            now,
            now
        );

        ThemeLocalization localizationEn = ThemeLocalization.reconstruct(
            new ThemeLocalizationId(localizationEnId),
            new ThemeId(themeId),
            new LanguageCode(LanguageCodeValue.EN),
            new ThemeTitle("Test Title (English)"),
            new ThemeDescription("Test Description (English)"),
            now,
            now
        );

        Map<LanguageCode, ThemeLocalization> localizations = Map.of(
            new LanguageCode(LanguageCodeValue.JA), localizationJa,
            new LanguageCode(LanguageCodeValue.EN), localizationEn
        );
        Pair<Theme, Map<LanguageCode, ThemeLocalization>> integratedTheme = Pair.of(theme, localizations);
        
        when(themeQueryService.getIntegratedMultiLangById(any(ThemeId.class)))
            .thenReturn(Optional.of(integratedTheme));

        // Act
        ThemeEditResultDto result = getThemeForEditInteractor.handle(query);

        // Assert
        assertEquals(themeId, result.id());
        assertEquals("テストタイトル（日本語）", result.titleJa());
        assertEquals("テスト説明文（日本語）", result.descriptionJa());
        assertEquals("Test Title (English)", result.titleEn());
        assertEquals("Test Description (English)", result.descriptionEn());
        assertEquals(ThemeTypeValue.PHOTO, result.type());
        assertEquals(startDate, result.startDate());
        assertEquals(now, result.createdAt());
        assertEquals(now, result.updatedAt());

        verify(themeQueryService).getIntegratedMultiLangById(any(ThemeId.class));
    }

    @Test
    @DisplayName("異常系：存在しないテーマIDを指定すると例外が発生する")
    void handle_nonExistingThemeId_shouldThrowResourceNotFoundException() {
        // Arrange
        UUID nonExistingId = UUID.randomUUID();
        GetThemeForEditQueryDto query = new GetThemeForEditQueryDto(nonExistingId);

        when(themeQueryService.getIntegratedMultiLangById(any(ThemeId.class)))
            .thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> getThemeForEditInteractor.handle(query));

        verify(themeQueryService).getIntegratedMultiLangById(any(ThemeId.class));
    }


    @Test
    @DisplayName("異常系：無効なテーマID形式の場合はバリデーションエラーが発生する")
    void handle_invalidThemeId_shouldThrowValidationError() {
        // Arrange - Using null to cause ThemeId validation to fail
        GetThemeForEditQueryDto query = new GetThemeForEditQueryDto(null);

        // Act & Assert
        assertThrows(InvalidDomainsException.class, () -> getThemeForEditInteractor.handle(query));

        verify(themeQueryService, never()).getIntegratedMultiLangById(any(ThemeId.class));
    }
}
