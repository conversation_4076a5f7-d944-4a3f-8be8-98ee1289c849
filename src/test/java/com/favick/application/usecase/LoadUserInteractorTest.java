package com.favick.application.usecase;

import com.favick.application.dto.query.LoadUserQueryDto;
import com.favick.application.dto.result.UserDetailsResultDto;
import com.favick.application.service.rank.query.RankQueryService;
import com.favick.application.service.user.query.UserQueryService;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.domain.feature.rank.model.Rank;
import com.favick.domain.feature.rank.model.RankId;
import com.favick.domain.feature.rank.model.RankName;
import com.favick.domain.feature.rank.model.RankNameValue;
import com.favick.domain.feature.user.model.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("LoadUserInteractor のテスト")
class LoadUserInteractorTest {

    @Mock
    private UserQueryService userQueryService;

    @Mock
    private RankQueryService rankQueryService;

    @InjectMocks
    private LoadUserInteractor loadUserInteractor;

    @Test
    @DisplayName("正常系：メールアドレスから認証用ユーザー情報を取得できる")
    void handle_validEmail_shouldReturnUserDetailsDto() {
        // Arrange
        String email = "<EMAIL>";
        UUID userId = UUID.randomUUID();
        UUID rankId = UUID.randomUUID();
        LocalDateTime now = LocalDateTime.now();

        User user = User.reconstruct(
            new UserId(userId),
            new RankId(rankId),
            new UserName("テストユーザー"),
            new UserEmail(email),
            new UserPassword("encodedPassword"),
            new UserEnabled(true),
            new UserImageUri("https://example.com/image.jpg"),
            now,
            now
        );

        Rank rank = Rank.reconstruct(
            new RankId(rankId),
            new RankName(RankNameValue.RANK_SPROUT),
            now,
            now
        );

        LoadUserQueryDto queryDto = new LoadUserQueryDto(email);

        when(userQueryService.getByEmail(any(UserEmail.class))).thenReturn(Optional.of(user));
        when(rankQueryService.getById(any(RankId.class))).thenReturn(Optional.of(rank));

        // Act
        UserDetailsResultDto result = loadUserInteractor.handle(queryDto);

        // Assert
        assertNotNull(result);
        assertEquals(userId, result.id());
        assertEquals(rankId, result.rankId());
        assertEquals("テストユーザー", result.name());
        assertEquals(RankNameValue.RANK_SPROUT, result.rankName());
        assertEquals(email, result.email());
        assertEquals("encodedPassword", result.password());
        assertTrue(result.enabled());
        assertEquals("https://example.com/image.jpg", result.imageUrl());
        assertEquals(now, result.createdAt());
        assertEquals(now, result.updatedAt());

        verify(userQueryService).getByEmail(any(UserEmail.class));
        verify(rankQueryService).getById(any(RankId.class));
    }

    @Test
    @DisplayName("異常系：存在しないメールアドレスの場合、例外が発生する")
    void handle_nonExistingEmail_shouldThrowException() {
        // Arrange
        String nonExistingEmail = "<EMAIL>";
        LoadUserQueryDto queryDto = new LoadUserQueryDto(nonExistingEmail);

        when(userQueryService.getByEmail(any(UserEmail.class)))
            .thenReturn(Optional.empty());

        // Act & Assert
        Exception exception = assertThrows(ResourceNotFoundException.class, () -> loadUserInteractor.handle(queryDto));

        assertEquals("ResourceNotFound", exception.getMessage());
        verify(userQueryService).getByEmail(any(UserEmail.class));
        verify(rankQueryService, never()).getById(any());
    }

    @Test
    @DisplayName("異常系：ランクIDが無効な場合、例外が発生する")
    void handle_invalidRankId_shouldThrowException() {
        // Arrange
        String email = "<EMAIL>";
        UUID userId = UUID.randomUUID();
        UUID invalidRankId = UUID.randomUUID();
        LocalDateTime now = LocalDateTime.now();

        User user = User.reconstruct(
            new UserId(userId),
            new RankId(invalidRankId),
            new UserName("テストユーザー"),
            new UserEmail(email),
            new UserPassword("encodedPassword"),
            new UserEnabled(true),
            new UserImageUri("https://example.com/image.jpg"),
            now,
            now
        );

        LoadUserQueryDto queryDto = new LoadUserQueryDto(email);

        when(userQueryService.getByEmail(any(UserEmail.class))).thenReturn(Optional.of(user));
        when(rankQueryService.getById(any(RankId.class))).thenReturn(Optional.empty());

        // Act & Assert
        Exception exception = assertThrows(ResourceNotFoundException.class, () -> loadUserInteractor.handle(queryDto));

        assertEquals("ResourceNotFound", exception.getMessage());
        verify(userQueryService).getByEmail(any(UserEmail.class));
        verify(rankQueryService).getById(any(RankId.class));
    }

    @Test
    @DisplayName("異常系：無効なメールアドレス形式の場合はバリデーションエラーが発生する")
    void handle_invalidEmail_shouldThrowValidationError() {
        // Arrange
        String invalidEmail = "invalid-email";
        LoadUserQueryDto queryDto = new LoadUserQueryDto(invalidEmail);

        // Act & Assert
        assertThrows(InvalidDomainsException.class, () -> loadUserInteractor.handle(queryDto));

        verify(userQueryService, never()).getByEmail(any(UserEmail.class));
        verify(rankQueryService, never()).getById(any(RankId.class));
    }
}
