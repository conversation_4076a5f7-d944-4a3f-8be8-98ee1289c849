package com.favick.application.usecase;

import com.favick.application.dto.command.CreateThemeCommandDto;
import com.favick.application.dto.command.LocalizedContent;
import com.favick.application.service.theme.command.ThemeCommandService;
import com.favick.application.service.theme.query.ThemeQueryService;
import com.favick.application.service.theme_localization.command.ThemeContent;
import com.favick.application.service.theme_localization.command.ThemeLocalizationCommandService;
import com.favick.application.service.theme_localization.command.ThemeLocalizationValidator;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.domain.exception.DomainException;
import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import com.favick.domain.feature.theme.model.Theme;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.theme.service.ThemeDomainService;
import com.favick.domain.feature.theme_localization.model.ThemeDescription;
import com.favick.domain.feature.theme_localization.model.ThemeLocalization;
import com.favick.domain.feature.theme_localization.model.ThemeLocalizationId;
import com.favick.domain.feature.theme_localization.model.ThemeTitle;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("CreateThemeInteractor のテスト")
class CreateThemeInteractorTest {

    @Mock
    private ThemeLocalizationValidator themeLocalizationValidator;

    @Mock
    private ThemeCommandService themeCommandService;

    @Mock
    private ThemeLocalizationCommandService themeLocalizationCommandService;

    @Mock
    private ThemeDomainService themeDomainService;

    @Mock
    private ThemeQueryService themeQueryService;

    @InjectMocks
    private CreateThemeInteractor createThemeInteractor;

    @Test
    @DisplayName("正常系：テーマ作成コマンドが正しく処理される")
    void handle_validCommand_shouldProcessSuccessfully() {
        // Arrange
        LocalDate startDate = LocalDate.now().plusMonths(1).withDayOfMonth(1);
        CreateThemeCommandDto command = new CreateThemeCommandDto(
            Map.of(
                LanguageCodeValue.JA, new LocalizedContent("春のテーマ", "春の作品を募集します"),
                LanguageCodeValue.EN, new LocalizedContent("Spring Theme", "Submit your spring works")
            ),
            "PHOTO",
            startDate
        );

        ThemeId expectedThemeId = new ThemeId(UUID.randomUUID());

        // Validatorのモック設定
        when(themeLocalizationValidator.validateLocalizations(eq(command.localizations()), any()))
            .thenReturn(Map.of(
                new LanguageCode(LanguageCodeValue.JA), new ThemeContent(
                    new ThemeTitle("春のテーマ"),
                    new ThemeDescription("春の作品を募集します")
                ),
                new LanguageCode(LanguageCodeValue.EN), new ThemeContent(
                    new ThemeTitle("Spring Theme"),
                    new ThemeDescription("Submit your spring works")
                )
            ));
        doNothing().when(themeDomainService).validateStartDateUniqueness(any(), any());

        when(themeCommandService.create(any(Theme.class)))
            .thenReturn(expectedThemeId);
        when(themeLocalizationCommandService.create(any(ThemeLocalization.class)))
            .thenReturn(new ThemeLocalizationId(UUID.randomUUID()));

        // Act
        createThemeInteractor.handle(command);

        // Assert
        verify(themeLocalizationValidator).validateLocalizations(eq(command.localizations()), any());
        verify(themeDomainService).validateStartDateUniqueness(any(), any());
        verify(themeCommandService).create(any(Theme.class));
        verify(themeLocalizationCommandService, times(2)).create(any(ThemeLocalization.class));
    }

    @Test
    @DisplayName("異常系：過去の日付を指定した場合、バリデーションエラーが発生する")
    void handle_pastDate_shouldThrowValidationError() {
        // Arrange
        LocalDate pastDate = LocalDate.now().minusDays(1);
        CreateThemeCommandDto command = new CreateThemeCommandDto(
            Map.of(
                LanguageCodeValue.JA, new LocalizedContent("春のテーマ", "春の作品を募集します"),
                LanguageCodeValue.EN, new LocalizedContent("Spring Theme", "Submit your spring works")
            ),
            "PHOTO",
            pastDate
        );

        when(themeLocalizationValidator.validateLocalizations(eq(command.localizations()), any()))
            .thenReturn(Map.of());

        // Act & Assert
        InvalidDomainsException exception = assertThrows(InvalidDomainsException.class, () -> createThemeInteractor.handle(command));

        assertEquals(1, exception.getExceptions().size());
        DomainException validationError = exception.getExceptions().get(0);
        assertEquals("startDate", validationError.getField());
        assertEquals("Domain Error", validationError.getMessage());

        verify(themeLocalizationCommandService, never()).create(any(ThemeLocalization.class));
    }
}
