package com.favick.application.usecase;

import com.favick.application.dto.command.LocalizedContent;
import com.favick.application.dto.command.UpdateThemeCommandDto;
import com.favick.application.service.theme.command.ThemeCommandService;
import com.favick.application.service.theme.query.ThemeQueryService;
import com.favick.application.service.theme_localization.command.ThemeContent;
import com.favick.application.service.theme_localization.command.ThemeLocalizationCommandService;
import com.favick.application.service.theme_localization.command.ThemeLocalizationValidator;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.domain.exception.DomainException;
import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import com.favick.domain.feature.theme.model.*;
import com.favick.domain.feature.theme.service.ThemeDomainService;
import com.favick.domain.feature.theme_localization.model.ThemeDescription;
import com.favick.domain.feature.theme_localization.model.ThemeLocalization;
import com.favick.domain.feature.theme_localization.model.ThemeLocalizationId;
import com.favick.domain.feature.theme_localization.model.ThemeTitle;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import org.springframework.data.util.Pair;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("UpdateThemeInteractor のテスト")
class UpdateThemeInteractorTest {

    @Mock
    private ThemeLocalizationValidator themeLocalizationValidator;

    @Mock
    private ThemeCommandService themeCommandService;

    @Mock
    private ThemeLocalizationCommandService themeLocalizationCommandService;

    @Mock
    private ThemeDomainService themeDomainService;

    @Mock
    private ThemeQueryService themeQueryService;


    @InjectMocks
    private UpdateThemeInteractor updateThemeInteractor;

    @Test
    @DisplayName("正常系：テーマ更新コマンドが正しく処理される")
    void handle_validCommand_shouldProcessSuccessfully() {
        // Arrange
        UUID themeId = UUID.randomUUID();
        LocalDate startDate = LocalDate.now().plusMonths(1).withDayOfMonth(1);
        UpdateThemeCommandDto command = new UpdateThemeCommandDto(
            themeId,
            Map.of(
                LanguageCodeValue.JA, new LocalizedContent("夏のテーマ", "夏の作品を募集します"),
                LanguageCodeValue.EN, new LocalizedContent("Summer Theme", "Submit your summer works")
            ),
            "PHOTO",
            startDate
        );

        // Validatorのモック設定
        when(themeLocalizationValidator.validateLocalizations(eq(command.localizations()), any()))
            .thenReturn(Map.of(
                new LanguageCode(LanguageCodeValue.JA), new ThemeContent(
                    new ThemeTitle("夏のテーマ"),
                    new ThemeDescription("夏の作品を募集します")
                ),
                new LanguageCode(LanguageCodeValue.EN), new ThemeContent(
                    new ThemeTitle("Summer Theme"),
                    new ThemeDescription("Submit your summer works")
                )
            ));

        Theme existingTheme = Theme.reconstruct(
            new ThemeId(themeId),
            new ThemeType(ThemeTypeValue.PHOTO),
            new ThemeStartDate(startDate.minusDays(1)),
            java.time.LocalDateTime.now().minusDays(1),
            java.time.LocalDateTime.now().minusDays(1)
        );
        // 既存のローカライゼーションデータを作成
        ThemeLocalization existingLocalizationJa = ThemeLocalization.reconstruct(
            new ThemeLocalizationId(UUID.randomUUID()),
            new ThemeId(themeId),
            new LanguageCode(LanguageCodeValue.JA),
            new ThemeTitle("既存のタイトル"),
            new ThemeDescription("既存の説明"),
            java.time.LocalDateTime.now().minusDays(1),
            java.time.LocalDateTime.now().minusDays(1)
        );
        ThemeLocalization existingLocalizationEn = ThemeLocalization.reconstruct(
            new ThemeLocalizationId(UUID.randomUUID()),
            new ThemeId(themeId),
            new LanguageCode(LanguageCodeValue.EN),
            new ThemeTitle("Existing Title"),
            new ThemeDescription("Existing Description"),
            java.time.LocalDateTime.now().minusDays(1),
            java.time.LocalDateTime.now().minusDays(1)
        );
        
        Map<LanguageCode, ThemeLocalization> localizations = Map.of(
            new LanguageCode(LanguageCodeValue.JA), existingLocalizationJa,
            new LanguageCode(LanguageCodeValue.EN), existingLocalizationEn
        );
        Pair<Theme, Map<LanguageCode, ThemeLocalization>> integratedTheme = Pair.of(existingTheme, localizations);
        
        when(themeQueryService.getIntegratedMultiLangById(any()))
            .thenReturn(Optional.of(integratedTheme));
        doNothing().when(themeDomainService).validateStartDateUniqueness(any(), any(), any());

        // ローカライゼーション更新のモック設定
        when(themeLocalizationCommandService.update(any(ThemeLocalization.class)))
            .thenReturn(new ThemeLocalizationId(UUID.randomUUID()));

        // Act
        updateThemeInteractor.handle(command);

        // 適切なデータでサービスが呼び出されることを確認
        verify(themeLocalizationValidator).validateLocalizations(eq(command.localizations()), any());
        verify(themeQueryService).getIntegratedMultiLangById(any());
        verify(themeDomainService).validateStartDateUniqueness(any(), any(), any());
        verify(themeCommandService).update(any(Theme.class));

        // ローカライゼーションが更新されることを確認 (2回呼び出される想定)
        verify(themeLocalizationCommandService, times(2)).update(any(ThemeLocalization.class));
    }

    @Test
    @DisplayName("異常系：存在しないテーマIDを指定した場合、ResourceNotFoundExceptionが発生する")
    void handle_nonExistingThemeId_shouldThrowResourceNotFoundException() {
        // Arrange
        UUID nonExistingId = UUID.randomUUID();
        LocalDate startDate = LocalDate.now().plusMonths(1).withDayOfMonth(1);
        UpdateThemeCommandDto command = new UpdateThemeCommandDto(
            nonExistingId,
            Map.of(
                LanguageCodeValue.JA, new LocalizedContent("夏のテーマ", "夏の作品を募集します"),
                LanguageCodeValue.EN, new LocalizedContent("Summer Theme", "Submit your summer works")
            ),
            "PHOTO",
            startDate
        );

        when(themeLocalizationValidator.validateLocalizations(eq(command.localizations()), any()))
            .thenReturn(Map.of());
        when(themeQueryService.getIntegratedMultiLangById(any()))
            .thenReturn(Optional.empty());

        // Act & Assert
        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class, () -> updateThemeInteractor.handle(command));

        assertEquals("ResourceNotFound", exception.getMessage());
        // ローカライゼーションサービスは呼び出されないことを確認
        verify(themeLocalizationCommandService, never()).update(any(ThemeLocalization.class));
    }

    @Test
    @DisplayName("異常系：過去の日付を指定した場合、バリデーションエラーが発生する")
    void handle_pastDate_shouldThrowValidationError() {
        // Arrange
        UUID themeId = UUID.randomUUID();
        LocalDate pastDate = LocalDate.now().minusDays(1); // 過去の日付（無効）
        UpdateThemeCommandDto command = new UpdateThemeCommandDto(
            themeId,
            Map.of(
                LanguageCodeValue.JA, new LocalizedContent("夏のテーマ", "夏の作品を募集します"),
                LanguageCodeValue.EN, new LocalizedContent("Summer Theme", "Submit your summer works")
            ),
            "PHOTO",
            pastDate
        );

        // テーマの存在をモックして ResourceNotFoundException を回避
        Theme existingTheme = Theme.reconstruct(
            new ThemeId(themeId),
            new ThemeType(ThemeTypeValue.PHOTO),
            new ThemeStartDate(LocalDate.now().plusDays(1)),
            java.time.LocalDateTime.now().minusDays(1),
            java.time.LocalDateTime.now().minusDays(1)
        );
        // テーマの存在をモックして ResourceNotFoundException を回避
        Map<LanguageCode, ThemeLocalization> localizations = Map.of(
            new LanguageCode(LanguageCodeValue.JA), ThemeLocalization.reconstruct(
                new ThemeLocalizationId(UUID.randomUUID()),
                new ThemeId(themeId),
                new LanguageCode(LanguageCodeValue.JA),
                new ThemeTitle("既存のタイトル"),
                new ThemeDescription("既存の説明"),
                java.time.LocalDateTime.now().minusDays(1),
                java.time.LocalDateTime.now().minusDays(1)
            )
        );
        Pair<Theme, Map<LanguageCode, ThemeLocalization>> integratedTheme = Pair.of(existingTheme, localizations);
        
        when(themeQueryService.getIntegratedMultiLangById(any()))
            .thenReturn(Optional.of(integratedTheme));

        when(themeLocalizationValidator.validateLocalizations(eq(command.localizations()), any()))
            .thenReturn(Map.of());


        // Act & Assert
        InvalidDomainsException exception = assertThrows(InvalidDomainsException.class, () -> updateThemeInteractor.handle(command));

        // バリデーションエラーの詳細を確認
        assertEquals(1, exception.getExceptions().size());
        DomainException validationError = exception.getExceptions().get(0);
        assertEquals("startDate", validationError.getField());
        assertEquals("Domain Error", validationError.getMessage());

        // ローカライゼーションサービスは呼び出されないことを確認
        verify(themeLocalizationCommandService, never()).update(any(ThemeLocalization.class));
    }
}
