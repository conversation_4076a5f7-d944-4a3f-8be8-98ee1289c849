package com.favick.application.usecase;

import com.favick.application.dto.query.LoadAdminQueryDto;
import com.favick.application.dto.result.AdminDetailsResultDto;
import com.favick.application.service.admin.query.AdminQueryService;
import com.favick.application.service.role.query.RoleQueryService;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.domain.feature.admin.model.*;
import com.favick.domain.feature.role.model.Role;
import com.favick.domain.feature.role.model.RoleId;
import com.favick.domain.feature.role.model.RoleName;
import com.favick.domain.feature.role.model.RoleNameValue;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("LoadAdminInteractor のテスト")
class LoadAdminInteractorTest {

    @Mock
    private AdminQueryService adminQueryService;

    @Mock
    private RoleQueryService roleQueryService;

    @InjectMocks
    private LoadAdminInteractor loadAdminInteractor;

    @Test
    @DisplayName("正常系：有効なメールアドレスで管理者詳細が取得される")
    void handle_validEmail_shouldReturnAdminDetailsDto() {
        // Arrange
        String email = "<EMAIL>";
        LoadAdminQueryDto query = new LoadAdminQueryDto(email);

        UUID adminId = UUID.randomUUID();
        UUID roleId = UUID.randomUUID();
        
        Admin admin = Admin.reconstruct(
            new AdminId(adminId),
            new RoleId(roleId),
            new AdminName("AdminUser"),
            new AdminEmail(email),
            new AdminPassword("hashedPassword"),
            LocalDateTime.now().minusDays(1),
            LocalDateTime.now().minusDays(1)
        );

        Role role = Role.reconstruct(
            new RoleId(roleId),
            new RoleName(RoleNameValue.ROLE_ADMIN),
            LocalDateTime.now().minusDays(1),
            LocalDateTime.now().minusDays(1)
        );

        when(adminQueryService.getByEmail(any(AdminEmail.class)))
            .thenReturn(Optional.of(admin));
        when(roleQueryService.getById(any(RoleId.class)))
            .thenReturn(Optional.of(role));

        // Act
        AdminDetailsResultDto result = loadAdminInteractor.handle(query);

        // Assert
        assertEquals(adminId, result.id());
        assertEquals(roleId, result.roleId());
        assertEquals("AdminUser", result.name());
        assertEquals(RoleNameValue.ROLE_ADMIN, result.roleName());
        assertEquals(email, result.email());
        assertEquals("hashedPassword", result.password());

        verify(adminQueryService).getByEmail(any(AdminEmail.class));
        verify(roleQueryService).getById(any(RoleId.class));
    }

    @Test
    @DisplayName("異常系：存在しないメールアドレスの場合はResourceNotFoundExceptionが発生する")
    void handle_nonExistingEmail_shouldThrowException() {
        // Arrange
        String nonExistingEmail = "<EMAIL>";
        LoadAdminQueryDto query = new LoadAdminQueryDto(nonExistingEmail);

        when(adminQueryService.getByEmail(any(AdminEmail.class)))
            .thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> loadAdminInteractor.handle(query));

        verify(adminQueryService).getByEmail(any(AdminEmail.class));
        verify(roleQueryService, never()).getById(any(RoleId.class));
    }

    @Test
    @DisplayName("異常系：無効なロールIDの場合はResourceNotFoundExceptionが発生する")
    void handle_invalidRoleId_shouldThrowException() {
        // Arrange
        String email = "<EMAIL>";
        LoadAdminQueryDto query = new LoadAdminQueryDto(email);

        UUID adminId = UUID.randomUUID();
        UUID invalidRoleId = UUID.randomUUID();
        
        Admin admin = Admin.reconstruct(
            new AdminId(adminId),
            new RoleId(invalidRoleId),
            new AdminName("AdminUser"),
            new AdminEmail(email),
            new AdminPassword("hashedPassword"),
            LocalDateTime.now().minusDays(1),
            LocalDateTime.now().minusDays(1)
        );

        when(adminQueryService.getByEmail(any(AdminEmail.class)))
            .thenReturn(Optional.of(admin));
        when(roleQueryService.getById(any(RoleId.class)))
            .thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> loadAdminInteractor.handle(query));

        verify(adminQueryService).getByEmail(any(AdminEmail.class));
        verify(roleQueryService).getById(any(RoleId.class));
    }

    @Test
    @DisplayName("異常系：無効なメールアドレス形式の場合はバリデーションエラーが発生する")
    void handle_invalidEmail_shouldThrowValidationError() {
        // Arrange
        String invalidEmail = "invalid-email";
        LoadAdminQueryDto query = new LoadAdminQueryDto(invalidEmail);

        // Act & Assert
        assertThrows(InvalidDomainsException.class, () -> loadAdminInteractor.handle(query));

        verify(adminQueryService, never()).getByEmail(any(AdminEmail.class));
        verify(roleQueryService, never()).getById(any(RoleId.class));
    }
}