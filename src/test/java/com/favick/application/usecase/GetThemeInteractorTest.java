package com.favick.application.usecase;

import com.favick.application.dto.query.GetThemeQueryDto;
import com.favick.application.dto.result.ThemeDetailResultDto;
import com.favick.application.service.theme.query.ThemeQueryService;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import com.favick.domain.feature.theme.model.*;
import com.favick.domain.feature.theme_localization.model.ThemeDescription;
import com.favick.domain.feature.theme_localization.model.ThemeLocalization;
import com.favick.domain.feature.theme_localization.model.ThemeLocalizationId;
import com.favick.domain.feature.theme_localization.model.ThemeTitle;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import org.springframework.data.util.Pair;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@DisplayName("GetThemeInteractor のテスト")
class GetThemeInteractorTest {

    @Mock
    private ThemeQueryService themeQueryService;


    @InjectMocks
    private GetThemeInteractor getThemeInteractor;

    @Test
    @DisplayName("正常系：存在するテーマIDを指定すると詳細情報を取得できる")
    void handle_existingThemeId_shouldReturnThemeDetails() {
        // Arrange
        UUID themeId = UUID.randomUUID();
        UUID localizationId = UUID.randomUUID();
        LanguageCodeValue languageCode = LanguageCodeValue.JA;
        LocalDate startDate = LocalDate.now().plusMonths(1).withDayOfMonth(1);
        LocalDateTime now = LocalDateTime.now();

        GetThemeQueryDto query = new GetThemeQueryDto(themeId, languageCode);

        Theme theme = Theme.reconstruct(
            new ThemeId(themeId),
            new ThemeType(ThemeTypeValue.PHOTO),
            new ThemeStartDate(startDate),
            now,
            now
        );

        ThemeLocalization localization = ThemeLocalization.reconstruct(
            new ThemeLocalizationId(localizationId),
            new ThemeId(themeId),
            new LanguageCode(languageCode),
            new ThemeTitle("テストタイトル"),
            new ThemeDescription("テスト説明文"),
            now,
            now
        );

        Pair<Theme, ThemeLocalization> integratedTheme = Pair.of(theme, localization);
        when(themeQueryService.getIntegratedByIdAndLanguageCode(
            new ThemeId(themeId), new LanguageCode(languageCode)))
            .thenReturn(Optional.of(integratedTheme));

        // Act
        ThemeDetailResultDto result = getThemeInteractor.handle(query);

        // Assert
        assertEquals(themeId, result.id());
        assertEquals(ThemeTypeValue.PHOTO, result.type());
        assertEquals(startDate, result.startDate());
        assertEquals("テストタイトル", result.title());
        assertEquals("テスト説明文", result.description());
        assertEquals(now, result.createdAt());
        assertEquals(now, result.updatedAt());
        verify(themeQueryService).getIntegratedByIdAndLanguageCode(
            new ThemeId(themeId), new LanguageCode(languageCode));
    }

    @Test
    @DisplayName("異常系：存在しないテーマIDを指定すると例外が発生する")
    void handle_nonExistingThemeId_shouldThrowException() {
        // Arrange
        UUID nonExistingId = UUID.randomUUID();
        LanguageCodeValue languageCode = LanguageCodeValue.JA;
        GetThemeQueryDto query = new GetThemeQueryDto(nonExistingId, languageCode);

        when(themeQueryService.getIntegratedByIdAndLanguageCode(
            new ThemeId(nonExistingId), new LanguageCode(languageCode)))
            .thenReturn(Optional.empty());

        // Act & Assert
        Exception exception = assertThrows(ResourceNotFoundException.class, () -> getThemeInteractor.handle(query));

        assertEquals("ResourceNotFound", exception.getMessage());
        verify(themeQueryService).getIntegratedByIdAndLanguageCode(
            new ThemeId(nonExistingId), new LanguageCode(languageCode));
    }
}
