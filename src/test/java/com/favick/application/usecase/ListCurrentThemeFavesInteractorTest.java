package com.favick.application.usecase;

import com.favick.application.dto.query.ListCurrentThemeFavesQueryDto;
import com.favick.application.dto.result.CurrentThemeFavesResultDto;
import com.favick.application.service.fave.query.FaveQueryService;
import com.favick.application.service.theme.query.ThemeQueryService;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.domain.feature.fave.model.Fave;
import com.favick.domain.feature.fave.model.FaveContent;
import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import com.favick.domain.feature.theme.model.*;
import com.favick.domain.feature.theme_localization.model.ThemeDescription;
import com.favick.domain.feature.theme_localization.model.ThemeLocalization;
import com.favick.domain.feature.theme_localization.model.ThemeLocalizationId;
import com.favick.domain.feature.theme_localization.model.ThemeTitle;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.data.util.Pair;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("ListCurrentThemeFavesInteractor のテスト")
class ListCurrentThemeFavesInteractorTest {

    @Mock
    private ThemeQueryService themeQueryService;


    @Mock
    private FaveQueryService faveQueryService;

    @InjectMocks
    private ListCurrentThemeFavesInteractor listCurrentThemeFavesInteractor;

    @Test
    @DisplayName("正常系：開催中のテーマとお気に入り一覧を正常に取得できる")
    void handle_withValidQuery_shouldReturnCurrentThemeFaves() {
        // Arrange
        LanguageCodeValue languageCode = LanguageCodeValue.JA;
        ListCurrentThemeFavesQueryDto query = new ListCurrentThemeFavesQueryDto(languageCode, Optional.empty());

        UUID themeId = UUID.randomUUID();
        UUID faveId1 = UUID.randomUUID();
        UUID faveId2 = UUID.randomUUID();
        UUID userId1 = UUID.randomUUID();
        UUID userId2 = UUID.randomUUID();
        LocalDateTime now = LocalDateTime.now();
        LocalDate startDate = LocalDate.now();

        Theme theme = Theme.reconstruct(
            new ThemeId(themeId),
            new ThemeType(ThemeTypeValue.PHOTO),
            new ThemeStartDate(startDate),
            now, now
        );

        ThemeLocalization themeLocalization = ThemeLocalization.reconstruct(
            new ThemeLocalizationId(UUID.randomUUID()),
            new ThemeId(themeId),
            new LanguageCode(LanguageCodeValue.JA),
            new ThemeTitle("テストタイトル"),
            new ThemeDescription("テスト説明"),
            now, now
        );

        Fave fave1 = Fave.reconstruct(
            new FaveId(faveId1),
            new UserId(userId1),
            new ThemeId(themeId),
            new FaveContent("お気に入りコンテンツ1"),
            now, now
        );

        Fave fave2 = Fave.reconstruct(
            new FaveId(faveId2),
            new UserId(userId2),
            new ThemeId(themeId),
            new FaveContent("お気に入りコンテンツ2"),
            now, now
        );

        List<Fave> faves = List.of(fave1, fave2);

        Pair<Theme, ThemeLocalization> integratedTheme = Pair.of(theme, themeLocalization);
        
        when(themeQueryService.getCurrentIntegratedByLanguageCode(
            new LanguageCode(LanguageCodeValue.JA))
        ).thenReturn(Optional.of(integratedTheme));
        when(faveQueryService.listApprovedByThemeId(theme.getId()))
            .thenReturn(faves);

        // Act
        CurrentThemeFavesResultDto result = listCurrentThemeFavesInteractor.handle(query);

        // Assert
        assertNotNull(result);
        assertNotNull(result.theme());
        assertEquals(themeId, result.theme().id());
        assertEquals("テストタイトル", result.theme().title());
        assertEquals("テスト説明", result.theme().description());
        assertEquals(ThemeTypeValue.PHOTO, result.theme().type());
        assertEquals(startDate, result.theme().startDate());

        assertNotNull(result.faves());
        assertEquals(2, result.faves().size());

        assertEquals(faveId1, result.faves().get(0).id());
        assertEquals("お気に入りコンテンツ1", result.faves().get(0).content());

        assertEquals(faveId2, result.faves().get(1).id());
        assertEquals("お気に入りコンテンツ2", result.faves().get(1).content());

        verify(themeQueryService).getCurrentIntegratedByLanguageCode(
            new LanguageCode(LanguageCodeValue.JA));
        verify(faveQueryService).listApprovedByThemeId(theme.getId());
    }

    @Test
    @DisplayName("正常系：お気に入りが存在しない場合でも正常に処理される")
    void handle_withNoFaves_shouldReturnCurrentThemeWithEmptyFaves() {
        // Arrange
        LanguageCodeValue languageCode = LanguageCodeValue.JA;
        ListCurrentThemeFavesQueryDto query = new ListCurrentThemeFavesQueryDto(languageCode, Optional.empty());

        UUID themeId = UUID.randomUUID();
        LocalDateTime now = LocalDateTime.now();
        LocalDate startDate = LocalDate.now();

        Theme theme = Theme.reconstruct(
            new ThemeId(themeId),
            new ThemeType(ThemeTypeValue.PHOTO),
            new ThemeStartDate(startDate),
            now, now
        );

        ThemeLocalization themeLocalization = ThemeLocalization.reconstruct(
            new ThemeLocalizationId(UUID.randomUUID()),
            new ThemeId(themeId),
            new LanguageCode(LanguageCodeValue.JA),
            new ThemeTitle("テストタイトル"),
            new ThemeDescription("テスト説明"),
            now, now
        );

        Pair<Theme, ThemeLocalization> integratedTheme = Pair.of(theme, themeLocalization);
        
        when(themeQueryService.getCurrentIntegratedByLanguageCode(
            new LanguageCode(LanguageCodeValue.JA))
        ).thenReturn(Optional.of(integratedTheme));
        when(faveQueryService.listApprovedByThemeId(theme.getId()))
            .thenReturn(List.of());

        // Act
        CurrentThemeFavesResultDto result = listCurrentThemeFavesInteractor.handle(query);

        // Assert
        assertNotNull(result);
        assertNotNull(result.theme());
        assertEquals(themeId, result.theme().id());
        assertNotNull(result.faves());
        assertTrue(result.faves().isEmpty());

        verify(themeQueryService).getCurrentIntegratedByLanguageCode(
            new LanguageCode(LanguageCodeValue.JA));
        verify(faveQueryService).listApprovedByThemeId(theme.getId());
    }

    @Test
    @DisplayName("異常系：開催中のテーマが存在しない場合は例外が発生する")
    void handle_withNoCurrentTheme_shouldThrowResourceNotFoundException() {
        // Arrange
        LanguageCodeValue languageCode = LanguageCodeValue.JA;
        ListCurrentThemeFavesQueryDto query = new ListCurrentThemeFavesQueryDto(languageCode, Optional.empty());

        when(themeQueryService.getCurrentIntegratedByLanguageCode(
            new LanguageCode(LanguageCodeValue.JA))
        ).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> listCurrentThemeFavesInteractor.handle(query));

        verify(themeQueryService).getCurrentIntegratedByLanguageCode(
            new LanguageCode(LanguageCodeValue.JA));
        verify(faveQueryService, never()).listApprovedByThemeId(any());
    }

}
