package com.favick.application.usecase;

import com.favick.application.dto.command.PickFaveCommandDto;
import com.favick.application.service.fave.command.FaveCommandService;
import com.favick.application.service.fave.query.FaveQueryService;
import com.favick.application.service.review.command.ReviewCommandService;
import com.favick.application.service.storage.FileStorageService;
import com.favick.common.exception.FileStorageException;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.domain.exception.EntityException;
import com.favick.domain.feature.fave.model.Fave;
import com.favick.domain.feature.fave.model.FaveContent;
import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.review.model.Review;
import com.favick.domain.feature.review.model.ReviewId;
import com.favick.domain.feature.review.model.ReviewStatus;
import com.favick.domain.feature.review.model.ReviewStatusValue;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.theme.model.ThemeType;
import com.favick.domain.feature.theme.model.ThemeTypeValue;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.util.Pair;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("PickFaveInteractor のテスト")
class PickFaveInteractorTest {

    @Mock
    private FaveQueryService faveQueryService;

    @Mock
    private FaveCommandService faveCommandService;

    @Mock
    private ReviewCommandService reviewCommandService;

    @Mock
    private FileStorageService fileStorageService;

    @InjectMocks
    private PickFaveInteractor pickFaveInteractor;

    @Test
    @DisplayName("正常系：新規お気に入りを正常に作成できる")
    void handle_withNewFave_shouldCreateFave() throws IOException {
        // Arrange
        UUID userId = UUID.randomUUID();
        UUID themeId = UUID.randomUUID();
        ThemeTypeValue themeType = ThemeTypeValue.PHOTO;
        MultipartFile file = new MockMultipartFile("file", "test.jpg", "image/jpeg", "test content".getBytes());
        String filePath = "/uploads/test.jpg";

        PickFaveCommandDto command = new PickFaveCommandDto(userId, themeId, themeType, file);

        when(faveQueryService.getIntegratedByUserIdAndThemeId(any(UserId.class), any(ThemeId.class)))
            .thenReturn(Optional.empty());
        when(fileStorageService.store(eq(file), any(ThemeType.class)))
            .thenReturn(filePath);

        // Act
        pickFaveInteractor.handle(command);

        // Assert
        verify(faveQueryService).getIntegratedByUserIdAndThemeId(any(UserId.class), any(ThemeId.class));
        verify(fileStorageService).store(eq(file), any(ThemeType.class));
        verify(faveCommandService).create(any(Fave.class));
        verify(reviewCommandService).create(any(Review.class));
        verify(faveCommandService, never()).update(any());
    }

    @Test
    @DisplayName("正常系：拒否されたお気に入りを置き換えできる")
    void handle_withRejectedFave_shouldReplaceFave() throws IOException {
        // Arrange
        UUID userId = UUID.randomUUID();
        UUID themeId = UUID.randomUUID();
        ThemeTypeValue themeType = ThemeTypeValue.PHOTO;
        MultipartFile file = new MockMultipartFile("file", "test.jpg", "image/jpeg", "test content".getBytes());
        String oldFilePath = "/uploads/old.jpg";
        String newFilePath = "/uploads/new.jpg";
        LocalDateTime now = LocalDateTime.now();

        UUID oldFaveId = UUID.randomUUID();
        Fave oldFave = Fave.reconstruct(
            new FaveId(oldFaveId),
            new UserId(userId),
            new ThemeId(themeId),
            new FaveContent(oldFilePath),
            now, now
        );

        Review rejectedReview = Review.reconstruct(
            new ReviewId(UUID.randomUUID()),
            new FaveId(oldFaveId),
            new ReviewStatus(ReviewStatusValue.REJECTED),
            now, now
        );

        Pair<Fave, Review> oldIntegratedFave = Pair.of(oldFave, rejectedReview);

        PickFaveCommandDto command = new PickFaveCommandDto(userId, themeId, themeType, file);

        when(faveQueryService.getIntegratedByUserIdAndThemeId(any(UserId.class), any(ThemeId.class)))
            .thenReturn(Optional.of(oldIntegratedFave));
        when(fileStorageService.store(eq(file), any(ThemeType.class)))
            .thenReturn(newFilePath);

        // Act
        pickFaveInteractor.handle(command);

        // Assert
        verify(faveQueryService).getIntegratedByUserIdAndThemeId(any(UserId.class), any(ThemeId.class));
        verify(fileStorageService).store(eq(file), any(ThemeType.class));
        verify(fileStorageService).delete(oldFilePath);
        verify(faveCommandService).update(any(Fave.class));
        verify(reviewCommandService).update(any(Review.class));
        verify(faveCommandService, never()).create(any());
    }

    @Test
    @DisplayName("異常系：承認済みのお気に入りが存在する場合は例外が発生する")
    void handle_withApprovedFave_shouldThrowEntityException() throws IOException {
        // Arrange
        UUID userId = UUID.randomUUID();
        UUID themeId = UUID.randomUUID();
        ThemeTypeValue themeType = ThemeTypeValue.PHOTO;
        MultipartFile file = new MockMultipartFile("file", "test.jpg", "image/jpeg", "test content".getBytes());
        LocalDateTime now = LocalDateTime.now();

        UUID existingFaveId = UUID.randomUUID();
        Fave existingFave = Fave.reconstruct(
            new FaveId(existingFaveId),
            new UserId(userId),
            new ThemeId(themeId),
            new FaveContent("/uploads/existing.jpg"),
            now, now
        );

        Review approvedReview = Review.reconstruct(
            new ReviewId(UUID.randomUUID()),
            new FaveId(existingFaveId),
            new ReviewStatus(ReviewStatusValue.APPROVED),
            now, now
        );

        Pair<Fave, Review> approvedIntegratedFave = Pair.of(existingFave, approvedReview);

        PickFaveCommandDto command = new PickFaveCommandDto(userId, themeId, themeType, file);

        when(faveQueryService.getIntegratedByUserIdAndThemeId(any(UserId.class), any(ThemeId.class)))
            .thenReturn(Optional.of(approvedIntegratedFave));

        // Act & Assert
        assertThrows(EntityException.class, () -> pickFaveInteractor.handle(command));

        verify(faveQueryService).getIntegratedByUserIdAndThemeId(any(UserId.class), any(ThemeId.class));
        verify(fileStorageService, never()).store(any(), any());
        verify(faveCommandService, never()).create(any());
        verify(faveCommandService, never()).update(any());
    }

    @Test
    @DisplayName("異常系：審査中のお気に入りが存在する場合は例外が発生する")
    void handle_withPendingFave_shouldThrowEntityException() throws IOException {
        // Arrange
        UUID userId = UUID.randomUUID();
        UUID themeId = UUID.randomUUID();
        ThemeTypeValue themeType = ThemeTypeValue.PHOTO;
        MultipartFile file = new MockMultipartFile("file", "test.jpg", "image/jpeg", "test content".getBytes());
        LocalDateTime now = LocalDateTime.now();

        UUID existingFaveId = UUID.randomUUID();
        Fave existingFave = Fave.reconstruct(
            new FaveId(existingFaveId),
            new UserId(userId),
            new ThemeId(themeId),
            new FaveContent("/uploads/existing.jpg"),
            now, now
        );

        Review pendingReview = Review.reconstruct(
            new ReviewId(UUID.randomUUID()),
            new FaveId(existingFaveId),
            new ReviewStatus(ReviewStatusValue.PENDING),
            now, now
        );

        Pair<Fave, Review> pendingIntegratedFave = Pair.of(existingFave, pendingReview);

        PickFaveCommandDto command = new PickFaveCommandDto(userId, themeId, themeType, file);

        when(faveQueryService.getIntegratedByUserIdAndThemeId(any(UserId.class), any(ThemeId.class)))
            .thenReturn(Optional.of(pendingIntegratedFave));

        // Act & Assert
        assertThrows(EntityException.class, () -> pickFaveInteractor.handle(command));

        verify(faveQueryService).getIntegratedByUserIdAndThemeId(any(UserId.class), any(ThemeId.class));
        verify(fileStorageService, never()).store(any(), any());
        verify(faveCommandService, never()).create(any());
        verify(faveCommandService, never()).update(any());
    }

    @Test
    @DisplayName("異常系：ファイル保存に失敗した場合は例外が発生する")
    void handle_withFileStorageFailure_shouldThrowFileStorageException() throws IOException {
        // Arrange
        UUID userId = UUID.randomUUID();
        UUID themeId = UUID.randomUUID();
        ThemeTypeValue themeType = ThemeTypeValue.PHOTO;
        MultipartFile file = new MockMultipartFile("file", "test.jpg", "image/jpeg", "test content".getBytes());

        PickFaveCommandDto command = new PickFaveCommandDto(userId, themeId, themeType, file);

        when(faveQueryService.getIntegratedByUserIdAndThemeId(any(UserId.class), any(ThemeId.class)))
            .thenReturn(Optional.empty());
        when(fileStorageService.store(eq(file), any(ThemeType.class)))
            .thenThrow(new RuntimeException("Storage failed"));

        // Act & Assert
        assertThrows(FileStorageException.class, () -> pickFaveInteractor.handle(command));

        verify(faveQueryService).getIntegratedByUserIdAndThemeId(any(UserId.class), any(ThemeId.class));
        verify(fileStorageService).store(eq(file), any(ThemeType.class));
        verify(faveCommandService, never()).create(any());
        verify(faveCommandService, never()).update(any());
    }

    @Test
    @DisplayName("異常系：無効なユーザーID形式の場合はバリデーションエラーが発生する")
    void handle_invalidUserId_shouldThrowValidationError() throws IOException {
        // Arrange - Using null to cause UserId validation to fail
        MultipartFile file = new MockMultipartFile("file", "test.jpg", "image/jpeg", "test content".getBytes());
        PickFaveCommandDto command = new PickFaveCommandDto(null, UUID.randomUUID(), ThemeTypeValue.PHOTO, file);

        when(fileStorageService.store(eq(file), any(ThemeType.class)))
            .thenReturn("/uploads/test.jpg");

        // Act & Assert
        assertThrows(InvalidDomainsException.class, () -> pickFaveInteractor.handle(command));

        // バリデーションエラーがあってもファイル保存は実行される
        verify(fileStorageService).store(eq(file), any(ThemeType.class));
        verify(faveCommandService, never()).create(any());
        verify(faveCommandService, never()).update(any());
    }
}
