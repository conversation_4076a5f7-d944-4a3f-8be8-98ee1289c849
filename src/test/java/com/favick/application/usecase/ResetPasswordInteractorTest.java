package com.favick.application.usecase;

import com.favick.application.dto.command.ResetPasswordCommandDto;
import com.favick.application.service.token.command.PasswordResetTokenCommandService;
import com.favick.application.service.token.query.PasswordResetTokenQueryService;
import com.favick.application.service.user.command.UserCommandService;
import com.favick.application.service.user.query.UserQueryService;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.domain.exception.DomainException;
import com.favick.domain.feature.rank.model.RankId;
import com.favick.domain.feature.token.model.PasswordResetToken;
import com.favick.domain.feature.token.model.PasswordResetTokenId;
import com.favick.domain.feature.token.model.TokenExpireAt;
import com.favick.domain.feature.token.model.TokenValue;
import com.favick.domain.feature.user.model.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("ResetPasswordInteractor のテスト")
class ResetPasswordInteractorTest {

    @Mock
    private PasswordResetTokenQueryService passwordResetTokenQueryService;

    @Mock
    private PasswordResetTokenCommandService passwordResetTokenCommandService;

    @Mock
    private UserQueryService userQueryService;

    @Mock
    private UserCommandService userCommandService;

    @Mock
    private PasswordEncoder passwordEncoder;

    @InjectMocks
    private ResetPasswordInteractor resetPasswordInteractor;

    @Test
    @DisplayName("正常系：有効なトークンとパスワードの場合、パスワードがリセットされる")
    void handle_validToken_shouldDeleteTokenAfterPasswordUpdate() {
        // Arrange
        String token = "valid-token";
        String newPassword = "NewPassword123";
        UUID userId = UUID.randomUUID();
        UUID tokenId = UUID.randomUUID();

        ResetPasswordCommandDto commandDto = new ResetPasswordCommandDto(newPassword, token);

        PasswordResetToken passwordResetToken = PasswordResetToken.reconstruct(
            new PasswordResetTokenId(tokenId),
            new UserId(userId),
            new TokenValue(token),
            new TokenExpireAt(LocalDateTime.now().plusHours(1)), // 有効期限内
            LocalDateTime.now().minusHours(1)
        );

        User user = User.reconstruct(
            new UserId(userId),
            new RankId(UUID.randomUUID()),
            new UserName("TestUser"),
            new UserEmail("<EMAIL>"),
            new UserPassword("password"),
            new UserEnabled(true),
            null,
            LocalDateTime.now().minusHours(1), LocalDateTime.now().minusHours(1)
        );

        when(passwordResetTokenQueryService.getByToken(any(TokenValue.class)))
            .thenReturn(Optional.of(passwordResetToken));
        when(userQueryService.getById(any(UserId.class)))
            .thenReturn(Optional.of(user));
        when(passwordEncoder.encode(newPassword))
            .thenReturn("encodedPassword");

        // Act
        resetPasswordInteractor.handle(commandDto);

        // Assert
        verify(passwordResetTokenQueryService)
            .getByToken(any(TokenValue.class));
        verify(userQueryService)
            .getById(any(UserId.class));
        verify(passwordEncoder)
            .encode(newPassword);
        verify(userCommandService)
            .updatePassword(eq(user), any(UserPassword.class));
        verify(passwordResetTokenCommandService)
            .delete(passwordResetToken);
    }

    @Test
    @DisplayName("異常系：存在しないトークンの場合は例外が発生する")
    void handle_nonExistingToken_shouldThrowResourceNotFoundException() {
        // Arrange
        String nonExistingToken = "non-existing-token";
        String newPassword = "NewPassword123";

        ResetPasswordCommandDto commandDto = new ResetPasswordCommandDto(newPassword, nonExistingToken);

        when(passwordResetTokenQueryService.getByToken(any(TokenValue.class)))
            .thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> resetPasswordInteractor.handle(commandDto));

        verify(passwordResetTokenQueryService)
            .getByToken(any(TokenValue.class));
        verify(userCommandService, never()).updatePassword(any(User.class), any(UserPassword.class));
        verify(passwordResetTokenCommandService, never()).delete(any(PasswordResetToken.class));
    }

    @Test
    @DisplayName("異常系：期限切れトークンの場合は例外が発生する")
    void handle_expiredToken_shouldThrowDomainException() {
        // Arrange
        String expiredToken = "expired-token";
        String newPassword = "NewPassword123";
        UUID userId = UUID.randomUUID();
        UUID tokenId = UUID.randomUUID();

        ResetPasswordCommandDto commandDto = new ResetPasswordCommandDto(newPassword, expiredToken);

        PasswordResetToken expiredPasswordResetToken = PasswordResetToken.reconstruct(
            new PasswordResetTokenId(tokenId),
            new UserId(userId),
            new TokenValue(expiredToken),
            new TokenExpireAt(LocalDateTime.now().minusHours(1)), // 期限切れ
            LocalDateTime.now().minusHours(2)
        );

        when(passwordResetTokenQueryService.getByToken(any(TokenValue.class)))
            .thenReturn(Optional.of(expiredPasswordResetToken));

        // Act & Assert
        assertThrows(DomainException.class, () -> resetPasswordInteractor.handle(commandDto));

        verify(passwordResetTokenQueryService)
            .getByToken(any(TokenValue.class));
        verify(userCommandService, never()).updatePassword(any(User.class), any(UserPassword.class));
        verify(passwordResetTokenCommandService, never()).delete(any(PasswordResetToken.class));
    }

    @Test
    @DisplayName("異常系：無効なパスワード形式の場合はバリデーションエラーが発生する")
    void handle_invalidPassword_shouldThrowValidationError() {
        // Arrange
        String token = "valid-token";
        String invalidPassword = "123"; // 短すぎるパスワード

        ResetPasswordCommandDto commandDto = new ResetPasswordCommandDto(invalidPassword, token);

        // Act & Assert
        assertThrows(InvalidDomainsException.class, () -> resetPasswordInteractor.handle(commandDto));

        verify(passwordResetTokenQueryService, never()).getByToken(any(TokenValue.class));
        verify(userCommandService, never()).updatePassword(any(User.class), any(UserPassword.class));
        verify(passwordResetTokenCommandService, never()).delete(any(PasswordResetToken.class));
    }

    @Test
    @DisplayName("異常系：無効なトークン形式の場合はバリデーションエラーが発生する")
    void handle_invalidToken_shouldThrowValidationError() {
        // Arrange
        String invalidToken = ""; // 空のトークン
        String password = "ValidPassword123";

        ResetPasswordCommandDto commandDto = new ResetPasswordCommandDto(password, invalidToken);

        // Act & Assert
        assertThrows(InvalidDomainsException.class, () -> resetPasswordInteractor.handle(commandDto));

        verify(passwordResetTokenQueryService, never()).getByToken(any(TokenValue.class));
        verify(userCommandService, never()).updatePassword(any(User.class), any(UserPassword.class));
        verify(passwordResetTokenCommandService, never()).delete(any(PasswordResetToken.class));
    }
}