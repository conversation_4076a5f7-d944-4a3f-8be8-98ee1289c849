package com.favick.application.usecase;

import com.favick.application.dto.command.DeleteThemeCommandDto;
import com.favick.application.service.theme.command.ThemeCommandService;
import com.favick.application.service.theme.query.ThemeQueryService;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.domain.feature.theme.model.Theme;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.theme.model.ThemeStartDate;
import com.favick.domain.feature.theme.model.ThemeType;
import com.favick.domain.feature.theme.model.ThemeTypeValue;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("DeleteThemeInteractor のテスト")
class DeleteThemeInteractorTest {

    @Mock
    private ThemeCommandService themeCommandService;

    @Mock
    private ThemeQueryService themeQueryService;

    @InjectMocks
    private DeleteThemeInteractor deleteThemeInteractor;

    @Test
    @DisplayName("正常系：テーマ削除コマンドが正しく処理される")
    void handle_validCommand_shouldCallServiceDelete() {
        // Arrange
        UUID themeId = UUID.randomUUID();
        DeleteThemeCommandDto command = new DeleteThemeCommandDto(themeId);

        Theme existingTheme = Theme.reconstruct(
            new ThemeId(themeId),
            new ThemeType(ThemeTypeValue.PHOTO),
            new ThemeStartDate(LocalDate.now().plusDays(1)),
            LocalDateTime.now().minusDays(1),
            LocalDateTime.now().minusDays(1)
        );

        when(themeQueryService.getById(any(ThemeId.class)))
            .thenReturn(Optional.of(existingTheme));
        doNothing().when(themeCommandService).delete(any(Theme.class));

        // Act
        deleteThemeInteractor.handle(command);

        // Assert
        verify(themeQueryService).getById(any(ThemeId.class));
        verify(themeCommandService).delete(existingTheme);
    }

    @Test
    @DisplayName("異常系：存在しないテーマIDを指定した場合、ResourceNotFoundExceptionが発生する")
    void handle_nonExistingThemeId_shouldThrowResourceNotFoundException() {
        // Arrange
        UUID nonExistingId = UUID.randomUUID();
        DeleteThemeCommandDto command = new DeleteThemeCommandDto(nonExistingId);

        when(themeQueryService.getById(any(ThemeId.class)))
            .thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> deleteThemeInteractor.handle(command));

        verify(themeQueryService).getById(any(ThemeId.class));
        verify(themeCommandService, never()).delete(any(Theme.class));
    }

    @Test
    @DisplayName("異常系：無効なテーマIDを指定した場合、バリデーションエラーが発生する")
    void handle_invalidThemeId_shouldThrowValidationError() {
        // Arrange - UUIDとしては無効だが、nullを使用してバリデーションエラーを発生させる
        DeleteThemeCommandDto command = new DeleteThemeCommandDto(null);

        // Act & Assert
        assertThrows(InvalidDomainsException.class, () -> deleteThemeInteractor.handle(command));

        verify(themeQueryService, never()).getById(any(ThemeId.class));
        verify(themeCommandService, never()).delete(any(Theme.class));
    }
}