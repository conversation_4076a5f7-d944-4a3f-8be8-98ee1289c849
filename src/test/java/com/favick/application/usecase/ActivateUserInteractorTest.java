package com.favick.application.usecase;

import com.favick.application.dto.query.ActivateUserQueryDto;
import com.favick.application.service.token.command.VerificationTokenCommandService;
import com.favick.application.service.token.query.VerificationTokenQueryService;
import com.favick.application.service.user.command.UserCommandService;
import com.favick.application.service.user.query.UserQueryService;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.domain.exception.DomainException;
import com.favick.domain.feature.rank.model.RankId;
import com.favick.domain.feature.token.model.TokenExpireAt;
import com.favick.domain.feature.token.model.TokenValue;
import com.favick.domain.feature.token.model.VerificationToken;
import com.favick.domain.feature.token.model.VerificationTokenId;
import com.favick.domain.feature.user.model.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("ActivateUserInteractor のテスト")
class ActivateUserInteractorTest {

    @Mock
    private VerificationTokenQueryService verificationTokenQueryService;

    @Mock
    private VerificationTokenCommandService verificationTokenCommandService;

    @Mock
    private UserQueryService userQueryService;

    @Mock
    private UserCommandService userCommandService;

    @InjectMocks
    private ActivateUserInteractor activateUserInteractor;

    @Test
    @DisplayName("正常系：有効なトークンでメール認証が成功する")
    void handle_validToken_shouldVerifyEmailSuccessfully() {
        // Arrange
        String tokenValue = "valid-verification-token";
        UUID userId = UUID.randomUUID();
        UUID tokenId = UUID.randomUUID();
        LocalDateTime now = LocalDateTime.now();

        ActivateUserQueryDto query = new ActivateUserQueryDto(tokenValue);

        VerificationToken verificationToken = VerificationToken.reconstruct(
            new VerificationTokenId(tokenId),
            new UserId(userId),
            new TokenValue(tokenValue),
            new TokenExpireAt(now.plusHours(24)),
            now.minusHours(1)
        );

        User user = User.reconstruct(
            new UserId(userId),
            new RankId(UUID.randomUUID()),
            new UserName("TestUser"),
            new UserEmail("<EMAIL>"),
            new UserPassword("password"),
            new UserEnabled(false), // 無効状態
            null,
            now.minusHours(1), now.minusHours(1)
        );

        when(verificationTokenQueryService.getByToken(any(TokenValue.class)))
            .thenReturn(Optional.of(verificationToken));
        when(userQueryService.getById(any(UserId.class)))
            .thenReturn(Optional.of(user));

        // Act
        activateUserInteractor.handle(query);

        // Assert
        verify(verificationTokenQueryService).getByToken(any(TokenValue.class));
        verify(userQueryService).getById(any(UserId.class));
        verify(userCommandService).enableUser(user);
        verify(verificationTokenCommandService).delete(verificationToken);
    }

    @Test
    @DisplayName("異常系：存在しないトークンの場合は例外が発生する")
    void handle_nonExistingToken_shouldThrowResourceNotFoundException() {
        // Arrange
        String nonExistingToken = "non-existing-token";
        ActivateUserQueryDto query = new ActivateUserQueryDto(nonExistingToken);

        when(verificationTokenQueryService.getByToken(any(TokenValue.class)))
            .thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> activateUserInteractor.handle(query));

        verify(verificationTokenQueryService).getByToken(any(TokenValue.class));
        verify(userCommandService, never()).enableUser(any(User.class));
        verify(verificationTokenCommandService, never()).delete(any(VerificationToken.class));
    }

    @Test
    @DisplayName("異常系：期限切れトークンの場合は例外が発生する")
    void handle_expiredToken_shouldThrowInvalidDomainException() {
        // Arrange
        String expiredToken = "expired-token";
        UUID userId = UUID.randomUUID();
        UUID tokenId = UUID.randomUUID();
        LocalDateTime now = LocalDateTime.now();
        ActivateUserQueryDto query = new ActivateUserQueryDto(expiredToken);

        VerificationToken expiredVerificationToken = VerificationToken.reconstruct(
            new VerificationTokenId(tokenId),
            new UserId(userId),
            new TokenValue(expiredToken),
            new TokenExpireAt(now.minusHours(1)), // 期限切れ
            now.minusHours(2)
        );

        when(verificationTokenQueryService.getByToken(any(TokenValue.class)))
            .thenReturn(Optional.of(expiredVerificationToken));

        // Act & Assert
        assertThrows(DomainException.class, () -> activateUserInteractor.handle(query));

        verify(verificationTokenQueryService).getByToken(any(TokenValue.class));
        verify(userCommandService, never()).enableUser(any(User.class));
        verify(verificationTokenCommandService, never()).delete(any(VerificationToken.class));
    }

    @Test
    @DisplayName("異常系：無効なトークン形式の場合はバリデーションエラーが発生する")
    void handle_invalidTokenFormat_shouldThrowValidationError() {
        // Arrange
        String invalidToken = ""; // 空文字列
        ActivateUserQueryDto query = new ActivateUserQueryDto(invalidToken);

        // Act & Assert
        assertThrows(InvalidDomainsException.class, () -> activateUserInteractor.handle(query));

        verify(verificationTokenQueryService, never()).getByToken(any(TokenValue.class));
        verify(userCommandService, never()).enableUser(any(User.class));
        verify(verificationTokenCommandService, never()).delete(any(VerificationToken.class));
    }

    @Test
    @DisplayName("正常系：ユーザー有効化後にトークンが削除される")
    void handle_validToken_shouldDeleteTokenAfterUserActivation() {
        // Arrange
        String tokenValue = "valid-verification-token";
        UUID userId = UUID.randomUUID();
        UUID tokenId = UUID.randomUUID();
        LocalDateTime now = LocalDateTime.now();

        ActivateUserQueryDto query = new ActivateUserQueryDto(tokenValue);

        VerificationToken verificationToken = VerificationToken.reconstruct(
            new VerificationTokenId(tokenId),
            new UserId(userId),
            new TokenValue(tokenValue),
            new TokenExpireAt(now.plusHours(24)),
            now.minusHours(1)
        );

        User user = User.reconstruct(
            new UserId(userId),
            new RankId(UUID.randomUUID()),
            new UserName("TestUser"),
            new UserEmail("<EMAIL>"),
            new UserPassword("password"),
            new UserEnabled(false), // 無効状態
            null,
            now.minusHours(1), now.minusHours(1)
        );

        when(verificationTokenQueryService.getByToken(any(TokenValue.class)))
            .thenReturn(Optional.of(verificationToken));
        when(userQueryService.getById(any(UserId.class)))
            .thenReturn(Optional.of(user));

        // Act
        activateUserInteractor.handle(query);

        // Assert - 処理順序を確認（ユーザー有効化 → トークン削除）
        var inOrder = inOrder(userCommandService, verificationTokenCommandService);
        inOrder.verify(userCommandService).enableUser(user);
        inOrder.verify(verificationTokenCommandService).delete(verificationToken);
    }
}