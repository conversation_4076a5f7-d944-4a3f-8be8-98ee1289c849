package com.favick.application.usecase;

import com.favick.application.dto.command.CreateUserCommandDto;
import com.favick.application.service.rank.query.RankQueryService;
import com.favick.application.service.user.command.UserCommandService;
import com.favick.application.service.user.event.UserRegisteredEventPublisher;
import com.favick.application.service.user.query.UserQueryService;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.domain.feature.rank.model.Rank;
import com.favick.domain.feature.rank.model.RankId;
import com.favick.domain.feature.rank.model.RankName;
import com.favick.domain.feature.rank.model.RankNameValue;
import com.favick.domain.feature.user.model.User;
import com.favick.domain.feature.user.model.UserEmail;
import com.favick.domain.feature.user.model.UserId;
import com.favick.domain.feature.user.service.UserDomainService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("CreateUserInteractor のテスト")
class CreateUserInteractorTest {

    @Mock
    private UserCommandService userCommandService;

    @Mock
    private RankQueryService rankQueryService;

    @Mock
    private UserRegisteredEventPublisher userRegisteredEventPublisher;

    @Mock
    private UserQueryService userQueryService;

    @Mock
    private UserDomainService userDomainService;

    @Mock
    private PasswordEncoder passwordEncoder;

    @InjectMocks
    private CreateUserInteractor createUserInteractor;

    private UUID rankId;
    private Rank mockRank;
    private CreateUserCommandDto validCommand;

    @BeforeEach
    void setUp() {
        rankId = UUID.randomUUID();
        mockRank = Rank.reconstruct(
            new RankId(rankId),
            new RankName(RankNameValue.RANK_SEED),
            LocalDateTime.now(),
            LocalDateTime.now()
        );

        validCommand = new CreateUserCommandDto(
            "TestUser",
            "<EMAIL>",
            "Password123",
            "https://example.com/avatar.png",
            "http://localhost:8080"
        );
    }

    @Test
    @DisplayName("正常系：ユーザー作成が正常に処理される")
    void handle_validCommand_shouldProcessSuccessfully() {
        // Arrange
        UUID userId = new UserId(UUID.randomUUID()).value();
        String encodedPassword = "encoded-password";

        when(rankQueryService.getByName(any(RankName.class)))
            .thenReturn(Optional.of(mockRank));
        when(passwordEncoder.encode(anyString()))
            .thenReturn(encodedPassword);
        when(userCommandService.create(any(User.class)))
            .thenReturn(new UserId(userId));

        // Act
        createUserInteractor.handle(validCommand);

        // Assert
        verify(rankQueryService).getByName(any(RankName.class));
        verify(passwordEncoder).encode("Password123");
        verify(userCommandService).create(any(User.class));
        verify(userRegisteredEventPublisher).publish(any(UserId.class), any(UserEmail.class), eq("http://localhost:8080"));
    }

    @Test
    @DisplayName("異常系：存在しないランクを指定した場合は例外が発生する")
    void handle_nonExistingRank_shouldThrowResourceNotFoundException() {
        // Arrange
        when(rankQueryService.getByName(any(RankName.class)))
            .thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> createUserInteractor.handle(validCommand));

        verify(rankQueryService).getByName(any(RankName.class));
        verify(userCommandService, never()).create(any(User.class));
        verify(userRegisteredEventPublisher, never()).publish(any(), any(), any());
    }

    @Test
    @DisplayName("異常系：無効なメールアドレス形式の場合はバリデーションエラーが発生する")
    void handle_invalidEmail_shouldThrowValidationError() {
        // Arrange
        CreateUserCommandDto invalidCommand = new CreateUserCommandDto(
            "TestUser",
            "invalid-email", // 無効なメールアドレス
            "Password123",
            "https://example.com/avatar.png",
            "http://localhost:8080"
        );

        // rankQueryService をモックして ResourceNotFoundException を回避
        when(rankQueryService.getByName(any(RankName.class)))
            .thenReturn(Optional.of(mockRank));

        // Act & Assert
        assertThrows(InvalidDomainsException.class, () -> createUserInteractor.handle(invalidCommand));

        verify(userCommandService, never()).create(any(User.class));
        verify(userRegisteredEventPublisher, never()).publish(any(), any(), any());
    }

    @Test
    @DisplayName("異常系：空のユーザー名の場合はバリデーションエラーが発生する")
    void handle_emptyUserName_shouldThrowValidationError() {
        // Arrange
        CreateUserCommandDto invalidCommand = new CreateUserCommandDto(
            "", // 空のユーザー名
            "<EMAIL>",
            "Password123",
            "https://example.com/avatar.png",
            "http://localhost:8080"
        );

        // rankQueryService をモックして ResourceNotFoundException を回避
        when(rankQueryService.getByName(any(RankName.class)))
            .thenReturn(Optional.of(mockRank));

        // Act & Assert
        assertThrows(InvalidDomainsException.class, () -> createUserInteractor.handle(invalidCommand));

        verify(userCommandService, never()).create(any(User.class));
        verify(userRegisteredEventPublisher, never()).publish(any(), any(), any());
    }

    @Test
    @DisplayName("異常系：短すぎるパスワードの場合はバリデーションエラーが発生する")
    void handle_shortPassword_shouldThrowValidationError() {
        // Arrange
        CreateUserCommandDto invalidCommand = new CreateUserCommandDto(
            "TestUser",
            "<EMAIL>",
            "123", // 短すぎるパスワード
            "https://example.com/avatar.png",
            "http://localhost:8080"
        );

        // rankQueryService をモックして ResourceNotFoundException を回避
        when(rankQueryService.getByName(any(RankName.class)))
            .thenReturn(Optional.of(mockRank));

        // Act & Assert
        assertThrows(InvalidDomainsException.class, () -> createUserInteractor.handle(invalidCommand));

        verify(userCommandService, never()).create(any(User.class));
        verify(userRegisteredEventPublisher, never()).publish(any(), any(), any());
    }

    @Test
    @DisplayName("正常系：画像URLがnullの場合も正常に処理される")
    void handle_nullImageUrl_shouldProcessSuccessfully() {
        // Arrange
        CreateUserCommandDto commandWithNullImage = new CreateUserCommandDto(
            "TestUser",
            "<EMAIL>",
            "Password123",
            null, // 画像URLなし
            "http://localhost:8080"
        );

        UUID userId = new UserId(UUID.randomUUID()).value();
        String encodedPassword = "encoded-password";

        when(rankQueryService.getByName(any(RankName.class)))
            .thenReturn(Optional.of(mockRank));
        when(passwordEncoder.encode(anyString()))
            .thenReturn(encodedPassword);
        when(userCommandService.create(any(User.class)))
            .thenReturn(new UserId(userId));

        // Act
        createUserInteractor.handle(commandWithNullImage);

        // Assert
        verify(userCommandService).create(any(User.class));
        verify(userRegisteredEventPublisher).publish(any(UserId.class), any(UserEmail.class), eq("http://localhost:8080"));
    }
}
