package com.favick.application.usecase;

import com.favick.application.dto.command.ToggleLikeCommandDto;
import com.favick.application.dto.result.FaveSummaryResultDto;
import com.favick.application.service.fave.query.FaveQueryService;
import com.favick.application.service.like.command.LikeCommandService;
import com.favick.application.service.like.query.LikeQueryService;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.domain.feature.fave.model.FaveContent;
import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.fave.model.Fave;
import com.favick.domain.feature.like.model.Like;
import com.favick.domain.feature.like.model.LikeId;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("ToggleLikeInteractor のテスト")
class ToggleLikeInteractorTest {

    @Mock
    private FaveQueryService faveQueryService;

    @Mock
    private LikeCommandService likeCommandService;

    @Mock
    private LikeQueryService likeQueryService;

    @InjectMocks
    private ToggleLikeInteractor toggleLikeInteractor;

    private UUID testUserId;
    private UUID testFaveId;
    private UUID testThemeId;
    private Fave testFave;
    private ToggleLikeCommandDto validCommand;

    @BeforeEach
    void setUp() {
        testUserId = UUID.randomUUID();
        testFaveId = UUID.randomUUID();
        testThemeId = UUID.randomUUID();

        LocalDateTime now = LocalDateTime.now();
        testFave = Fave.reconstruct(
            new FaveId(testFaveId),
            new UserId(testUserId),
            new ThemeId(testThemeId),
            new FaveContent("photos/test.jpg"),
            now,
            now
        );

        validCommand = new ToggleLikeCommandDto(testUserId, testFaveId);
    }

    @Test
    @DisplayName("正常系：いいねが存在しない場合、新しいいいねを作成する")
    void handle_withNoExistingLike_shouldCreateNewLike() {
        // Arrange
        when(faveQueryService.getById(any(FaveId.class)))
            .thenReturn(Optional.of(testFave));
        when(likeQueryService.getByUserIdAndFaveId(any(UserId.class), any(FaveId.class)))
            .thenReturn(Optional.empty());
        when(likeCommandService.create(any(Like.class)))
            .thenReturn(new LikeId(UUID.randomUUID()));

        // Act
        FaveSummaryResultDto result = toggleLikeInteractor.handle(validCommand);

        // Assert
        assertNotNull(result);
        assertEquals(testFaveId, result.id());
        assertEquals("photos/test.jpg", result.content());
        assertTrue(result.isLiked());
        assertEquals(testFave.getCreatedAt(), result.createdAt());
        assertEquals(testFave.getUpdatedAt(), result.updatedAt());

        verify(faveQueryService).getById(any(FaveId.class));
        verify(likeQueryService).getByUserIdAndFaveId(any(UserId.class), any(FaveId.class));
        verify(likeCommandService).create(any(Like.class));
        verify(likeCommandService, never()).delete(any(Like.class));
    }

    @Test
    @DisplayName("正常系：いいねが存在する場合、既存のいいねを削除する")
    void handle_withExistingLike_shouldDeleteExistingLike() {
        // Arrange
        Like existingLike = Like.create(new UserId(testUserId), new FaveId(testFaveId));

        when(faveQueryService.getById(any(FaveId.class)))
            .thenReturn(Optional.of(testFave));
        when(likeQueryService.getByUserIdAndFaveId(any(UserId.class), any(FaveId.class)))
            .thenReturn(Optional.of(existingLike));
        doNothing().when(likeCommandService).delete(any(Like.class));

        // Act
        FaveSummaryResultDto result = toggleLikeInteractor.handle(validCommand);

        // Assert
        assertNotNull(result);
        assertEquals(testFaveId, result.id());
        assertEquals("photos/test.jpg", result.content());
        assertFalse(result.isLiked());
        assertEquals(testFave.getCreatedAt(), result.createdAt());
        assertEquals(testFave.getUpdatedAt(), result.updatedAt());

        verify(faveQueryService).getById(any(FaveId.class));
        verify(likeQueryService).getByUserIdAndFaveId(any(UserId.class), any(FaveId.class));
        verify(likeCommandService).delete(existingLike);
        verify(likeCommandService, never()).create(any(Like.class));
    }

    @Test
    @DisplayName("異常系：無効なユーザーIDの場合はバリデーションエラーが発生する")
    void handle_withInvalidUserId_shouldThrowValidationError() {
        // Arrange
        ToggleLikeCommandDto invalidCommand = new ToggleLikeCommandDto(null, testFaveId);

        // Act & Assert
        assertThrows(InvalidDomainsException.class, () -> toggleLikeInteractor.handle(invalidCommand));

        verify(faveQueryService, never()).getById(any());
        verify(likeQueryService, never()).getByUserIdAndFaveId(any(), any());
        verify(likeCommandService, never()).create(any());
        verify(likeCommandService, never()).delete(any());
    }

    @Test
    @DisplayName("異常系：無効なFaveIDの場合はバリデーションエラーが発生する")
    void handle_withInvalidFaveId_shouldThrowValidationError() {
        // Arrange
        ToggleLikeCommandDto invalidCommand = new ToggleLikeCommandDto(testUserId, null);

        // Act & Assert
        assertThrows(InvalidDomainsException.class, () -> toggleLikeInteractor.handle(invalidCommand));

        verify(faveQueryService, never()).getById(any());
        verify(likeQueryService, never()).getByUserIdAndFaveId(any(), any());
        verify(likeCommandService, never()).create(any());
        verify(likeCommandService, never()).delete(any());
    }

    @Test
    @DisplayName("異常系：存在しないFaveIDの場合はResourceNotFoundExceptionが発生する")
    void handle_withNonExistentFaveId_shouldThrowResourceNotFoundException() {
        // Arrange
        when(faveQueryService.getById(any(FaveId.class)))
            .thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> toggleLikeInteractor.handle(validCommand));

        verify(faveQueryService).getById(any(FaveId.class));
        verify(likeQueryService, never()).getByUserIdAndFaveId(any(), any());
        verify(likeCommandService, never()).create(any());
        verify(likeCommandService, never()).delete(any());
    }

    @Test
    @DisplayName("正常系：REJECTEDステータスのFaveでもいいねの切り替えができる")
    void handle_withRejectedFave_shouldToggleLike() {
        // Arrange
        LocalDateTime now = LocalDateTime.now();
        Fave rejectedFave = Fave.reconstruct(
            new FaveId(testFaveId),
            new UserId(testUserId),
            new ThemeId(testThemeId),
            new FaveContent("photos/rejected.jpg"),
            now,
            now
        );

        when(faveQueryService.getById(any(FaveId.class)))
            .thenReturn(Optional.of(rejectedFave));
        when(likeQueryService.getByUserIdAndFaveId(any(UserId.class), any(FaveId.class)))
            .thenReturn(Optional.empty());
        when(likeCommandService.create(any(Like.class)))
            .thenReturn(new LikeId(UUID.randomUUID()));

        // Act
        FaveSummaryResultDto result = toggleLikeInteractor.handle(validCommand);

        // Assert
        assertNotNull(result);
        assertTrue(result.isLiked());

        verify(likeCommandService).create(any(Like.class));
    }

    @Test
    @DisplayName("正常系：PENDINGステータスのFaveでもいいねの切り替えができる")
    void handle_withPendingFave_shouldToggleLike() {
        // Arrange
        LocalDateTime now = LocalDateTime.now();
        Fave pendingFave = Fave.reconstruct(
            new FaveId(testFaveId),
            new UserId(testUserId),
            new ThemeId(testThemeId),
            new FaveContent("photos/pending.jpg"),
            now,
            now
        );

        when(faveQueryService.getById(any(FaveId.class)))
            .thenReturn(Optional.of(pendingFave));
        when(likeQueryService.getByUserIdAndFaveId(any(UserId.class), any(FaveId.class)))
            .thenReturn(Optional.empty());
        when(likeCommandService.create(any(Like.class)))
            .thenReturn(new LikeId(UUID.randomUUID()));

        // Act
        FaveSummaryResultDto result = toggleLikeInteractor.handle(validCommand);

        // Assert
        assertNotNull(result);
        assertTrue(result.isLiked());

        verify(likeCommandService).create(any(Like.class));
    }
}
