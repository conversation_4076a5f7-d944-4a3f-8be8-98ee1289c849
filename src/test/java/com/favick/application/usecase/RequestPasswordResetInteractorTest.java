package com.favick.application.usecase;

import com.favick.application.dto.command.RequestPasswordResetCommandDto;
import com.favick.application.service.user.event.PasswordResetEventPublisher;
import com.favick.application.service.user.query.UserQueryService;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.domain.feature.rank.model.RankId;
import com.favick.domain.feature.user.model.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("RequestPasswordResetInteractor のテスト")
class RequestPasswordResetInteractorTest {

    @Mock
    private UserQueryService userQueryService;

    @Mock
    private PasswordResetEventPublisher passwordResetEventPublisher;

    @InjectMocks
    private RequestPasswordResetInteractor requestPasswordResetInteractor;

    @Test
    @DisplayName("正常系：有効なメールアドレスでパスワードリセット要求が処理される")
    void handle_validEmail_shouldProcessPasswordResetRequest() {
        // Arrange
        String email = "<EMAIL>";
        String origin = "http://localhost:8080";
        RequestPasswordResetCommandDto command = new RequestPasswordResetCommandDto(email, origin);

        UUID userId = UUID.randomUUID();
        User user = User.reconstruct(
            new UserId(userId),
            new RankId(UUID.randomUUID()),
            new UserName("TestUser"),
            new UserEmail(email),
            new UserPassword("hashedPassword"),
            new UserEnabled(true),
            null,
            LocalDateTime.now().minusDays(1),
            LocalDateTime.now().minusDays(1)
        );

        when(userQueryService.getByEmail(any(UserEmail.class)))
            .thenReturn(Optional.of(user));

        // Act
        requestPasswordResetInteractor.handle(command);

        // Assert
        verify(userQueryService).getByEmail(any(UserEmail.class));
        verify(passwordResetEventPublisher)
            .publish(eq(user.getId()), any(UserEmail.class), eq(origin));
    }

    @Test
    @DisplayName("異常系：存在しないメールアドレスの場合はResourceNotFoundExceptionが発生する")
    void handle_nonExistingEmail_shouldThrowResourceNotFoundException() {
        // Arrange
        String nonExistingEmail = "<EMAIL>";
        String origin = "http://localhost:8080";
        RequestPasswordResetCommandDto command = new RequestPasswordResetCommandDto(nonExistingEmail, origin);

        when(userQueryService.getByEmail(any(UserEmail.class)))
            .thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> requestPasswordResetInteractor.handle(command));

        verify(userQueryService).getByEmail(any(UserEmail.class));
        verify(passwordResetEventPublisher, never()).publish(any(), any(), any());
    }

    @Test
    @DisplayName("正常系：無効化されたユーザーでもパスワードリセット要求は処理される")
    void handle_disabledUser_shouldStillProcessPasswordResetRequest() {
        // Arrange
        String email = "<EMAIL>";
        String origin = "http://localhost:8080";
        RequestPasswordResetCommandDto command = new RequestPasswordResetCommandDto(email, origin);

        UUID userId = UUID.randomUUID();
        User disabledUser = User.reconstruct(
            new UserId(userId),
            new RankId(UUID.randomUUID()),
            new UserName("DisabledUser"),
            new UserEmail(email),
            new UserPassword("hashedPassword"),
            new UserEnabled(false), // 無効化されたユーザー
            null,
            LocalDateTime.now().minusDays(1),
            LocalDateTime.now().minusDays(1)
        );

        when(userQueryService.getByEmail(any(UserEmail.class)))
            .thenReturn(Optional.of(disabledUser));

        // Act
        requestPasswordResetInteractor.handle(command);

        // Assert
        verify(userQueryService).getByEmail(any(UserEmail.class));
        verify(passwordResetEventPublisher)
            .publish(eq(disabledUser.getId()), any(UserEmail.class), eq(origin));
    }

    @Test
    @DisplayName("異常系：無効なメールアドレス形式の場合はバリデーションエラーが発生する")
    void handle_invalidEmail_shouldThrowValidationError() {
        // Arrange
        String invalidEmail = "invalid-email"; // 無効なメールアドレス形式
        String origin = "http://localhost:8080";
        RequestPasswordResetCommandDto command = new RequestPasswordResetCommandDto(invalidEmail, origin);

        // Act & Assert
        assertThrows(InvalidDomainsException.class, () -> requestPasswordResetInteractor.handle(command));

        verify(userQueryService, never()).getByEmail(any(UserEmail.class));
        verify(passwordResetEventPublisher, never()).publish(any(), any(), any());
    }

    @Test
    @DisplayName("異常系：空のメールアドレスの場合はバリデーションエラーが発生する")
    void handle_emptyEmail_shouldThrowValidationError() {
        // Arrange
        String emptyEmail = ""; // 空のメールアドレス
        String origin = "http://localhost:8080";
        RequestPasswordResetCommandDto command = new RequestPasswordResetCommandDto(emptyEmail, origin);

        // Act & Assert
        assertThrows(InvalidDomainsException.class, () -> requestPasswordResetInteractor.handle(command));

        verify(userQueryService, never()).getByEmail(any(UserEmail.class));
        verify(passwordResetEventPublisher, never()).publish(any(), any(), any());
    }
}