package com.favick.application.usecase;

import com.favick.application.dto.query.ListThemesQueryDto;
import com.favick.application.dto.result.ThemeSummaryResultDto;
import com.favick.application.service.theme.query.ThemeQueryService;
import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import com.favick.domain.feature.theme.model.*;
import com.favick.domain.feature.theme_localization.model.ThemeDescription;
import com.favick.domain.feature.theme_localization.model.ThemeLocalization;
import com.favick.domain.feature.theme_localization.model.ThemeLocalizationId;
import com.favick.domain.feature.theme_localization.model.ThemeTitle;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.util.Pair;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@DisplayName("ListThemesInteractor のテスト")
class ListThemesInteractorTest {

    @Mock
    private ThemeQueryService themeQueryService;


    @InjectMocks
    private ListThemesInteractor listThemesInteractor;

    @Test
    @DisplayName("正常系：テーマ一覧が正しく取得できる")
    void handle_withValidData_shouldReturnThemeList() {
        // Arrange
        UUID themeId1 = UUID.randomUUID();
        UUID themeId2 = UUID.randomUUID();
        LocalDate startDate1 = LocalDate.now().plusMonths(1);
        LocalDate startDate2 = LocalDate.now().plusMonths(2);
        LocalDateTime now = LocalDateTime.now();

        ListThemesQueryDto query = new ListThemesQueryDto(LanguageCodeValue.JA);

        Theme theme1 = Theme.reconstruct(
            new ThemeId(themeId1),
            new ThemeType(ThemeTypeValue.PHOTO),
            new ThemeStartDate(startDate1),
            now,
            now
        );

        Theme theme2 = Theme.reconstruct(
            new ThemeId(themeId2),
            new ThemeType(ThemeTypeValue.MOVIE),
            new ThemeStartDate(startDate2),
            now,
            now
        );

        ThemeLocalization localization1 = ThemeLocalization.reconstruct(
            new ThemeLocalizationId(UUID.randomUUID()),
            new ThemeId(themeId1),
            new LanguageCode(LanguageCodeValue.JA),
            new ThemeTitle("テーマ1"),
            new ThemeDescription("テーマ1の説明"),
            now,
            now
        );

        ThemeLocalization localization2 = ThemeLocalization.reconstruct(
            new ThemeLocalizationId(UUID.randomUUID()),
            new ThemeId(themeId2),
            new LanguageCode(LanguageCodeValue.JA),
            new ThemeTitle("テーマ2"),
            new ThemeDescription("テーマ2の説明"),
            now,
            now
        );

        List<Pair<Theme, ThemeLocalization>> integratedThemes = List.of(
            Pair.of(theme1, localization1),
            Pair.of(theme2, localization2)
        );

        when(themeQueryService.listIntegratedByLanguageCode(
            new LanguageCode(LanguageCodeValue.JA)))
            .thenReturn(integratedThemes);

        // Act
        List<ThemeSummaryResultDto> result = listThemesInteractor.handle(query);

        // Assert
        assertEquals(2, result.size());

        ThemeSummaryResultDto summary1 = result.get(0);
        assertEquals(themeId1, summary1.id());
        assertEquals("テーマ1", summary1.title());
        assertEquals("テーマ1の説明", summary1.description());
        assertEquals(ThemeTypeValue.PHOTO, summary1.type());
        assertEquals(startDate1, summary1.startDate());

        ThemeSummaryResultDto summary2 = result.get(1);
        assertEquals(themeId2, summary2.id());
        assertEquals("テーマ2", summary2.title());
        assertEquals("テーマ2の説明", summary2.description());
        assertEquals(ThemeTypeValue.MOVIE, summary2.type());
        assertEquals(startDate2, summary2.startDate());
    }

    @Test
    @DisplayName("正常系：テーマが存在しない場合は空のリストが返される")
    void handle_withNoThemes_shouldReturnEmptyList() {
        // Arrange
        ListThemesQueryDto query = new ListThemesQueryDto(LanguageCodeValue.JA);
        when(themeQueryService.listIntegratedByLanguageCode(
            new LanguageCode(LanguageCodeValue.JA)))
            .thenReturn(Collections.emptyList());

        // Act
        List<ThemeSummaryResultDto> result = listThemesInteractor.handle(query);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("正常系：言語コードがnullの場合は日本語がデフォルトで使用される")
    void handle_withNullLanguageCode_shouldUseJapaneseAsDefault() {
        // Arrange
        UUID themeId = UUID.randomUUID();
        LocalDate startDate = LocalDate.now().plusMonths(1);
        LocalDateTime now = LocalDateTime.now();

        ListThemesQueryDto query = new ListThemesQueryDto(null);

        Theme theme = Theme.reconstruct(
            new ThemeId(themeId),
            new ThemeType(ThemeTypeValue.PHOTO),
            new ThemeStartDate(startDate),
            now,
            now
        );

        ThemeLocalization localization = ThemeLocalization.reconstruct(
            new ThemeLocalizationId(UUID.randomUUID()),
            new ThemeId(themeId),
            new LanguageCode(LanguageCodeValue.JA),
            new ThemeTitle("日本語テーマ"),
            new ThemeDescription("日本語の説明"),
            now,
            now
        );

        List<Pair<Theme, ThemeLocalization>> integratedThemes = List.of(
            Pair.of(theme, localization)
        );

        when(themeQueryService.listIntegratedByLanguageCode(
            new LanguageCode(LanguageCodeValue.JA)))
            .thenReturn(integratedThemes);

        // Act
        List<ThemeSummaryResultDto> result = listThemesInteractor.handle(query);

        // Assert
        assertEquals(1, result.size());
        ThemeSummaryResultDto summary = result.get(0);
        assertEquals("日本語テーマ", summary.title());
        assertEquals("日本語の説明", summary.description());
    }
}
