package com.favick.application.usecase;

import com.favick.application.dto.query.GetCurrentFaveQueryDto;
import com.favick.application.dto.result.CurrentThemePickedFaveResultDto;
import com.favick.application.service.fave.query.FaveQueryService;
import com.favick.application.service.theme.query.ThemeQueryService;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.domain.feature.fave.model.Fave;
import com.favick.domain.feature.fave.model.FaveContent;
import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import com.favick.domain.feature.review.model.Review;
import com.favick.domain.feature.review.model.ReviewId;
import com.favick.domain.feature.review.model.ReviewStatus;
import com.favick.domain.feature.review.model.ReviewStatusValue;
import com.favick.domain.feature.theme.model.*;
import com.favick.domain.feature.theme_localization.model.ThemeDescription;
import com.favick.domain.feature.theme_localization.model.ThemeLocalization;
import com.favick.domain.feature.theme_localization.model.ThemeLocalizationId;
import com.favick.domain.feature.theme_localization.model.ThemeTitle;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.util.Pair;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("GetCurrentThemePickedFaveInteractor のテスト")
class GetCurrentThemePickedFaveInteractorTest {

    @Mock
    private ThemeQueryService themeQueryService;


    @Mock
    private FaveQueryService faveQueryService;

    @InjectMocks
    private GetCurrentThemePickedFaveInteractor getCurrentThemePickedFaveInteractor;

    @Test
    @DisplayName("正常系：開催中のテーマとお気に入りを正常に取得できる")
    void handle_withValidQuery_shouldReturnCurrentThemePickedFave() {
        // Arrange
        UUID userId = UUID.randomUUID();
        LanguageCodeValue languageCode = LanguageCodeValue.JA;
        GetCurrentFaveQueryDto query = new GetCurrentFaveQueryDto(userId, languageCode);

        UUID themeId = UUID.randomUUID();
        UUID faveId = UUID.randomUUID();
        LocalDateTime now = LocalDateTime.now();
        LocalDate startDate = LocalDate.now();

        Theme theme = Theme.reconstruct(
            new ThemeId(themeId),
            new ThemeType(ThemeTypeValue.PHOTO),
            new ThemeStartDate(startDate),
            now, now
        );

        ThemeLocalization themeLocalization = ThemeLocalization.reconstruct(
            new ThemeLocalizationId(UUID.randomUUID()),
            new ThemeId(themeId),
            new LanguageCode(LanguageCodeValue.JA),
            new ThemeTitle("テストタイトル"),
            new ThemeDescription("テスト説明"),
            now, now
        );

        Fave fave = Fave.reconstruct(
            new FaveId(faveId),
            new UserId(userId),
            new ThemeId(themeId),
            new FaveContent("お気に入りコンテンツ"),
            now, now
        );

        Review review = Review.reconstruct(
            new ReviewId(UUID.randomUUID()),
            new FaveId(faveId),
            new ReviewStatus(ReviewStatusValue.APPROVED),
            now, now
        );

        Pair<Fave, Review> integratedFave = Pair.of(fave, review);

        Pair<Theme, ThemeLocalization> integratedTheme = Pair.of(theme, themeLocalization);

        when(themeQueryService.getCurrentIntegratedByLanguageCode(
            new LanguageCode(LanguageCodeValue.JA))
        ).thenReturn(Optional.of(integratedTheme));
        when(faveQueryService.getIntegratedByUserIdAndThemeId(
            new UserId(userId), new ThemeId(themeId))
        ).thenReturn(Optional.of(integratedFave));

        // Act
        CurrentThemePickedFaveResultDto result = getCurrentThemePickedFaveInteractor.handle(query);

        // Assert
        assertNotNull(result);
        assertNotNull(result.theme());
        assertEquals(themeId, result.theme().id());
        assertEquals("テストタイトル", result.theme().title());
        assertEquals("テスト説明", result.theme().description());
        assertEquals(ThemeTypeValue.PHOTO, result.theme().type());
        assertEquals(startDate, result.theme().startDate());

        assertTrue(result.fave().isPresent());
        assertEquals(faveId, result.fave().get().id());
        assertEquals("お気に入りコンテンツ", result.fave().get().content());
        assertEquals(ReviewStatusValue.APPROVED, result.fave().get().status());

        verify(themeQueryService).getCurrentIntegratedByLanguageCode(
            new LanguageCode(LanguageCodeValue.JA));
        verify(faveQueryService).getIntegratedByUserIdAndThemeId(
            new UserId(userId), new ThemeId(themeId));
    }

    @Test
    @DisplayName("正常系：お気に入りが存在しない場合でも正常に処理される")
    void handle_withNoFave_shouldReturnCurrentThemeWithoutFave() {
        // Arrange
        UUID userId = UUID.randomUUID();
        LanguageCodeValue languageCode = LanguageCodeValue.JA;
        GetCurrentFaveQueryDto query = new GetCurrentFaveQueryDto(userId, languageCode);

        UUID themeId = UUID.randomUUID();
        LocalDateTime now = LocalDateTime.now();
        LocalDate startDate = LocalDate.now();

        Theme theme = Theme.reconstruct(
            new ThemeId(themeId),
            new ThemeType(ThemeTypeValue.PHOTO),
            new ThemeStartDate(startDate),
            now, now
        );

        ThemeLocalization themeLocalization = ThemeLocalization.reconstruct(
            new ThemeLocalizationId(UUID.randomUUID()),
            new ThemeId(themeId),
            new LanguageCode(LanguageCodeValue.JA),
            new ThemeTitle("テストタイトル"),
            new ThemeDescription("テスト説明"),
            now, now
        );

        Pair<Theme, ThemeLocalization> integratedTheme = Pair.of(theme, themeLocalization);

        when(themeQueryService.getCurrentIntegratedByLanguageCode(
            new LanguageCode(LanguageCodeValue.JA))
        ).thenReturn(Optional.of(integratedTheme));
        when(faveQueryService.getIntegratedByUserIdAndThemeId(
            new UserId(userId), new ThemeId(themeId))
        ).thenReturn(Optional.empty());

        // Act
        CurrentThemePickedFaveResultDto result = getCurrentThemePickedFaveInteractor.handle(query);

        // Assert
        assertNotNull(result);
        assertNotNull(result.theme());
        assertEquals(themeId, result.theme().id());
        assertTrue(result.fave().isEmpty());

        verify(themeQueryService).getCurrentIntegratedByLanguageCode(
            new LanguageCode(LanguageCodeValue.JA));
        verify(faveQueryService).getIntegratedByUserIdAndThemeId(
            new UserId(userId), new ThemeId(themeId));
    }

    @Test
    @DisplayName("異常系：開催中のテーマが存在しない場合は例外が発生する")
    void handle_withNoCurrentTheme_shouldThrowResourceNotFoundException() {
        // Arrange
        UUID userId = UUID.randomUUID();
        LanguageCodeValue languageCode = LanguageCodeValue.JA;
        GetCurrentFaveQueryDto query = new GetCurrentFaveQueryDto(userId, languageCode);

        when(themeQueryService.getCurrentIntegratedByLanguageCode(
            new LanguageCode(LanguageCodeValue.JA))
        ).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> getCurrentThemePickedFaveInteractor.handle(query));

        verify(themeQueryService).getCurrentIntegratedByLanguageCode(
            new LanguageCode(LanguageCodeValue.JA));
        verify(faveQueryService, never()).getIntegratedByUserIdAndThemeId(any(), any());
    }


    @Test
    @DisplayName("英語の言語コードでも正常に処理される")
    void handle_withEnglishLanguageCode_shouldSucceed() {
        // Arrange
        UUID userId = UUID.randomUUID();
        LanguageCodeValue languageCode = LanguageCodeValue.EN;
        GetCurrentFaveQueryDto query = new GetCurrentFaveQueryDto(userId, languageCode);

        UUID themeId = UUID.randomUUID();
        LocalDateTime now = LocalDateTime.now();
        LocalDate startDate = LocalDate.now();

        Theme theme = Theme.reconstruct(
            new ThemeId(themeId),
            new ThemeType(ThemeTypeValue.PHOTO),
            new ThemeStartDate(startDate),
            now, now
        );

        ThemeLocalization themeLocalization = ThemeLocalization.reconstruct(
            new ThemeLocalizationId(UUID.randomUUID()),
            new ThemeId(themeId),
            new LanguageCode(LanguageCodeValue.EN),
            new ThemeTitle("Test Title"),
            new ThemeDescription("Test Description"),
            now, now
        );

        Pair<Theme, ThemeLocalization> integratedTheme = Pair.of(theme, themeLocalization);

        when(themeQueryService.getCurrentIntegratedByLanguageCode(
            new LanguageCode(LanguageCodeValue.EN))
        ).thenReturn(Optional.of(integratedTheme));
        when(faveQueryService.getIntegratedByUserIdAndThemeId(
            new UserId(userId), new ThemeId(themeId))
        ).thenReturn(Optional.empty());

        // Act
        CurrentThemePickedFaveResultDto result = getCurrentThemePickedFaveInteractor.handle(query);

        // Assert
        assertNotNull(result);
        assertNotNull(result.theme());
        assertEquals("Test Title", result.theme().title());
        assertEquals("Test Description", result.theme().description());
        assertTrue(result.fave().isEmpty());

        verify(themeQueryService).getCurrentIntegratedByLanguageCode(
            new LanguageCode(LanguageCodeValue.EN));
        verify(faveQueryService).getIntegratedByUserIdAndThemeId(
            new UserId(userId), new ThemeId(themeId));
    }
}
