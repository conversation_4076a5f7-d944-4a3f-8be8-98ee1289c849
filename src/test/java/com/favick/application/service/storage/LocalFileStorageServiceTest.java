package com.favick.application.service.storage;

import com.favick.domain.feature.theme.model.ThemeType;
import com.favick.domain.feature.theme.model.ThemeTypeValue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("LocalFileStorageService のテスト")
class LocalFileStorageServiceTest {

    @TempDir
    Path tempDir;

    private LocalFileStorageService localFileStorageService;

    @BeforeEach
    void setUp() {
        localFileStorageService = new LocalFileStorageService();
        ReflectionTestUtils.setField(localFileStorageService, "uploadDir", tempDir.toString());
    }

    @Test
    @DisplayName("写真ファイルを正常に保存できる")
    void store_withPhotoFile_shouldStoreFileSuccessfully() throws IOException {
        // Arrange
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "test-photo.jpg",
            "image/jpeg",
            "test photo content".getBytes()
        );
        ThemeType themeType = new ThemeType(ThemeTypeValue.PHOTO);

        // Act
        String result = localFileStorageService.store(file, themeType);

        // Assert
        assertNotNull(result);
        assertTrue(result.startsWith("photos/"));
        assertTrue(result.endsWith(".jpg"));

        Path savedFile = Paths.get(tempDir.toString(), result);
        assertTrue(Files.exists(savedFile));
        assertEquals("test photo content", Files.readString(savedFile));
    }

    @Test
    @DisplayName("動画ファイルを正常に保存できる")
    void store_withMovieFile_shouldStoreFileSuccessfully() throws IOException {
        // Arrange
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "test-movie.mp4",
            "video/mp4",
            "test movie content".getBytes()
        );
        ThemeType themeType = new ThemeType(ThemeTypeValue.MOVIE);

        // Act
        String result = localFileStorageService.store(file, themeType);

        // Assert
        assertNotNull(result);
        assertTrue(result.startsWith("movies/"));
        assertTrue(result.endsWith(".mp4"));

        Path savedFile = Paths.get(tempDir.toString(), result);
        assertTrue(Files.exists(savedFile));
    }

    @Test
    @DisplayName("音声ファイルを正常に保存できる")
    void store_withAudioFile_shouldStoreFileSuccessfully() throws IOException {
        // Arrange
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "test-audio.mp3",
            "audio/mpeg",
            "test audio content".getBytes()
        );
        ThemeType themeType = new ThemeType(ThemeTypeValue.AUDIO);

        // Act
        String result = localFileStorageService.store(file, themeType);

        // Assert
        assertNotNull(result);
        assertTrue(result.startsWith("audios/"));
        assertTrue(result.endsWith(".mp3"));

        Path savedFile = Paths.get(tempDir.toString(), result);
        assertTrue(Files.exists(savedFile));
    }

    @Test
    @DisplayName("特殊文字を含むファイル名をサニタイズして保存できる")
    void store_withSpecialCharactersInFileName_shouldSanitizeAndStore() throws IOException {
        // Arrange
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "テスト！@#$%ファイル.jpg",
            "image/jpeg",
            "test content".getBytes()
        );
        ThemeType themeType = new ThemeType(ThemeTypeValue.PHOTO);

        // Act
        String result = localFileStorageService.store(file, themeType);

        // Assert
        assertNotNull(result);
        assertTrue(result.startsWith("photos/"));
        assertTrue(result.endsWith(".jpg"));

        Path savedFile = Paths.get(tempDir.toString(), result);
        assertTrue(Files.exists(savedFile));
    }

    @Test
    @DisplayName("存在するファイルを上書きして保存できる")
    void store_withExistingFile_shouldReplaceFile() throws IOException {
        // Arrange
        Path photosDir = tempDir.resolve("photos");
        Files.createDirectories(photosDir);
        String fileName = "existing-file.jpg";
        Path existingFile = photosDir.resolve(fileName);
        Files.writeString(existingFile, "old content");

        MockMultipartFile file = new MockMultipartFile(
            "file",
            "new-photo.jpg",
            "image/jpeg",
            "new content".getBytes()
        );
        ThemeType themeType = new ThemeType(ThemeTypeValue.PHOTO);

        // Act
        String result = localFileStorageService.store(file, themeType);

        // Assert
        assertNotNull(result);
        Path savedFile = Paths.get(tempDir.toString(), result);
        assertTrue(Files.exists(savedFile));
        assertEquals("new content", Files.readString(savedFile));
    }

    @Test
    @DisplayName("存在するファイルを正常に削除できる")
    void delete_withExistingFile_shouldDeleteFileSuccessfully() throws IOException {
        // Arrange
        Path photosDir = tempDir.resolve("photos");
        Files.createDirectories(photosDir);
        String fileName = "test-file.jpg";
        Path testFile = photosDir.resolve(fileName);
        Files.writeString(testFile, "test content");
        String filePath = "photos/" + fileName;

        // Act
        localFileStorageService.delete(filePath);

        // Assert
        assertFalse(Files.exists(testFile));
    }

    @Test
    @DisplayName("存在しないファイルを削除しようとしても例外が発生しない")
    void delete_withNonExistentFile_shouldNotThrowException() {
        // Arrange
        String filePath = "photos/non-existent.jpg";

        // Act & Assert
        assertDoesNotThrow(() -> localFileStorageService.delete(filePath));
    }

    @Test
    @DisplayName("拡張子のないファイル名の場合はStringIndexOutOfBoundsExceptionが発生する")
    void store_withFileNameWithoutExtension_shouldThrowException() {
        // Arrange
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "filename_without_extension",
            "application/octet-stream",
            "test content".getBytes()
        );
        ThemeType themeType = new ThemeType(ThemeTypeValue.PHOTO);

        // Act & Assert
        assertThrows(StringIndexOutOfBoundsException.class, () -> localFileStorageService.store(file, themeType));
    }

    @Test
    @DisplayName("空のファイル名の場合はStringIndexOutOfBoundsExceptionが発生する")
    void store_withEmptyFileName_shouldThrowException() {
        // Arrange
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "",
            "application/octet-stream",
            "test content".getBytes()
        );
        ThemeType themeType = new ThemeType(ThemeTypeValue.PHOTO);

        // Act & Assert
        assertThrows(StringIndexOutOfBoundsException.class, () -> localFileStorageService.store(file, themeType));
    }
}
