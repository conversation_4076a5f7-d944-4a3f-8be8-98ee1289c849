package com.favick.application.service.user.event;

import com.favick.domain.feature.user.event.PasswordResetEvent;
import com.favick.domain.feature.user.model.UserEmail;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
@DisplayName("PasswordResetEventPublisher のテスト")
class PasswordResetEventPublisherTest {

    @Mock
    private ApplicationEventPublisher applicationEventPublisher;

    @InjectMocks
    private PasswordResetEventPublisher passwordResetEventPublisher;

    @Test
    @DisplayName("正常系：パスワードリセットイベントが正しく発行される")
    void publish_shouldPublishPasswordResetEvent() {
        // Arrange
        UUID userId = UUID.randomUUID();
        String email = "<EMAIL>";
        String origin = "http://localhost:8080";

        UserId userIdObj = new UserId(userId);
        UserEmail userEmailObj = new UserEmail(email);

        // Act
        passwordResetEventPublisher.publish(userIdObj, userEmailObj, origin);

        // Assert
        ArgumentCaptor<PasswordResetEvent> eventCaptor = ArgumentCaptor.forClass(PasswordResetEvent.class);
        verify(applicationEventPublisher, times(1)).publishEvent(eventCaptor.capture());

        PasswordResetEvent publishedEvent = eventCaptor.getValue();
        assertNotNull(publishedEvent);
        assertEquals(passwordResetEventPublisher, publishedEvent.getSource());
        assertEquals(userId, publishedEvent.getUserId());
        assertEquals(email, publishedEvent.getUserEmail());
        assertEquals(origin, publishedEvent.getOrigin());
    }

    @Test
    @DisplayName("正常系：HTTPSのoriginでもイベントが正しく発行される")
    void publish_withHttpsOrigin_shouldPublishEvent() {
        // Arrange
        UUID userId = UUID.randomUUID();
        String email = "<EMAIL>";
        String origin = "https://favick.com";

        UserId userIdObj = new UserId(userId);
        UserEmail userEmailObj = new UserEmail(email);

        // Act
        passwordResetEventPublisher.publish(userIdObj, userEmailObj, origin);

        // Assert
        ArgumentCaptor<PasswordResetEvent> eventCaptor = ArgumentCaptor.forClass(PasswordResetEvent.class);
        verify(applicationEventPublisher, times(1)).publishEvent(eventCaptor.capture());

        PasswordResetEvent publishedEvent = eventCaptor.getValue();
        assertNotNull(publishedEvent);
        assertEquals(origin, publishedEvent.getOrigin());
    }

    @Test
    @DisplayName("正常系：複数のイベント発行が正しく処理される")
    void publish_multipleEvents_shouldPublishAllEvents() {
        // Arrange
        UUID userId1 = UUID.randomUUID();
        UUID userId2 = UUID.randomUUID();
        String email1 = "<EMAIL>";
        String email2 = "<EMAIL>";
        String origin = "http://localhost:8080";

        UserId userIdObj1 = new UserId(userId1);
        UserEmail userEmailObj1 = new UserEmail(email1);
        UserId userIdObj2 = new UserId(userId2);
        UserEmail userEmailObj2 = new UserEmail(email2);

        // Act
        passwordResetEventPublisher.publish(userIdObj1, userEmailObj1, origin);
        passwordResetEventPublisher.publish(userIdObj2, userEmailObj2, origin);

        // Assert
        ArgumentCaptor<PasswordResetEvent> eventCaptor = ArgumentCaptor.forClass(PasswordResetEvent.class);
        verify(applicationEventPublisher, times(2)).publishEvent(eventCaptor.capture());
    }

    @Test
    @DisplayName("正常系：異なるoriginでもイベントが正しく発行される")
    void publish_withDifferentOrigins_shouldPublishEvents() {
        // Arrange
        UUID userId = UUID.randomUUID();
        String email = "<EMAIL>";
        String origin1 = "http://localhost:8080";
        String origin2 = "https://favick.com";

        UserId userIdObj = new UserId(userId);
        UserEmail userEmailObj = new UserEmail(email);

        // Act
        passwordResetEventPublisher.publish(userIdObj, userEmailObj, origin1);
        passwordResetEventPublisher.publish(userIdObj, userEmailObj, origin2);

        // Assert
        ArgumentCaptor<PasswordResetEvent> eventCaptor = ArgumentCaptor.forClass(PasswordResetEvent.class);
        verify(applicationEventPublisher, times(2)).publishEvent(eventCaptor.capture());

        var publishedEvents = eventCaptor.getAllValues();
        assertEquals(2, publishedEvents.size());
        assertEquals(origin1, publishedEvents.get(0).getOrigin());
        assertEquals(origin2, publishedEvents.get(1).getOrigin());
    }

    @Test
    @DisplayName("正常系：長いメールアドレスでもイベントが正しく発行される")
    void publish_withLongEmailAddress_shouldPublishEvent() {
        // Arrange
        UUID userId = UUID.randomUUID();
        String longEmail = "<EMAIL>";
        String origin = "http://localhost:8080";

        UserId userIdObj = new UserId(userId);
        UserEmail userEmailObj = new UserEmail(longEmail);

        // Act
        passwordResetEventPublisher.publish(userIdObj, userEmailObj, origin);

        // Assert
        ArgumentCaptor<PasswordResetEvent> eventCaptor = ArgumentCaptor.forClass(PasswordResetEvent.class);
        verify(applicationEventPublisher, times(1)).publishEvent(eventCaptor.capture());

        PasswordResetEvent publishedEvent = eventCaptor.getValue();
        assertEquals(longEmail, publishedEvent.getUserEmail());
    }
}
