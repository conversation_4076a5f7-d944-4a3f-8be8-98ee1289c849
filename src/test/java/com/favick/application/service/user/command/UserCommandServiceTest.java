package com.favick.application.service.user.command;

import com.favick.application.port.out.feature.user.repository.UserRepository;
import com.favick.domain.exception.ValueObjectException;
import com.favick.domain.feature.rank.model.RankId;
import com.favick.domain.feature.user.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("UserCommandService のテスト")
class UserCommandServiceTest {

    @Mock
    private UserRepository userRepository;

    @InjectMocks
    private UserCommandService userCommandService;

    private UUID testRankId;

    @BeforeEach
    void setUp() {
        testRankId = UUID.randomUUID();
    }

    @Test
    @DisplayName("正常なユーザー情報でユーザーを作成できる")
    void create_withValidData_shouldCreateUser() {
        // Arrange
        RankId rankId = new RankId(testRankId);
        UserName userName = new UserName("山田太郎");
        UserEmail userEmail = new UserEmail("<EMAIL>");
        UserImageUri userImageUri = new UserImageUri("https://example.com/avatar.jpg");

        // Act
        User user = User.create(rankId, userName, userEmail, new UserPassword("encodedPassword"), userImageUri);
        UserId result = userCommandService.create(user);

        // Assert
        assertNotNull(result);
        assertEquals(user.getId(), result);

        verify(userRepository).save(user);
    }

    @Test
    @DisplayName("画像URLがnullの場合でもユーザーを作成できる")
    void create_withNullImageUrl_shouldCreateUser() {
        // Arrange
        RankId rankId = new RankId(testRankId);
        UserName userName = new UserName("田中花子");
        UserEmail userEmail = new UserEmail("<EMAIL>");

        // Act
        User user = User.create(rankId, userName, userEmail, new UserPassword("encodedPassword"), null);
        UserId result = userCommandService.create(user);

        // Assert
        assertNotNull(result);
        assertEquals(user.getId(), result);

        verify(userRepository).save(user);
    }


    @Test
    @DisplayName("無効な値オブジェクトでユーザーを作成しようとするとエラーになる")
    void create_withInvalidValueObjects_shouldThrowException() {
        // 空の名前でエラーが発生することを確認
        Exception nameException = assertThrows(
            ValueObjectException.class,
            () -> new UserName("")
        );
        assertInstanceOf(ValueObjectException.class, nameException);

        // 無効なメールアドレスでエラーが発生することを確認
        Exception emailException = assertThrows(
            ValueObjectException.class,
            () -> new UserEmail("invalid-email")
        );
        assertInstanceOf(ValueObjectException.class, emailException);

        // 無効なパスワードでエラーが発生することを確認
        Exception passwordException = assertThrows(
            ValueObjectException.class,
            () -> new RawUserPassword("weak")
        );
        assertInstanceOf(ValueObjectException.class, passwordException);

        // 長すぎる画像URLでエラーが発生することを確認
        String tooLongUrl = "https://example.com/" + "a".repeat(300);
        Exception uriException = assertThrows(
            ValueObjectException.class,
            () -> new UserImageUri(tooLongUrl)
        );
        assertInstanceOf(ValueObjectException.class, uriException);

        verify(userRepository, never()).save(any(User.class));
    }

    @Test
    @DisplayName("存在するユーザーを有効化できる")
    void enableUser_withExistingUser_shouldEnableUser() {
        // Arrange
        User user = mock(User.class);

        when(user.isEnabled()).thenReturn(false);

        // Act
        userCommandService.enableUser(user);

        // Assert
        verify(user).enable();
        verify(userRepository).save(user);
    }

    @Test
    @DisplayName("既に有効なユーザーを有効化しても何も起こらない")
    void enableUser_withAlreadyEnabledUser_shouldDoNothing() {
        // Arrange
        User user = mock(User.class);

        when(user.isEnabled()).thenReturn(true);

        // Act
        userCommandService.enableUser(user);

        // Assert
        verify(user, never()).enable();
        verify(userRepository, never()).save(any(User.class));
    }

    @Test
    @DisplayName("パスワードを更新できる")
    void updatePassword_withValidPassword_shouldUpdatePassword() {
        // Arrange
        User user = mock(User.class);
        UserPassword password = new UserPassword("newPassword123");

        // Act
        userCommandService.updatePassword(user, password);

        // Assert
        verify(user).changePassword(password);
        verify(userRepository).save(user);
    }


}
