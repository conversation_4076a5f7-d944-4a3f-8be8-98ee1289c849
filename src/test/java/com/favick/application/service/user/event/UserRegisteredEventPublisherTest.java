package com.favick.application.service.user.event;

import com.favick.domain.feature.user.event.UserRegisteredEvent;
import com.favick.domain.feature.user.model.UserEmail;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("UserRegisteredEventPublisher のテスト")
class UserRegisteredEventPublisherTest {

    @Mock
    private ApplicationEventPublisher applicationEventPublisher;

    @InjectMocks
    private UserRegisteredEventPublisher userRegisteredEventPublisher;

    @Test
    @DisplayName("正常系：ユーザー登録イベントが正しく発行される")
    void publish_shouldPublishUserRegisteredEvent() {
        // Arrange
        UUID userId = UUID.randomUUID();
        String email = "<EMAIL>";
        String origin = "http://localhost:8080";

        UserId userIdObj = new UserId(userId);
        UserEmail userEmailObj = new UserEmail(email);

        // Act
        userRegisteredEventPublisher.publish(userIdObj, userEmailObj, origin);

        // Assert
        ArgumentCaptor<UserRegisteredEvent> eventCaptor = ArgumentCaptor.forClass(UserRegisteredEvent.class);
        verify(applicationEventPublisher, times(1)).publishEvent(eventCaptor.capture());

        UserRegisteredEvent publishedEvent = eventCaptor.getValue();
        assertNotNull(publishedEvent);
        assertEquals(userRegisteredEventPublisher, publishedEvent.getSource());
        assertEquals(userId, publishedEvent.getUserId());
        assertEquals(email, publishedEvent.getUserEmail());
        assertEquals(origin, publishedEvent.getOrigin());
    }

    @Test
    @DisplayName("正常系：HTTPSのoriginでもイベントが正しく発行される")
    void publish_withHttpsOrigin_shouldPublishEvent() {
        // Arrange
        UUID userId = UUID.randomUUID();
        String email = "<EMAIL>";
        String origin = "https://favick.com";

        UserId userIdObj = new UserId(userId);
        UserEmail userEmailObj = new UserEmail(email);

        // Act
        userRegisteredEventPublisher.publish(userIdObj, userEmailObj, origin);

        // Assert
        ArgumentCaptor<UserRegisteredEvent> eventCaptor = ArgumentCaptor.forClass(UserRegisteredEvent.class);
        verify(applicationEventPublisher, times(1)).publishEvent(eventCaptor.capture());

        UserRegisteredEvent publishedEvent = eventCaptor.getValue();
        assertNotNull(publishedEvent);
        assertEquals(origin, publishedEvent.getOrigin());
    }

    @Test
    @DisplayName("正常系：複数のイベント発行が正しく処理される")
    void publish_multipleEvents_shouldPublishAllEvents() {
        // Arrange
        UUID userId1 = UUID.randomUUID();
        UUID userId2 = UUID.randomUUID();
        String email1 = "<EMAIL>";
        String email2 = "<EMAIL>";
        String origin = "http://localhost:8080";

        UserId userIdObj1 = new UserId(userId1);
        UserEmail userEmailObj1 = new UserEmail(email1);
        UserId userIdObj2 = new UserId(userId2);
        UserEmail userEmailObj2 = new UserEmail(email2);

        // Act
        userRegisteredEventPublisher.publish(userIdObj1, userEmailObj1, origin);
        userRegisteredEventPublisher.publish(userIdObj2, userEmailObj2, origin);

        // Assert
        ArgumentCaptor<UserRegisteredEvent> eventCaptor = ArgumentCaptor.forClass(UserRegisteredEvent.class);
        verify(applicationEventPublisher, times(2)).publishEvent(eventCaptor.capture());
    }

    @Test
    @DisplayName("正常系：異なるoriginでもイベントが正しく発行される")
    void publish_withDifferentOrigins_shouldPublishEvents() {
        // Arrange
        UUID userId = UUID.randomUUID();
        String email = "<EMAIL>";
        String origin1 = "http://localhost:8080";
        String origin2 = "https://favick.com";

        UserId userIdObj = new UserId(userId);
        UserEmail userEmailObj = new UserEmail(email);

        // Act
        userRegisteredEventPublisher.publish(userIdObj, userEmailObj, origin1);
        userRegisteredEventPublisher.publish(userIdObj, userEmailObj, origin2);

        // Assert
        ArgumentCaptor<UserRegisteredEvent> eventCaptor = ArgumentCaptor.forClass(UserRegisteredEvent.class);
        verify(applicationEventPublisher, times(2)).publishEvent(eventCaptor.capture());

        var publishedEvents = eventCaptor.getAllValues();
        assertEquals(2, publishedEvents.size());
        assertEquals(origin1, publishedEvents.get(0).getOrigin());
        assertEquals(origin2, publishedEvents.get(1).getOrigin());
    }

    @Test
    @DisplayName("正常系：長いメールアドレスでもイベントが正しく発行される")
    void publish_withLongEmailAddress_shouldPublishEvent() {
        // Arrange
        UUID userId = UUID.randomUUID();
        String longEmail = "<EMAIL>";
        String origin = "http://localhost:8080";

        UserId userIdObj = new UserId(userId);
        UserEmail userEmailObj = new UserEmail(longEmail);

        // Act
        userRegisteredEventPublisher.publish(userIdObj, userEmailObj, origin);

        // Assert
        ArgumentCaptor<UserRegisteredEvent> eventCaptor = ArgumentCaptor.forClass(UserRegisteredEvent.class);
        verify(applicationEventPublisher, times(1)).publishEvent(eventCaptor.capture());

        UserRegisteredEvent publishedEvent = eventCaptor.getValue();
        assertEquals(longEmail, publishedEvent.getUserEmail());
    }

    @Test
    @DisplayName("正常系：同じユーザーで複数回イベント発行が可能")
    void publish_sameUserMultipleTimes_shouldPublishAllEvents() {
        // Arrange
        UUID userId = UUID.randomUUID();
        String email = "<EMAIL>";
        String origin = "http://localhost:8080";

        UserId userIdObj = new UserId(userId);
        UserEmail userEmailObj = new UserEmail(email);

        // Act
        userRegisteredEventPublisher.publish(userIdObj, userEmailObj, origin);
        userRegisteredEventPublisher.publish(userIdObj, userEmailObj, origin);

        // Assert
        verify(applicationEventPublisher, times(2)).publishEvent(any(UserRegisteredEvent.class));
    }
}
