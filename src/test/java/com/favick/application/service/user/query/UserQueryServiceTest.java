package com.favick.application.service.user.query;

import com.favick.application.port.out.feature.user.repository.UserRepository;
import com.favick.domain.feature.rank.model.RankId;
import com.favick.domain.feature.user.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("UserQueryService のテスト")
class UserQueryServiceTest {

    @Mock
    private UserRepository userRepository;

    @InjectMocks
    private UserQueryService userQueryService;

    private UserId testUserId;
    private UserEmail testUserEmail;
    private User testUser;

    @BeforeEach
    void setUp() {
        testUserId = new UserId(UUID.randomUUID());
        testUserEmail = new UserEmail("<EMAIL>");
        testUser = createTestUser();
    }

    private User createTestUser() {
        RankId rankId = new RankId(UUID.randomUUID());
        UserName userName = new UserName("テストユーザー");
        UserPassword userPassword = new UserPassword("encodedPassword");
        UserImageUri userImageUri = new UserImageUri("https://example.com/avatar.jpg");

        return User.create(rankId, userName, testUserEmail, userPassword, userImageUri);
    }

    @Test
    @DisplayName("IDでユーザーを正常に取得できる")
    void getById_withExistingUser_shouldReturnUser() {
        // Arrange
        when(userRepository.findById(testUserId))
            .thenReturn(Optional.of(testUser));

        // Act
        Optional<User> result = userQueryService.getById(testUserId);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(testUser, result.get());
        verify(userRepository).findById(testUserId);
    }

    @Test
    @DisplayName("存在しないIDでユーザーを取得しようとした場合は空のOptionalを返す")
    void getById_withNonExistentUser_shouldReturnEmptyOptional() {
        // Arrange
        UserId nonExistentId = new UserId(UUID.randomUUID());
        when(userRepository.findById(nonExistentId))
            .thenReturn(Optional.empty());

        // Act
        Optional<User> result = userQueryService.getById(nonExistentId);

        // Assert
        assertTrue(result.isEmpty());
        verify(userRepository).findById(nonExistentId);
    }

    @Test
    @DisplayName("メールアドレスでユーザーを正常に取得できる")
    void getByEmail_withExistingUser_shouldReturnUser() {
        // Arrange
        when(userRepository.findByEmail(testUserEmail))
            .thenReturn(Optional.of(testUser));

        // Act
        Optional<User> result = userQueryService.getByEmail(testUserEmail);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(testUser, result.get());
        assertEquals(testUserEmail, result.get().getEmail());
        verify(userRepository).findByEmail(testUserEmail);
    }

    @Test
    @DisplayName("存在しないメールアドレスでユーザーを取得しようとした場合は空のOptionalを返す")
    void getByEmail_withNonExistentEmail_shouldReturnEmptyOptional() {
        // Arrange
        UserEmail nonExistentEmail = new UserEmail("<EMAIL>");
        when(userRepository.findByEmail(nonExistentEmail))
            .thenReturn(Optional.empty());

        // Act
        Optional<User> result = userQueryService.getByEmail(nonExistentEmail);

        // Assert
        assertTrue(result.isEmpty());
        verify(userRepository).findByEmail(nonExistentEmail);
    }

    @Test
    @DisplayName("メールアドレスが存在する場合はtrueを返す")
    void existsByEmail_withExistingEmail_shouldReturnTrue() {
        // Arrange
        when(userRepository.existsByEmail(testUserEmail))
            .thenReturn(true);

        // Act
        boolean result = userQueryService.existsByEmail(testUserEmail);

        // Assert
        assertTrue(result);
        verify(userRepository).existsByEmail(testUserEmail);
    }

    @Test
    @DisplayName("メールアドレスが存在しない場合はfalseを返す")
    void existsByEmail_withNonExistentEmail_shouldReturnFalse() {
        // Arrange
        UserEmail nonExistentEmail = new UserEmail("<EMAIL>");
        when(userRepository.existsByEmail(nonExistentEmail))
            .thenReturn(false);

        // Act
        boolean result = userQueryService.existsByEmail(nonExistentEmail);

        // Assert
        assertFalse(result);
        verify(userRepository).existsByEmail(nonExistentEmail);
    }

    @Test
    @DisplayName("複数の異なるメールアドレスで正しく存在確認できる")
    void existsByEmail_withDifferentEmails_shouldReturnCorrectResults() {
        // Arrange
        UserEmail existingEmail = new UserEmail("<EMAIL>");
        UserEmail nonExistentEmail = new UserEmail("<EMAIL>");
        
        when(userRepository.existsByEmail(existingEmail)).thenReturn(true);
        when(userRepository.existsByEmail(nonExistentEmail)).thenReturn(false);

        // Act
        boolean existingResult = userQueryService.existsByEmail(existingEmail);
        boolean nonExistentResult = userQueryService.existsByEmail(nonExistentEmail);

        // Assert
        assertTrue(existingResult);
        assertFalse(nonExistentResult);
        verify(userRepository).existsByEmail(existingEmail);
        verify(userRepository).existsByEmail(nonExistentEmail);
    }

    @Test
    @DisplayName("リポジトリでエラーが発生した場合は例外が伝播される")
    void getById_whenRepositoryThrowsException_shouldPropagateException() {
        // Arrange
        RuntimeException expectedException = new RuntimeException("Repository error");
        when(userRepository.findById(testUserId))
            .thenThrow(expectedException);

        // Act & Assert
        RuntimeException thrownException = assertThrows(
            RuntimeException.class,
            () -> userQueryService.getById(testUserId)
        );
        
        assertEquals("Repository error", thrownException.getMessage());
        verify(userRepository).findById(testUserId);
    }
}