package com.favick.application.service.theme.command;

import com.favick.application.port.out.feature.theme.repository.ThemeRepository;
import com.favick.domain.feature.theme.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
@DisplayName("ThemeCommandService のテスト")
class ThemeCommandServiceTest {

    @Mock
    private ThemeRepository themeRepository;

    @InjectMocks
    private ThemeCommandService themeCommandService;

    private Theme testTheme;

    @BeforeEach
    void setUp() {
        testTheme = createTestTheme();
    }

    private Theme createTestTheme() {
        ThemeType type = new ThemeType(ThemeTypeValue.PHOTO);
        ThemeStartDate startDate = new ThemeStartDate(LocalDate.now().plusDays(1));

        return Theme.create(type, startDate);
    }

    @Test
    @DisplayName("新しいテーマを正常に作成できる")
    void create_withValidTheme_shouldCreateThemeSuccessfully() {
        // Arrange
        // testTheme は setUp() で作成済み

        // Act
        ThemeId result = themeCommandService.create(testTheme);

        // Assert
        assertNotNull(result);
        assertEquals(testTheme.getId(), result);
        verify(themeRepository).save(testTheme);
    }

    @Test
    @DisplayName("既存のテーマを正常に更新できる")
    void update_withValidTheme_shouldUpdateThemeSuccessfully() {
        // Arrange
        // testTheme は setUp() で作成済み

        // Act
        ThemeId result = themeCommandService.update(testTheme);

        // Assert
        assertNotNull(result);
        assertEquals(testTheme.getId(), result);
        verify(themeRepository).save(testTheme);
    }

    @Test
    @DisplayName("既存のテーマを正常に削除できる")
    void delete_withValidTheme_shouldDeleteThemeSuccessfully() {
        // Arrange
        // testTheme は setUp() で作成済み

        // Act
        themeCommandService.delete(testTheme);

        // Assert
        verify(themeRepository).delete(testTheme.getId());
    }

    @Test
    @DisplayName("テーマ作成時にリポジトリでエラーが発生した場合は例外が伝播される")
    void create_whenRepositoryThrowsException_shouldPropagateException() {
        // Arrange
        RuntimeException expectedException = new RuntimeException("Repository error");
        doThrow(expectedException).when(themeRepository).save(testTheme);

        // Act & Assert
        RuntimeException thrownException = assertThrows(
            RuntimeException.class,
            () -> themeCommandService.create(testTheme)
        );

        assertEquals("Repository error", thrownException.getMessage());
        verify(themeRepository).save(testTheme);
    }

    @Test
    @DisplayName("テーマ更新時にリポジトリでエラーが発生した場合は例外が伝播される")
    void update_whenRepositoryThrowsException_shouldPropagateException() {
        // Arrange
        RuntimeException expectedException = new RuntimeException("Repository error");
        doThrow(expectedException).when(themeRepository).save(testTheme);

        // Act & Assert
        RuntimeException thrownException = assertThrows(
            RuntimeException.class,
            () -> themeCommandService.update(testTheme)
        );

        assertEquals("Repository error", thrownException.getMessage());
        verify(themeRepository).save(testTheme);
    }

    @Test
    @DisplayName("テーマ削除時にリポジトリでエラーが発生した場合は例外が伝播される")
    void delete_whenRepositoryThrowsException_shouldPropagateException() {
        // Arrange
        RuntimeException expectedException = new RuntimeException("Repository error");
        doThrow(expectedException).when(themeRepository).delete(testTheme.getId());

        // Act & Assert
        RuntimeException thrownException = assertThrows(
            RuntimeException.class,
            () -> themeCommandService.delete(testTheme)
        );

        assertEquals("Repository error", thrownException.getMessage());
        verify(themeRepository).delete(testTheme.getId());
    }

    @Test
    @DisplayName("異なる種類のテーマでも正常に作成できる")
    void create_withDifferentThemeTypes_shouldCreateSuccessfully() {
        // Arrange - 動画テーマ
        ThemeType movieType = new ThemeType(ThemeTypeValue.MOVIE);
        ThemeStartDate movieStartDate = new ThemeStartDate(LocalDate.now().plusDays(1));

        Theme movieTheme = Theme.create(movieType, movieStartDate);

        // Act
        ThemeId result = themeCommandService.create(movieTheme);

        // Assert
        assertNotNull(result);
        assertEquals(movieTheme.getId(), result);
        verify(themeRepository).save(movieTheme);
    }

    @Test
    @DisplayName("未来のテーマでも正常に作成できる")
    void create_withInactiveTheme_shouldCreateSuccessfully() {
        // Arrange
        ThemeType type = new ThemeType(ThemeTypeValue.PHOTO);
        ThemeStartDate startDate = new ThemeStartDate(LocalDate.now().plusDays(30));

        Theme pastTheme = Theme.create(type, startDate);

        // Act
        ThemeId result = themeCommandService.create(pastTheme);

        // Assert
        assertNotNull(result);
        assertEquals(pastTheme.getId(), result);
        verify(themeRepository).save(pastTheme);
    }
}
