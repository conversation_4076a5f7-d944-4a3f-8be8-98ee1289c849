package com.favick.application.service.theme.query;

import com.favick.application.port.out.feature.theme.repository.ThemeRepository;
import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import com.favick.domain.feature.theme.model.*;
import com.favick.domain.feature.theme_localization.model.ThemeLocalization;
import com.favick.domain.feature.theme_localization.model.ThemeTitle;
import com.favick.domain.feature.theme_localization.model.ThemeDescription;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.util.Pair;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@DisplayName("ThemeQueryService のテスト")
class ThemeQueryServiceTest {

    @Mock
    private ThemeRepository themeRepository;

    @InjectMocks
    private ThemeQueryService themeQueryService;

    private Theme testTheme;
    private ThemeId testThemeId;
    private ThemeStartDate testStartDate;
    private LanguageCode testLanguageCode;

    @BeforeEach
    void setUp() {
        testThemeId = new ThemeId(UUID.randomUUID());
        testStartDate = new ThemeStartDate(LocalDate.now().plusDays(1));
        testLanguageCode = new LanguageCode(LanguageCodeValue.JA);
        testTheme = createTestTheme();
    }

    private Theme createTestTheme() {
        ThemeType type = new ThemeType(ThemeTypeValue.PHOTO);

        return Theme.create(type, testStartDate);
    }


    @Test
    @DisplayName("IDでテーマを正常に取得できる")
    void getById_withExistingTheme_shouldReturnTheme() {
        // Arrange
        when(themeRepository.findById(testThemeId))
            .thenReturn(Optional.of(testTheme));

        // Act
        Optional<Theme> result = themeQueryService.getById(testThemeId);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(testTheme, result.get());
        verify(themeRepository).findById(testThemeId);
    }

    @Test
    @DisplayName("存在しないIDでテーマを取得しようとした場合は空のOptionalを返す")
    void getById_withNonExistentTheme_shouldReturnEmptyOptional() {
        // Arrange
        when(themeRepository.findById(testThemeId))
            .thenReturn(Optional.empty());

        // Act
        Optional<Theme> result = themeQueryService.getById(testThemeId);

        // Assert
        assertTrue(result.isEmpty());
        verify(themeRepository).findById(testThemeId);
    }

    @Test
    @DisplayName("開始日でテーマの存在確認を正常に行える")
    void existsByStartDate_withExistingStartDate_shouldReturnTrue() {
        // Arrange
        when(themeRepository.existsByStartDate(testStartDate))
            .thenReturn(true);

        // Act
        boolean result = themeQueryService.existsByStartDate(testStartDate);

        // Assert
        assertTrue(result);
        verify(themeRepository).existsByStartDate(testStartDate);
    }

    @Test
    @DisplayName("存在しない開始日でテーマの存在確認を行った場合はfalseを返す")
    void existsByStartDate_withNonExistentStartDate_shouldReturnFalse() {
        // Arrange
        when(themeRepository.existsByStartDate(testStartDate))
            .thenReturn(false);

        // Act
        boolean result = themeQueryService.existsByStartDate(testStartDate);

        // Assert
        assertFalse(result);
        verify(themeRepository).existsByStartDate(testStartDate);
    }

    @Test
    @DisplayName("指定IDを除いた開始日でテーマの存在確認を正常に行える")
    void existsByStartDateExcludingId_withExistingStartDate_shouldReturnTrue() {
        // Arrange
        ThemeId excludeId = new ThemeId(UUID.randomUUID());
        when(themeRepository.existsByStartDateExcludingId(testStartDate, excludeId))
            .thenReturn(true);

        // Act
        boolean result = themeQueryService.existsByStartDateExcludingId(testStartDate, excludeId);

        // Assert
        assertTrue(result);
        verify(themeRepository).existsByStartDateExcludingId(testStartDate, excludeId);
    }

    @Test
    @DisplayName("指定IDを除いた開始日でテーマが存在しない場合はfalseを返す")
    void existsByStartDateExcludingId_withNonExistentStartDate_shouldReturnFalse() {
        // Arrange
        ThemeId excludeId = new ThemeId(UUID.randomUUID());
        when(themeRepository.existsByStartDateExcludingId(testStartDate, excludeId))
            .thenReturn(false);

        // Act
        boolean result = themeQueryService.existsByStartDateExcludingId(testStartDate, excludeId);

        // Assert
        assertFalse(result);
        verify(themeRepository).existsByStartDateExcludingId(testStartDate, excludeId);
    }

    @Test
    @DisplayName("リポジトリでエラーが発生した場合は例外が伝播される")
    void listIntegratedByLanguageCode_whenRepositoryThrowsException_shouldPropagateException() {
        // Arrange
        RuntimeException expectedException = new RuntimeException("Repository error");
        when(themeRepository.findAllIntegratedByLanguageCode(testLanguageCode))
            .thenThrow(expectedException);

        // Act & Assert
        RuntimeException thrownException = assertThrows(
            RuntimeException.class,
            () -> themeQueryService.listIntegratedByLanguageCode(testLanguageCode)
        );

        assertEquals("Repository error", thrownException.getMessage());
        verify(themeRepository).findAllIntegratedByLanguageCode(testLanguageCode);
    }

    @Test
    @DisplayName("言語コードで統合済みのテーマ一覧を正常に取得できる")
    void listIntegratedByLanguageCode_withExistingThemes_shouldReturnIntegratedThemes() {
        // Arrange
        List<Pair<Theme, ThemeLocalization>> expectedThemes = List.of(
            Pair.of(testTheme, createTestThemeLocalization())
        );
        when(themeRepository.findAllIntegratedByLanguageCode(testLanguageCode))
            .thenReturn(expectedThemes);

        // Act
        List<Pair<Theme, ThemeLocalization>> result = themeQueryService.listIntegratedByLanguageCode(testLanguageCode);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(expectedThemes, result);
        verify(themeRepository).findAllIntegratedByLanguageCode(testLanguageCode);
    }

    @Test
    @DisplayName("現在開催中の統合済みのテーマを正常に取得できる")
    void getCurrentIntegratedByLanguageCode_withExistingTheme_shouldReturnCurrentIntegratedTheme() {
        // Arrange
        Pair<Theme, ThemeLocalization> expectedTheme = Pair.of(testTheme, createTestThemeLocalization());
        when(themeRepository.findCurrentIntegratedByLanguageCode(testLanguageCode))
            .thenReturn(Optional.of(expectedTheme));

        // Act
        Optional<Pair<Theme, ThemeLocalization>> result = themeQueryService.getCurrentIntegratedByLanguageCode(testLanguageCode);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(expectedTheme, result.get());
        verify(themeRepository).findCurrentIntegratedByLanguageCode(testLanguageCode);
    }

    @Test
    @DisplayName("テーマIDと言語コードで統合済みのテーマを正常に取得できる")
    void getIntegratedByIdAndLanguageCode_withExistingTheme_shouldReturnIntegratedTheme() {
        // Arrange
        Pair<Theme, ThemeLocalization> expectedTheme = Pair.of(testTheme, createTestThemeLocalization());
        when(themeRepository.findIntegratedByIdAndLanguageCode(testThemeId, testLanguageCode))
            .thenReturn(Optional.of(expectedTheme));

        // Act
        Optional<Pair<Theme, ThemeLocalization>> result = themeQueryService.getIntegratedByIdAndLanguageCode(testThemeId, testLanguageCode);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(expectedTheme, result.get());
        verify(themeRepository).findIntegratedByIdAndLanguageCode(testThemeId, testLanguageCode);
    }

    @Test
    @DisplayName("テーマIDで全言語の統合済みのテーマを正常に取得できる")
    void getIntegratedMultiLangById_withExistingTheme_shouldReturnMultiLangIntegratedTheme() {
        // Arrange
        Map<LanguageCode, ThemeLocalization> localizations = Map.of(
            testLanguageCode, createTestThemeLocalization()
        );
        Pair<Theme, Map<LanguageCode, ThemeLocalization>> expectedTheme = Pair.of(testTheme, localizations);
        when(themeRepository.findIntegratedMultiLangById(testThemeId))
            .thenReturn(Optional.of(expectedTheme));

        // Act
        Optional<Pair<Theme, Map<LanguageCode, ThemeLocalization>>> result = themeQueryService.getIntegratedMultiLangById(testThemeId);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(expectedTheme, result.get());
        verify(themeRepository).findIntegratedMultiLangById(testThemeId);
    }

    private ThemeLocalization createTestThemeLocalization() {
        return ThemeLocalization.create(
            testThemeId,
            testLanguageCode,
            new ThemeTitle("テストタイトル"),
            new ThemeDescription("テスト説明")
        );
    }
}
