package com.favick.application.service.rank.query;

import com.favick.application.port.out.feature.rank.repository.RankRepository;
import com.favick.domain.feature.rank.model.Rank;
import com.favick.domain.feature.rank.model.RankId;
import com.favick.domain.feature.rank.model.RankName;
import com.favick.domain.feature.rank.model.RankNameValue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@DisplayName("RankQueryService のテスト")
class RankQueryServiceTest {

    @Mock
    private RankRepository rankRepository;

    @InjectMocks
    private RankQueryService rankQueryService;

    private RankId testRankId;
    private RankName testRankName;
    private Rank testRank;

    @BeforeEach
    void setUp() {
        testRankId = new RankId(UUID.randomUUID());
        testRankName = new RankName(RankNameValue.RANK_SEED);
        testRank = createTestRank();
    }

    private Rank createTestRank() {
        return Rank.create(testRankName);
    }

    @Test
    @DisplayName("IDでランクを正常に取得できる")
    void getById_withExistingRank_shouldReturnRank() {
        // Arrange
        when(rankRepository.findById(testRankId))
            .thenReturn(Optional.of(testRank));

        // Act
        Optional<Rank> result = rankQueryService.getById(testRankId);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(testRank, result.get());
        verify(rankRepository).findById(testRankId);
    }

    @Test
    @DisplayName("存在しないIDでランクを取得しようとした場合は空のOptionalを返す")
    void getById_withNonExistentRank_shouldReturnEmptyOptional() {
        // Arrange
        RankId nonExistentId = new RankId(UUID.randomUUID());
        when(rankRepository.findById(nonExistentId))
            .thenReturn(Optional.empty());

        // Act
        Optional<Rank> result = rankQueryService.getById(nonExistentId);

        // Assert
        assertTrue(result.isEmpty());
        verify(rankRepository).findById(nonExistentId);
    }

    @Test
    @DisplayName("名前でランクを正常に取得できる")
    void getByName_withExistingRank_shouldReturnRank() {
        // Arrange
        when(rankRepository.findByName(testRankName))
            .thenReturn(Optional.of(testRank));

        // Act
        Optional<Rank> result = rankQueryService.getByName(testRankName);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(testRank, result.get());
        assertEquals(testRankName, result.get().getName());
        verify(rankRepository).findByName(testRankName);
    }

    @Test
    @DisplayName("存在しない名前でランクを取得しようとした場合は空のOptionalを返す")
    void getByName_withNonExistentRank_shouldReturnEmptyOptional() {
        // Arrange
        RankName nonExistentName = new RankName(RankNameValue.RANK_SPROUT);
        when(rankRepository.findByName(nonExistentName))
            .thenReturn(Optional.empty());

        // Act
        Optional<Rank> result = rankQueryService.getByName(nonExistentName);

        // Assert
        assertTrue(result.isEmpty());
        verify(rankRepository).findByName(nonExistentName);
    }

    @Test
    @DisplayName("異なるランク名でそれぞれ正しいランクを取得できる")
    void getByName_withDifferentRankNames_shouldReturnCorrectRanks() {
        // Arrange
        RankName seedName = new RankName(RankNameValue.RANK_SEED);
        RankName sproutName = new RankName(RankNameValue.RANK_SPROUT);
        RankName budName = new RankName(RankNameValue.RANK_BUD);

        Rank seedRank = Rank.create(seedName);
        Rank sproutRank = Rank.create(sproutName);
        Rank budRank = Rank.create(budName);

        when(rankRepository.findByName(seedName)).thenReturn(Optional.of(seedRank));
        when(rankRepository.findByName(sproutName)).thenReturn(Optional.of(sproutRank));
        when(rankRepository.findByName(budName)).thenReturn(Optional.of(budRank));

        // Act
        Optional<Rank> seedResult = rankQueryService.getByName(seedName);
        Optional<Rank> sproutResult = rankQueryService.getByName(sproutName);
        Optional<Rank> budResult = rankQueryService.getByName(budName);

        // Assert
        assertTrue(seedResult.isPresent());
        assertTrue(sproutResult.isPresent());
        assertTrue(budResult.isPresent());

        assertEquals(seedRank, seedResult.get());
        assertEquals(sproutRank, sproutResult.get());
        assertEquals(budRank, budResult.get());

        assertEquals(RankNameValue.RANK_SEED, seedResult.get().getName().value());
        assertEquals(RankNameValue.RANK_SPROUT, sproutResult.get().getName().value());
        assertEquals(RankNameValue.RANK_BUD, budResult.get().getName().value());

        verify(rankRepository).findByName(seedName);
        verify(rankRepository).findByName(sproutName);
        verify(rankRepository).findByName(budName);
    }

    @Test
    @DisplayName("ID検索でリポジトリエラーが発生した場合は例外が伝播される")
    void getById_whenRepositoryThrowsException_shouldPropagateException() {
        // Arrange
        RuntimeException expectedException = new RuntimeException("Repository error");
        when(rankRepository.findById(testRankId))
            .thenThrow(expectedException);

        // Act & Assert
        RuntimeException thrownException = assertThrows(
            RuntimeException.class,
            () -> rankQueryService.getById(testRankId)
        );

        assertEquals("Repository error", thrownException.getMessage());
        verify(rankRepository).findById(testRankId);
    }

    @Test
    @DisplayName("名前検索でリポジトリエラーが発生した場合は例外が伝播される")
    void getByName_whenRepositoryThrowsException_shouldPropagateException() {
        // Arrange
        RuntimeException expectedException = new RuntimeException("Repository error");
        when(rankRepository.findByName(testRankName))
            .thenThrow(expectedException);

        // Act & Assert
        RuntimeException thrownException = assertThrows(
            RuntimeException.class,
            () -> rankQueryService.getByName(testRankName)
        );

        assertEquals("Repository error", thrownException.getMessage());
        verify(rankRepository).findByName(testRankName);
    }

    @Test
    @DisplayName("最上位ランクを正常に取得できる")
    void getByName_withHighestRank_shouldReturnCorrectRank() {
        // Arrange
        RankName gardenName = new RankName(RankNameValue.RANK_GARDEN);
        Rank gardenRank = Rank.create(gardenName);

        when(rankRepository.findByName(gardenName))
            .thenReturn(Optional.of(gardenRank));

        // Act
        Optional<Rank> result = rankQueryService.getByName(gardenName);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(gardenRank, result.get());
        assertEquals(RankNameValue.RANK_GARDEN, result.get().getName().value());
        verify(rankRepository).findByName(gardenName);
    }
}
