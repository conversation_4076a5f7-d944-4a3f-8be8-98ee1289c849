package com.favick.application.service.language.query;

import com.favick.application.port.out.feature.language.repository.LanguageRepository;
import com.favick.domain.feature.language.model.Language;
import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import com.favick.domain.feature.language.model.LanguageDisplayName;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("LanguageQueryService のテスト")
class LanguageQueryServiceTest {

    @Mock
    private LanguageRepository languageRepository;

    @InjectMocks
    private LanguageQueryService languageQueryService;

    @Test
    @DisplayName("言語一覧を正常に取得できる")
    void list_withExistingLanguages_shouldReturnLanguageList() {
        // Arrange
        Language japanese = createLanguage(LanguageCodeValue.JA, "日本語");
        Language english = createLanguage(LanguageCodeValue.EN, "English");
        List<Language> expectedLanguages = List.of(japanese, english);

        when(languageRepository.findAll())
            .thenReturn(expectedLanguages);

        // Act
        List<Language> result = languageQueryService.list();

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(expectedLanguages, result);

        // 日本語の検証
        Language resultJapanese = result.stream()
            .filter(lang -> lang.getCode().value() == LanguageCodeValue.JA)
            .findFirst()
            .orElse(null);
        assertNotNull(resultJapanese);
        assertEquals("日本語", resultJapanese.getDisplayName().value());

        // 英語の検証
        Language resultEnglish = result.stream()
            .filter(lang -> lang.getCode().value() == LanguageCodeValue.EN)
            .findFirst()
            .orElse(null);
        assertNotNull(resultEnglish);
        assertEquals("English", resultEnglish.getDisplayName().value());

        verify(languageRepository).findAll();
    }

    @Test
    @DisplayName("言語が存在しない場合は空のリストを返す")
    void list_withNoLanguages_shouldReturnEmptyList() {
        // Arrange
        when(languageRepository.findAll())
            .thenReturn(List.of());

        // Act
        List<Language> result = languageQueryService.list();

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(languageRepository).findAll();
    }

    @Test
    @DisplayName("単一の言語のみ存在する場合も正常に取得できる")
    void list_withSingleLanguage_shouldReturnSingleLanguageList() {
        // Arrange
        Language japanese = createLanguage(LanguageCodeValue.JA, "日本語");
        List<Language> expectedLanguages = List.of(japanese);

        when(languageRepository.findAll())
            .thenReturn(expectedLanguages);

        // Act
        List<Language> result = languageQueryService.list();

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(japanese, result.get(0));
        assertEquals(LanguageCodeValue.JA, result.get(0).getCode().value());
        assertEquals("日本語", result.get(0).getDisplayName().value());
        verify(languageRepository).findAll();
    }

    @Test
    @DisplayName("複数回呼び出しても同じ結果を返す")
    void list_calledMultipleTimes_shouldReturnSameResult() {
        // Arrange
        Language japanese = createLanguage(LanguageCodeValue.JA, "日本語");
        Language english = createLanguage(LanguageCodeValue.EN, "English");
        List<Language> expectedLanguages = List.of(japanese, english);

        when(languageRepository.findAll())
            .thenReturn(expectedLanguages);

        // Act
        List<Language> result1 = languageQueryService.list();
        List<Language> result2 = languageQueryService.list();

        // Assert
        assertNotNull(result1);
        assertNotNull(result2);
        assertEquals(result1.size(), result2.size());
        assertEquals(result1, result2);

        verify(languageRepository, times(2)).findAll();
    }

    @Test
    @DisplayName("リポジトリでエラーが発生した場合は例外が伝播される")
    void list_whenRepositoryThrowsException_shouldPropagateException() {
        // Arrange
        RuntimeException expectedException = new RuntimeException("Repository error");
        when(languageRepository.findAll())
            .thenThrow(expectedException);

        // Act & Assert
        RuntimeException thrownException = assertThrows(
            RuntimeException.class,
            () -> languageQueryService.list()
        );

        assertEquals("Repository error", thrownException.getMessage());
        verify(languageRepository).findAll();
    }

    private Language createLanguage(LanguageCodeValue codeValue, String displayName) {
        LanguageCode code = new LanguageCode(codeValue);
        LanguageDisplayName name = new LanguageDisplayName(displayName);
        return Language.create(code, name);
    }
}
