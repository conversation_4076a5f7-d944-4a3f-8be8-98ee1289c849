package com.favick.application.service.admin.query;

import com.favick.application.port.out.feature.admin.repository.AdminRepository;
import com.favick.domain.feature.admin.model.Admin;
import com.favick.domain.feature.admin.model.AdminEmail;
import com.favick.domain.feature.admin.model.AdminName;
import com.favick.domain.feature.admin.model.AdminPassword;
import com.favick.domain.feature.role.model.RoleId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("AdminQueryService のテスト")
class AdminQueryServiceTest {

    @Mock
    private AdminRepository adminRepository;

    @InjectMocks
    private AdminQueryService adminQueryService;

    private AdminEmail testEmail;
    private Admin testAdmin;

    @BeforeEach
    void setUp() {
        testEmail = new AdminEmail("<EMAIL>");
        testAdmin = createTestAdmin();
    }

    private Admin createTestAdmin() {
        RoleId roleId = new RoleId(UUID.randomUUID());
        AdminName name = new AdminName("管理者");
        AdminPassword password = new AdminPassword("encodedPassword");

        return Admin.create(roleId, name, testEmail, password);
    }

    @Test
    @DisplayName("メールアドレスで管理者を正常に取得できる")
    void getByEmail_withExistingAdmin_shouldReturnAdmin() {
        // Arrange
        when(adminRepository.findByEmail(testEmail))
            .thenReturn(Optional.of(testAdmin));

        // Act
        Optional<Admin> result = adminQueryService.getByEmail(testEmail);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(testAdmin, result.get());
        assertEquals(testEmail, result.get().getEmail());
        verify(adminRepository).findByEmail(testEmail);
    }

    @Test
    @DisplayName("存在しないメールアドレスで管理者を取得しようとした場合は空のOptionalを返す")
    void getByEmail_withNonExistentAdmin_shouldReturnEmptyOptional() {
        // Arrange
        AdminEmail nonExistentEmail = new AdminEmail("<EMAIL>");
        when(adminRepository.findByEmail(nonExistentEmail))
            .thenReturn(Optional.empty());

        // Act
        Optional<Admin> result = adminQueryService.getByEmail(nonExistentEmail);

        // Assert
        assertTrue(result.isEmpty());
        verify(adminRepository).findByEmail(nonExistentEmail);
    }

    @Test
    @DisplayName("複数の異なるメールアドレスで管理者を取得できる")
    void getByEmail_withDifferentEmails_shouldReturnCorrectAdmin() {
        // Arrange
        AdminEmail email1 = new AdminEmail("<EMAIL>");
        AdminEmail email2 = new AdminEmail("<EMAIL>");
        
        Admin admin1 = Admin.create(
            new RoleId(UUID.randomUUID()),
            new AdminName("管理者1"), 
            email1, 
            new AdminPassword("password1")
        );
        Admin admin2 = Admin.create(
            new RoleId(UUID.randomUUID()),
            new AdminName("管理者2"), 
            email2, 
            new AdminPassword("password2")
        );

        when(adminRepository.findByEmail(email1)).thenReturn(Optional.of(admin1));
        when(adminRepository.findByEmail(email2)).thenReturn(Optional.of(admin2));

        // Act
        Optional<Admin> result1 = adminQueryService.getByEmail(email1);
        Optional<Admin> result2 = adminQueryService.getByEmail(email2);

        // Assert
        assertTrue(result1.isPresent());
        assertTrue(result2.isPresent());
        assertEquals(admin1, result1.get());
        assertEquals(admin2, result2.get());
        assertEquals(email1, result1.get().getEmail());
        assertEquals(email2, result2.get().getEmail());
        
        verify(adminRepository).findByEmail(email1);
        verify(adminRepository).findByEmail(email2);
    }

    @Test
    @DisplayName("リポジトリでエラーが発生した場合は例外が伝播される")
    void getByEmail_whenRepositoryThrowsException_shouldPropagateException() {
        // Arrange
        RuntimeException expectedException = new RuntimeException("Repository error");
        when(adminRepository.findByEmail(testEmail))
            .thenThrow(expectedException);

        // Act & Assert
        RuntimeException thrownException = assertThrows(
            RuntimeException.class,
            () -> adminQueryService.getByEmail(testEmail)
        );
        
        assertEquals("Repository error", thrownException.getMessage());
        verify(adminRepository).findByEmail(testEmail);
    }

    @Test
    @DisplayName("大文字小文字が異なるメールアドレスでも正しく処理される")
    void getByEmail_withDifferentCase_shouldHandleCorrectly() {
        // Arrange
        AdminEmail upperCaseEmail = new AdminEmail("<EMAIL>");
        when(adminRepository.findByEmail(upperCaseEmail))
            .thenReturn(Optional.of(testAdmin));

        // Act
        Optional<Admin> result = adminQueryService.getByEmail(upperCaseEmail);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(testAdmin, result.get());
        verify(adminRepository).findByEmail(upperCaseEmail);
    }
}