package com.favick.application.service.like.command;

import com.favick.application.port.out.feature.like.repository.LikeRepository;
import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.like.model.Like;
import com.favick.domain.feature.like.model.LikeId;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("LikeCommandService のテスト")
class LikeCommandServiceTest {

    @Mock
    private LikeRepository likeRepository;

    @InjectMocks
    private LikeCommandService likeCommandService;

    private UUID testUserId;
    private UUID testFaveId;
    private Like testLike;

    @BeforeEach
    void setUp() {
        testUserId = UUID.randomUUID();
        testFaveId = UUID.randomUUID();

        testLike = Like.create(
            new UserId(testUserId),
            new FaveId(testFaveId)
        );
    }

    @Test
    @DisplayName("新しいいいねを正常に作成できる")
    void create_withValidLike_shouldCreateLikeSuccessfully() {
        // Arrange
        doNothing().when(likeRepository).save(any(Like.class));

        // Act
        LikeId result = likeCommandService.create(testLike);

        // Assert
        assertNotNull(result);
        assertEquals(testLike.getId(), result);
        verify(likeRepository, times(1)).save(testLike);
    }

    @Test
    @DisplayName("いいねを正常に削除できる")
    void delete_withValidLike_shouldDeleteLikeSuccessfully() {
        // Arrange
        doNothing().when(likeRepository).delete(any(LikeId.class));

        // Act & Assert - 例外が発生しないことを確認
        assertDoesNotThrow(() -> likeCommandService.delete(testLike));

        verify(likeRepository, times(1)).delete(testLike.getId());
    }

    @Test
    @DisplayName("複数のいいねを連続して作成できる")
    void create_multipleConsecutiveLikes_shouldCreateAllSuccessfully() {
        // Arrange
        UUID anotherUserId = UUID.randomUUID();
        UUID anotherFaveId = UUID.randomUUID();

        Like anotherLike = Like.create(
            new UserId(anotherUserId),
            new FaveId(anotherFaveId)
        );

        doNothing().when(likeRepository).save(any(Like.class));

        // Act
        LikeId result1 = likeCommandService.create(testLike);
        LikeId result2 = likeCommandService.create(anotherLike);

        // Assert
        assertNotNull(result1);
        assertNotNull(result2);
        assertEquals(testLike.getId(), result1);
        assertEquals(anotherLike.getId(), result2);
        assertNotEquals(result1, result2);

        verify(likeRepository, times(2)).save(any(Like.class));
        verify(likeRepository, times(1)).save(testLike);
        verify(likeRepository, times(1)).save(anotherLike);
    }

    @Test
    @DisplayName("作成と削除を連続して実行できる")
    void createAndDelete_consecutiveOperations_shouldExecuteSuccessfully() {
        // Arrange
        doNothing().when(likeRepository).save(any(Like.class));
        doNothing().when(likeRepository).delete(any(LikeId.class));

        // Act
        LikeId createdId = likeCommandService.create(testLike);

        // Assert creation
        assertNotNull(createdId);
        assertEquals(testLike.getId(), createdId);

        // Act - delete
        assertDoesNotThrow(() -> likeCommandService.delete(testLike));

        // Assert
        verify(likeRepository, times(1)).save(testLike);
        verify(likeRepository, times(1)).delete(testLike.getId());
    }

    @Test
    @DisplayName("同じユーザーが異なるFaveにいいねを作成できる")
    void create_sameUserDifferentFaves_shouldCreateBothLikes() {
        // Arrange
        UUID anotherFaveId = UUID.randomUUID();
        Like anotherLike = Like.create(
            new UserId(testUserId), // 同じユーザー
            new FaveId(anotherFaveId) // 異なるFave
        );

        doNothing().when(likeRepository).save(any(Like.class));

        // Act
        LikeId result1 = likeCommandService.create(testLike);
        LikeId result2 = likeCommandService.create(anotherLike);

        // Assert
        assertNotNull(result1);
        assertNotNull(result2);
        assertNotEquals(result1, result2);
        assertEquals(testUserId, testLike.getUserId().value());
        assertEquals(testUserId, anotherLike.getUserId().value());
        assertEquals(testFaveId, testLike.getFaveId().value());
        assertEquals(anotherFaveId, anotherLike.getFaveId().value());

        verify(likeRepository, times(2)).save(any(Like.class));
    }

    @Test
    @DisplayName("異なるユーザーが同じFaveにいいねを作成できる")
    void create_differentUsersSameFave_shouldCreateBothLikes() {
        // Arrange
        UUID anotherUserId = UUID.randomUUID();
        Like anotherLike = Like.create(
            new UserId(anotherUserId), // 異なるユーザー
            new FaveId(testFaveId) // 同じFave
        );

        doNothing().when(likeRepository).save(any(Like.class));

        // Act
        LikeId result1 = likeCommandService.create(testLike);
        LikeId result2 = likeCommandService.create(anotherLike);

        // Assert
        assertNotNull(result1);
        assertNotNull(result2);
        assertNotEquals(result1, result2);
        assertEquals(testUserId, testLike.getUserId().value());
        assertEquals(anotherUserId, anotherLike.getUserId().value());
        assertEquals(testFaveId, testLike.getFaveId().value());
        assertEquals(testFaveId, anotherLike.getFaveId().value());

        verify(likeRepository, times(2)).save(any(Like.class));
    }

    @Test
    @DisplayName("再構築されたいいねも正常に削除できる")
    void delete_withReconstructedLike_shouldDeleteSuccessfully() {
        // Arrange
        UUID likeId = UUID.randomUUID();
        LocalDateTime createdAt = LocalDateTime.now().minusHours(1);

        Like reconstructedLike = Like.reconstruct(
            new LikeId(likeId),
            new UserId(testUserId),
            new FaveId(testFaveId),
            createdAt
        );

        doNothing().when(likeRepository).delete(any(LikeId.class));

        // Act & Assert
        assertDoesNotThrow(() -> likeCommandService.delete(reconstructedLike));

        verify(likeRepository, times(1)).delete(new LikeId(likeId));
    }
}
