package com.favick.application.service.like.query;

import com.favick.application.port.out.feature.like.repository.LikeRepository;
import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.like.model.Like;
import com.favick.domain.feature.like.model.LikeId;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("LikeQueryService のテスト")
class LikeQueryServiceTest {

    @Mock
    private LikeRepository likeRepository;

    @InjectMocks
    private LikeQueryService likeQueryService;

    private UUID testUserId;
    private UUID testFaveId;
    private UUID anotherFaveId;
    private Like testLike;

    @BeforeEach
    void setUp() {
        testUserId = UUID.randomUUID();
        testFaveId = UUID.randomUUID();
        anotherFaveId = UUID.randomUUID();

        testLike = Like.reconstruct(
            new LikeId(UUID.randomUUID()),
            new UserId(testUserId),
            new FaveId(testFaveId),
            LocalDateTime.now()
        );
    }

    @Test
    @DisplayName("ユーザーIDとFaveIDでいいねを正常に取得できる")
    void getByUserIdAndFaveId_withExistingLike_shouldReturnLike() {
        // Arrange
        UserId userId = new UserId(testUserId);
        FaveId faveId = new FaveId(testFaveId);

        when(likeRepository.findByUserIdAndFaveId(eq(userId), eq(faveId)))
            .thenReturn(Optional.of(testLike));

        // Act
        Optional<Like> result = likeQueryService.getByUserIdAndFaveId(userId, faveId);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(testLike, result.get());
        assertEquals(testUserId, result.get().getUserId().value());
        assertEquals(testFaveId, result.get().getFaveId().value());

        verify(likeRepository, times(1)).findByUserIdAndFaveId(userId, faveId);
    }

    @Test
    @DisplayName("存在しないいいねを検索すると空のOptionalが返る")
    void getByUserIdAndFaveId_withNonExistingLike_shouldReturnEmptyOptional() {
        // Arrange
        UserId userId = new UserId(testUserId);
        FaveId faveId = new FaveId(testFaveId);

        when(likeRepository.findByUserIdAndFaveId(eq(userId), eq(faveId)))
            .thenReturn(Optional.empty());

        // Act
        Optional<Like> result = likeQueryService.getByUserIdAndFaveId(userId, faveId);

        // Assert
        assertFalse(result.isPresent());

        verify(likeRepository, times(1)).findByUserIdAndFaveId(userId, faveId);
    }

    @Test
    @DisplayName("ユーザーがいいねしたお気に入りIDのセットを正常に取得できる")
    void listLikedFaveIdsByUserIdAndFaveIds_withLikedFaves_shouldReturnLikedFaveIds() {
        // Arrange
        UserId userId = new UserId(testUserId);
        UUID thirdFaveId = UUID.randomUUID();

        Set<FaveId> faveIds = Set.of(
            new FaveId(testFaveId),
            new FaveId(anotherFaveId),
            new FaveId(thirdFaveId)
        );

        Like anotherLike = Like.reconstruct(
            new LikeId(UUID.randomUUID()),
            new UserId(testUserId),
            new FaveId(anotherFaveId),
            LocalDateTime.now()
        );

        List<Like> likedFaves = List.of(testLike, anotherLike);

        when(likeRepository.findByUserIdAndFaveIds(eq(userId), eq(faveIds)))
            .thenReturn(likedFaves);

        // Act
        Set<UUID> result = likeQueryService.listLikedFaveIdsByUserIdAndFaveIds(userId, faveIds);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(testFaveId));
        assertTrue(result.contains(anotherFaveId));
        assertFalse(result.contains(thirdFaveId));

        verify(likeRepository, times(1)).findByUserIdAndFaveIds(userId, faveIds);
    }

    @Test
    @DisplayName("いいねが存在しない場合は空のセットが返る")
    void listLikedFaveIdsByUserIdAndFaveIds_withNoLikes_shouldReturnEmptySet() {
        // Arrange
        UserId userId = new UserId(testUserId);
        Set<FaveId> faveIds = Set.of(
            new FaveId(testFaveId),
            new FaveId(anotherFaveId)
        );

        when(likeRepository.findByUserIdAndFaveIds(eq(userId), eq(faveIds)))
            .thenReturn(List.of());

        // Act
        Set<UUID> result = likeQueryService.listLikedFaveIdsByUserIdAndFaveIds(userId, faveIds);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(likeRepository, times(1)).findByUserIdAndFaveIds(userId, faveIds);
    }

    @Test
    @DisplayName("空のFaveIDセットを渡すと空のセットが返る")
    void listLikedFaveIdsByUserIdAndFaveIds_withEmptyFaveIds_shouldReturnEmptySet() {
        // Arrange
        UserId userId = new UserId(testUserId);
        Set<FaveId> emptyFaveIds = Set.of();

        // Act
        Set<UUID> result = likeQueryService.listLikedFaveIdsByUserIdAndFaveIds(userId, emptyFaveIds);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // リポジトリは呼ばれない
        verify(likeRepository, never()).findByUserIdAndFaveIds(any(), any());
    }

    @Test
    @DisplayName("単一のFaveIDでいいねを検索できる")
    void listLikedFaveIdsByUserIdAndFaveIds_withSingleFaveId_shouldReturnCorrectResult() {
        // Arrange
        UserId userId = new UserId(testUserId);
        Set<FaveId> singleFaveId = Set.of(new FaveId(testFaveId));

        when(likeRepository.findByUserIdAndFaveIds(eq(userId), eq(singleFaveId)))
            .thenReturn(List.of(testLike));

        // Act
        Set<UUID> result = likeQueryService.listLikedFaveIdsByUserIdAndFaveIds(userId, singleFaveId);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.contains(testFaveId));

        verify(likeRepository, times(1)).findByUserIdAndFaveIds(userId, singleFaveId);
    }

    @Test
    @DisplayName("異なるユーザーのいいねは取得されない")
    void getByUserIdAndFaveId_withDifferentUser_shouldNotReturnLike() {
        // Arrange
        UUID differentUserId = UUID.randomUUID();
        UserId userId = new UserId(differentUserId);
        FaveId faveId = new FaveId(testFaveId);

        when(likeRepository.findByUserIdAndFaveId(eq(userId), eq(faveId)))
            .thenReturn(Optional.empty());

        // Act
        Optional<Like> result = likeQueryService.getByUserIdAndFaveId(userId, faveId);

        // Assert
        assertFalse(result.isPresent());

        verify(likeRepository, times(1)).findByUserIdAndFaveId(userId, faveId);
    }

    @Test
    @DisplayName("大量のFaveIDでも正常に処理できる")
    void listLikedFaveIdsByUserIdAndFaveIds_withManyFaveIds_shouldHandleCorrectly() {
        // Arrange
        UserId userId = new UserId(testUserId);
        Set<FaveId> manyFaveIds = new HashSet<>();
        List<Like> someLikes = new ArrayList<>();

        // 10個のFaveIDを作成し、そのうち3個にいいねがある状態をシミュレート
        for (int i = 0; i < 10; i++) {
            UUID faveId = UUID.randomUUID();
            manyFaveIds.add(new FaveId(faveId));

            if (i < 3) {
                Like like = Like.reconstruct(
                    new LikeId(UUID.randomUUID()),
                    userId,
                    new FaveId(faveId),
                    LocalDateTime.now()
                );
                someLikes.add(like);
            }
        }

        when(likeRepository.findByUserIdAndFaveIds(eq(userId), eq(manyFaveIds)))
            .thenReturn(someLikes);

        // Act
        Set<UUID> result = likeQueryService.listLikedFaveIdsByUserIdAndFaveIds(userId, manyFaveIds);

        // Assert
        assertNotNull(result);
        assertEquals(3, result.size());

        verify(likeRepository, times(1)).findByUserIdAndFaveIds(userId, manyFaveIds);
    }
}
