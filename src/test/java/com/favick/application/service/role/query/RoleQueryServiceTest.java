package com.favick.application.service.role.query;

import com.favick.application.port.out.feature.role.repository.RoleRepository;
import com.favick.domain.feature.role.model.Role;
import com.favick.domain.feature.role.model.RoleId;
import com.favick.domain.feature.role.model.RoleName;
import com.favick.domain.feature.role.model.RoleNameValue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@DisplayName("RoleQueryService のテスト")
class RoleQueryServiceTest {

    @Mock
    private RoleRepository roleRepository;

    @InjectMocks
    private RoleQueryService roleQueryService;

    private RoleId testRoleId;
    private Role testRole;

    @BeforeEach
    void setUp() {
        testRoleId = new RoleId(UUID.randomUUID());
        testRole = createTestRole();
    }

    private Role createTestRole() {
        RoleName roleName = new RoleName(RoleNameValue.ROLE_ADMIN);
        return Role.create(roleName);
    }

    @Test
    @DisplayName("IDで権限を正常に取得できる")
    void getById_withExistingRole_shouldReturnRole() {
        // Arrange
        when(roleRepository.findById(testRoleId))
            .thenReturn(Optional.of(testRole));

        // Act
        Optional<Role> result = roleQueryService.getById(testRoleId);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(testRole, result.get());
        assertEquals(RoleNameValue.ROLE_ADMIN, result.get().getName().value());
        verify(roleRepository).findById(testRoleId);
    }

    @Test
    @DisplayName("存在しないIDで権限を取得しようとした場合は空のOptionalを返す")
    void getById_withNonExistentRole_shouldReturnEmptyOptional() {
        // Arrange
        RoleId nonExistentId = new RoleId(UUID.randomUUID());
        when(roleRepository.findById(nonExistentId))
            .thenReturn(Optional.empty());

        // Act
        Optional<Role> result = roleQueryService.getById(nonExistentId);

        // Assert
        assertTrue(result.isEmpty());
        verify(roleRepository).findById(nonExistentId);
    }

    @Test
    @DisplayName("管理者権限を正常に取得できる")
    void getById_withAdminRole_shouldReturnAdminRole() {
        // Arrange
        Role adminRole = createTestRole();
        RoleId adminRoleId = new RoleId(UUID.randomUUID());

        when(roleRepository.findById(adminRoleId))
            .thenReturn(Optional.of(adminRole));

        // Act
        Optional<Role> result = roleQueryService.getById(adminRoleId);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(adminRole, result.get());
        assertEquals(RoleNameValue.ROLE_ADMIN, result.get().getName().value());
        verify(roleRepository).findById(adminRoleId);
    }

    @Test
    @DisplayName("ユーザー権限を正常に取得できる")
    void getById_withUserRole_shouldReturnUserRole() {
        // Arrange
        Role userRole = createTestRole();
        RoleId userRoleId = new RoleId(UUID.randomUUID());

        when(roleRepository.findById(userRoleId))
            .thenReturn(Optional.of(userRole));

        // Act
        Optional<Role> result = roleQueryService.getById(userRoleId);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(userRole, result.get());
        assertEquals(RoleNameValue.ROLE_ADMIN, result.get().getName().value());
        verify(roleRepository).findById(userRoleId);
    }

    @Test
    @DisplayName("異なる権限IDでそれぞれ正しい権限を取得できる")
    void getById_withDifferentRoleIds_shouldReturnCorrectRoles() {
        // Arrange
        RoleId adminRoleId = new RoleId(UUID.randomUUID());
        RoleId userRoleId = new RoleId(UUID.randomUUID());

        Role adminRole = createTestRole();
        Role userRole = createTestRole();

        when(roleRepository.findById(adminRoleId)).thenReturn(Optional.of(adminRole));
        when(roleRepository.findById(userRoleId)).thenReturn(Optional.of(userRole));

        // Act
        Optional<Role> adminResult = roleQueryService.getById(adminRoleId);
        Optional<Role> userResult = roleQueryService.getById(userRoleId);

        // Assert
        assertTrue(adminResult.isPresent());
        assertTrue(userResult.isPresent());

        assertEquals(adminRole, adminResult.get());
        assertEquals(userRole, userResult.get());

        assertEquals(RoleNameValue.ROLE_ADMIN, adminResult.get().getName().value());
        assertEquals(RoleNameValue.ROLE_ADMIN, userResult.get().getName().value());

        verify(roleRepository).findById(adminRoleId);
        verify(roleRepository).findById(userRoleId);
    }

    @Test
    @DisplayName("リポジトリでエラーが発生した場合は例外が伝播される")
    void getById_whenRepositoryThrowsException_shouldPropagateException() {
        // Arrange
        RuntimeException expectedException = new RuntimeException("Repository error");
        when(roleRepository.findById(testRoleId))
            .thenThrow(expectedException);

        // Act & Assert
        RuntimeException thrownException = assertThrows(
            RuntimeException.class,
            () -> roleQueryService.getById(testRoleId)
        );

        assertEquals("Repository error", thrownException.getMessage());
        verify(roleRepository).findById(testRoleId);
    }

    @Test
    @DisplayName("nullのIDで検索しても適切に処理される")
    void getById_withNullId_shouldHandleGracefully() {
        // Arrange
        when(roleRepository.findById(null))
            .thenReturn(Optional.empty());

        // Act
        Optional<Role> result = roleQueryService.getById(null);

        // Assert
        assertTrue(result.isEmpty());
        verify(roleRepository).findById(null);
    }
}
