package com.favick.application.service.token.command;

import com.favick.application.port.out.feature.token.repository.PasswordResetTokenRepository;
import com.favick.domain.feature.token.model.PasswordResetToken;
import com.favick.domain.feature.token.model.PasswordResetTokenId;
import com.favick.domain.feature.token.model.TokenExpireAt;
import com.favick.domain.feature.token.model.TokenValue;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("PasswordResetTokenCommandService のテスト")
class PasswordResetTokenCommandServiceTest {

    @Mock
    private PasswordResetTokenRepository passwordResetTokenRepository;

    @InjectMocks
    private PasswordResetTokenCommandService passwordResetTokenCommandService;

    @Test
    @DisplayName("正常系：存在するトークンIDで削除が成功する")
    void delete_existingTokenId_shouldDeleteToken() {
        // Arrange
        UUID tokenId = UUID.randomUUID();
        UUID userId = UUID.randomUUID();
        PasswordResetTokenId passwordResetTokenId = new PasswordResetTokenId(tokenId);

        PasswordResetToken passwordResetToken = PasswordResetToken.reconstruct(
            passwordResetTokenId,
            new UserId(userId),
            new TokenValue("test-token"),
            new TokenExpireAt(LocalDateTime.now().plusHours(2)),
            LocalDateTime.now()
        );

        // Act
        passwordResetTokenCommandService.delete(passwordResetToken);

        // Assert
        verify(passwordResetTokenRepository, times(1)).delete(passwordResetTokenId);
    }

    @Test
    @DisplayName("異常系：nullのトークンで削除するとNullPointerExceptionが発生する")
    @SuppressWarnings("DataFlowIssue")
    void delete_nullToken_shouldThrowNullPointerException() {
        // Act & Assert
        assertThrows(NullPointerException.class, () -> passwordResetTokenCommandService.delete(null));
        verify(passwordResetTokenRepository, never()).delete(any());
    }


    @Test
    @DisplayName("正常系：期限切れトークンでも削除が成功する")
    void delete_expiredToken_shouldDeleteToken() {
        // Arrange
        UUID tokenId = UUID.randomUUID();
        UUID userId = UUID.randomUUID();
        PasswordResetTokenId passwordResetTokenId = new PasswordResetTokenId(tokenId);

        // 期限切れのトークンを作成
        PasswordResetToken expiredToken = PasswordResetToken.reconstruct(
            passwordResetTokenId,
            new UserId(userId),
            new TokenValue("expired-token"),
            new TokenExpireAt(LocalDateTime.now().minusHours(1)), // 1時間前に期限切れ
            LocalDateTime.now().minusHours(3)
        );

        // Act
        passwordResetTokenCommandService.delete(expiredToken);

        // Assert
        verify(passwordResetTokenRepository, times(1)).delete(passwordResetTokenId);
    }

    @Test
    @DisplayName("正常系：同じトークンの複数回削除が適切に処理される")
    void delete_multipleCallsWithSameToken_shouldHandleAppropriately() {
        // Arrange
        UUID tokenId = UUID.randomUUID();
        UUID userId = UUID.randomUUID();
        PasswordResetTokenId passwordResetTokenId = new PasswordResetTokenId(tokenId);

        PasswordResetToken passwordResetToken = PasswordResetToken.reconstruct(
            passwordResetTokenId,
            new UserId(userId),
            new TokenValue("test-token"),
            new TokenExpireAt(LocalDateTime.now().plusHours(2)),
            LocalDateTime.now()
        );

        // Act
        passwordResetTokenCommandService.delete(passwordResetToken);
        passwordResetTokenCommandService.delete(passwordResetToken);

        // Assert
        verify(passwordResetTokenRepository, times(2)).delete(passwordResetTokenId);
    }

    @Test
    @DisplayName("正常系：異なるトークンIDで複数削除が成功する")
    void delete_multipleDifferentTokenIds_shouldDeleteAll() {
        // Arrange
        UUID tokenId1 = UUID.randomUUID();
        UUID tokenId2 = UUID.randomUUID();
        UUID userId = UUID.randomUUID();

        PasswordResetTokenId passwordResetTokenId1 = new PasswordResetTokenId(tokenId1);
        PasswordResetTokenId passwordResetTokenId2 = new PasswordResetTokenId(tokenId2);

        PasswordResetToken token1 = PasswordResetToken.reconstruct(
            passwordResetTokenId1,
            new UserId(userId),
            new TokenValue("test-token-1"),
            new TokenExpireAt(LocalDateTime.now().plusHours(2)),
            LocalDateTime.now()
        );

        PasswordResetToken token2 = PasswordResetToken.reconstruct(
            passwordResetTokenId2,
            new UserId(userId),
            new TokenValue("test-token-2"),
            new TokenExpireAt(LocalDateTime.now().plusHours(2)),
            LocalDateTime.now()
        );

        // Act
        passwordResetTokenCommandService.delete(token1);
        passwordResetTokenCommandService.delete(token2);

        // Assert
        verify(passwordResetTokenRepository, times(1)).delete(passwordResetTokenId1);
        verify(passwordResetTokenRepository, times(1)).delete(passwordResetTokenId2);
    }
}
