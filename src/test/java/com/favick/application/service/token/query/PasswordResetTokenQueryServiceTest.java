package com.favick.application.service.token.query;

import com.favick.application.port.out.feature.token.repository.PasswordResetTokenRepository;
import com.favick.domain.feature.token.model.PasswordResetToken;
import com.favick.domain.feature.token.model.PasswordResetTokenId;
import com.favick.domain.feature.token.model.TokenExpireAt;
import com.favick.domain.feature.token.model.TokenValue;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("PasswordResetTokenQueryService のテスト")
class PasswordResetTokenQueryServiceTest {

    @Mock
    private PasswordResetTokenRepository passwordResetTokenRepository;

    @InjectMocks
    private PasswordResetTokenQueryService passwordResetTokenQueryService;


    @Test
    @DisplayName("正常系：有効なトークンで取得が成功する")
    void getByToken_validToken_shouldReturnOptionalToken() {
        // Arrange
        String tokenValue = "valid-token";
        UUID tokenId = UUID.randomUUID();
        UUID userId = UUID.randomUUID();
        TokenValue token = new TokenValue(tokenValue);

        PasswordResetToken passwordResetToken = PasswordResetToken.reconstruct(
            new PasswordResetTokenId(tokenId),
            new UserId(userId),
            token,
            new TokenExpireAt(LocalDateTime.now().plusHours(2)), // 2時間後に期限切れ
            LocalDateTime.now()
        );

        when(passwordResetTokenRepository.findByToken(token))
            .thenReturn(Optional.of(passwordResetToken));

        // Act
        Optional<PasswordResetToken> result = passwordResetTokenQueryService.getByToken(token);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(tokenId, result.get().getId().value());
        assertEquals(userId, result.get().getUserId().value());
        assertEquals(tokenValue, result.get().getToken().value());

        verify(passwordResetTokenRepository, times(1)).findByToken(token);
    }


}
