package com.favick.application.service.token.query;

import com.favick.application.port.out.feature.token.repository.VerificationTokenRepository;
import com.favick.domain.feature.token.model.*;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("VerificationTokenQueryService のテスト")
class VerificationTokenQueryServiceTest {

    @Mock
    private VerificationTokenRepository verificationTokenRepository;

    @InjectMocks
    private VerificationTokenQueryService verificationTokenQueryService;

    private TokenValue testTokenValue;
    private VerificationToken testToken;

    @BeforeEach
    void setUp() {
        testTokenValue = new TokenValue("test-verification-token");
        testToken = createTestVerificationToken();
    }

    private VerificationToken createTestVerificationToken() {
        VerificationTokenId tokenId = new VerificationTokenId(UUID.randomUUID());
        UserId userId = new UserId(UUID.randomUUID());
        TokenExpireAt expireAt = new TokenExpireAt(LocalDateTime.now().plusHours(24));
        LocalDateTime createdAt = LocalDateTime.now();

        return VerificationToken.reconstruct(
            tokenId,
            userId,
            testTokenValue,
            expireAt,
            createdAt
        );
    }

    @Test
    @DisplayName("トークン値で認証トークンを正常に取得できる")
    void getByToken_withExistingToken_shouldReturnToken() {
        // Arrange
        when(verificationTokenRepository.findByToken(testTokenValue))
            .thenReturn(Optional.of(testToken));

        // Act
        Optional<VerificationToken> result = verificationTokenQueryService.getByToken(testTokenValue);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(testToken, result.get());
        assertEquals(testTokenValue, result.get().getToken());
        verify(verificationTokenRepository).findByToken(testTokenValue);
    }

    @Test
    @DisplayName("存在しないトークン値で認証トークンを取得しようとした場合は空のOptionalを返す")
    void getByToken_withNonExistentToken_shouldReturnEmptyOptional() {
        // Arrange
        TokenValue nonExistentToken = new TokenValue("nonexistent-token");
        when(verificationTokenRepository.findByToken(nonExistentToken))
            .thenReturn(Optional.empty());

        // Act
        Optional<VerificationToken> result = verificationTokenQueryService.getByToken(nonExistentToken);

        // Assert
        assertTrue(result.isEmpty());
        verify(verificationTokenRepository).findByToken(nonExistentToken);
    }

    @Test
    @DisplayName("異なるトークン値でそれぞれ正しい認証トークンを取得できる")
    void getByToken_withDifferentTokenValues_shouldReturnCorrectTokens() {
        // Arrange
        TokenValue token1 = new TokenValue("token-1");
        TokenValue token2 = new TokenValue("token-2");

        VerificationToken verificationToken1 = VerificationToken.reconstruct(
            new VerificationTokenId(UUID.randomUUID()),
            new UserId(UUID.randomUUID()),
            token1,
            new TokenExpireAt(LocalDateTime.now().plusHours(24)),
            LocalDateTime.now()
        );

        VerificationToken verificationToken2 = VerificationToken.reconstruct(
            new VerificationTokenId(UUID.randomUUID()),
            new UserId(UUID.randomUUID()),
            token2,
            new TokenExpireAt(LocalDateTime.now().plusHours(24)),
            LocalDateTime.now()
        );

        when(verificationTokenRepository.findByToken(token1)).thenReturn(Optional.of(verificationToken1));
        when(verificationTokenRepository.findByToken(token2)).thenReturn(Optional.of(verificationToken2));

        // Act
        Optional<VerificationToken> result1 = verificationTokenQueryService.getByToken(token1);
        Optional<VerificationToken> result2 = verificationTokenQueryService.getByToken(token2);

        // Assert
        assertTrue(result1.isPresent());
        assertTrue(result2.isPresent());
        assertEquals(verificationToken1, result1.get());
        assertEquals(verificationToken2, result2.get());
        assertEquals(token1, result1.get().getToken());
        assertEquals(token2, result2.get().getToken());

        verify(verificationTokenRepository).findByToken(token1);
        verify(verificationTokenRepository).findByToken(token2);
    }

    @Test
    @DisplayName("期限切れのトークンでも正常に取得できる")
    void getByToken_withExpiredToken_shouldReturnToken() {
        // Arrange
        VerificationToken expiredToken = VerificationToken.reconstruct(
            new VerificationTokenId(UUID.randomUUID()),
            new UserId(UUID.randomUUID()),
            testTokenValue,
            new TokenExpireAt(LocalDateTime.now().minusHours(1)), // 1時間前に期限切れ
            LocalDateTime.now().minusHours(25)
        );

        when(verificationTokenRepository.findByToken(testTokenValue))
            .thenReturn(Optional.of(expiredToken));

        // Act
        Optional<VerificationToken> result = verificationTokenQueryService.getByToken(testTokenValue);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(expiredToken, result.get());
        verify(verificationTokenRepository).findByToken(testTokenValue);
    }

    @Test
    @DisplayName("リポジトリでエラーが発生した場合は例外が伝播される")
    void getByToken_whenRepositoryThrowsException_shouldPropagateException() {
        // Arrange
        RuntimeException expectedException = new RuntimeException("Repository error");
        when(verificationTokenRepository.findByToken(testTokenValue))
            .thenThrow(expectedException);

        // Act & Assert
        RuntimeException thrownException = assertThrows(
            RuntimeException.class,
            () -> verificationTokenQueryService.getByToken(testTokenValue)
        );

        assertEquals("Repository error", thrownException.getMessage());
        verify(verificationTokenRepository).findByToken(testTokenValue);
    }

    @Test
    @DisplayName("複数回同じトークンで検索しても同じ結果を返す")
    void getByToken_calledMultipleTimes_shouldReturnSameResult() {
        // Arrange
        when(verificationTokenRepository.findByToken(testTokenValue))
            .thenReturn(Optional.of(testToken));

        // Act
        Optional<VerificationToken> result1 = verificationTokenQueryService.getByToken(testTokenValue);
        Optional<VerificationToken> result2 = verificationTokenQueryService.getByToken(testTokenValue);

        // Assert
        assertTrue(result1.isPresent());
        assertTrue(result2.isPresent());
        assertEquals(result1.get(), result2.get());

        verify(verificationTokenRepository, times(2)).findByToken(testTokenValue);
    }
}
