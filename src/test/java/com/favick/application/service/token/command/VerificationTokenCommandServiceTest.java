package com.favick.application.service.token.command;

import com.favick.application.port.out.feature.token.repository.VerificationTokenRepository;
import com.favick.domain.feature.token.model.TokenExpireAt;
import com.favick.domain.feature.token.model.TokenValue;
import com.favick.domain.feature.token.model.VerificationToken;
import com.favick.domain.feature.token.model.VerificationTokenId;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("VerificationTokenCommandService のテスト")
class VerificationTokenCommandServiceTest {

    @Mock
    private VerificationTokenRepository verificationTokenRepository;

    @InjectMocks
    private VerificationTokenCommandService verificationTokenCommandService;

    @Test
    @DisplayName("正常系：存在するトークンで削除が成功する")
    void delete_withValidToken_shouldDeleteToken() {
        // Arrange
        UUID tokenId = UUID.randomUUID();
        UUID userId = UUID.randomUUID();
        VerificationTokenId verificationTokenId = new VerificationTokenId(tokenId);

        VerificationToken verificationToken = VerificationToken.reconstruct(
            verificationTokenId,
            new UserId(userId),
            new TokenValue("test-token"),
            new TokenExpireAt(LocalDateTime.now().plusHours(24)),
            LocalDateTime.now()
        );

        // Act
        verificationTokenCommandService.delete(verificationToken);

        // Assert
        verify(verificationTokenRepository, times(1)).delete(verificationTokenId);
    }

    @Test
    @DisplayName("異常系：nullのトークンで削除するとNullPointerExceptionが発生する")
    @SuppressWarnings("DataFlowIssue")
    void delete_withNullToken_shouldThrowNullPointerException() {
        // Act & Assert
        assertThrows(NullPointerException.class, () -> verificationTokenCommandService.delete(null));
        verify(verificationTokenRepository, never()).delete(any());
    }

    @Test
    @DisplayName("正常系：期限切れトークンでも削除が成功する")
    void delete_withExpiredToken_shouldDeleteToken() {
        // Arrange
        UUID tokenId = UUID.randomUUID();
        UUID userId = UUID.randomUUID();
        VerificationTokenId verificationTokenId = new VerificationTokenId(tokenId);

        // 期限切れのトークンを作成
        VerificationToken expiredToken = VerificationToken.reconstruct(
            verificationTokenId,
            new UserId(userId),
            new TokenValue("expired-token"),
            new TokenExpireAt(LocalDateTime.now().minusHours(1)), // 1時間前に期限切れ
            LocalDateTime.now().minusHours(25)
        );

        // Act
        verificationTokenCommandService.delete(expiredToken);

        // Assert
        verify(verificationTokenRepository, times(1)).delete(verificationTokenId);
    }

    @Test
    @DisplayName("正常系：同じトークンの複数回削除が適切に処理される")
    void delete_withMultipleCallsForSameToken_shouldHandleAppropriately() {
        // Arrange
        UUID tokenId = UUID.randomUUID();
        UUID userId = UUID.randomUUID();
        VerificationTokenId verificationTokenId = new VerificationTokenId(tokenId);

        VerificationToken verificationToken = VerificationToken.reconstruct(
            verificationTokenId,
            new UserId(userId),
            new TokenValue("test-token"),
            new TokenExpireAt(LocalDateTime.now().plusHours(24)),
            LocalDateTime.now()
        );

        // Act
        verificationTokenCommandService.delete(verificationToken);
        verificationTokenCommandService.delete(verificationToken);

        // Assert
        verify(verificationTokenRepository, times(2)).delete(verificationTokenId);
    }

    @Test
    @DisplayName("正常系：異なるトークンIDで複数削除が成功する")
    void delete_withMultipleDifferentTokens_shouldDeleteAll() {
        // Arrange
        UUID tokenId1 = UUID.randomUUID();
        UUID tokenId2 = UUID.randomUUID();
        UUID userId = UUID.randomUUID();

        VerificationTokenId verificationTokenId1 = new VerificationTokenId(tokenId1);
        VerificationTokenId verificationTokenId2 = new VerificationTokenId(tokenId2);

        VerificationToken token1 = VerificationToken.reconstruct(
            verificationTokenId1,
            new UserId(userId),
            new TokenValue("test-token-1"),
            new TokenExpireAt(LocalDateTime.now().plusHours(24)),
            LocalDateTime.now()
        );

        VerificationToken token2 = VerificationToken.reconstruct(
            verificationTokenId2,
            new UserId(userId),
            new TokenValue("test-token-2"),
            new TokenExpireAt(LocalDateTime.now().plusHours(24)),
            LocalDateTime.now()
        );

        // Act
        verificationTokenCommandService.delete(token1);
        verificationTokenCommandService.delete(token2);

        // Assert
        verify(verificationTokenRepository, times(1)).delete(verificationTokenId1);
        verify(verificationTokenRepository, times(1)).delete(verificationTokenId2);
    }

    @Test
    @DisplayName("正常系：リポジトリエラーが発生した場合は例外が伝播される")
    void delete_whenRepositoryThrowsException_shouldPropagateException() {
        // Arrange
        UUID tokenId = UUID.randomUUID();
        UUID userId = UUID.randomUUID();
        VerificationTokenId verificationTokenId = new VerificationTokenId(tokenId);

        VerificationToken verificationToken = VerificationToken.reconstruct(
            verificationTokenId,
            new UserId(userId),
            new TokenValue("test-token"),
            new TokenExpireAt(LocalDateTime.now().plusHours(24)),
            LocalDateTime.now()
        );

        RuntimeException expectedException = new RuntimeException("Repository error");
        doThrow(expectedException).when(verificationTokenRepository).delete(verificationTokenId);

        // Act & Assert
        RuntimeException thrownException = assertThrows(
            RuntimeException.class,
            () -> verificationTokenCommandService.delete(verificationToken)
        );

        assertEquals("Repository error", thrownException.getMessage());
        verify(verificationTokenRepository).delete(verificationTokenId);
    }
}
