package com.favick.application.service.fave.command;

import com.favick.application.port.out.feature.fave.repository.FaveRepository;
import com.favick.domain.feature.fave.model.Fave;
import com.favick.domain.feature.fave.model.FaveContent;
import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("FaveCommandService のテスト")
class FaveCommandServiceTest {

    @Mock
    private FaveRepository faveRepository;

    @InjectMocks
    private FaveCommandService faveCommandService;

    private UUID testUserId;
    private UUID testThemeId;

    @BeforeEach
    void setUp() {
        testUserId = UUID.randomUUID();
        testThemeId = UUID.randomUUID();
    }

    @Test
    @DisplayName("新しいお気に入りを正常に作成できる")
    void create_withValidFave_shouldCreateFaveSuccessfully() {
        // Arrange
        UserId userId = new UserId(testUserId);
        ThemeId themeId = new ThemeId(testThemeId);
        FaveContent content = new FaveContent("photos/test-image.jpg");
        
        Fave fave = Fave.create(userId, themeId, content);

        // Act
        FaveId result = faveCommandService.create(fave);

        // Assert
        assertNotNull(result);
        assertEquals(fave.getId(), result);
        verify(faveRepository).save(fave);
    }

    @Test
    @DisplayName("お気に入りを正常に更新できる")
    void update_withValidFave_shouldUpdateFaveSuccessfully() {
        // Arrange
        UserId userId = new UserId(testUserId);
        ThemeId themeId = new ThemeId(testThemeId);
        FaveContent content = new FaveContent("photos/updated-image.jpg");
        
        Fave fave = Fave.create(userId, themeId, content);

        // Act
        FaveId result = faveCommandService.update(fave);

        // Assert
        assertNotNull(result);
        assertEquals(fave.getId(), result);
        verify(faveRepository).save(fave);
    }

    @Test
    @DisplayName("リポジトリでエラーが発生した場合は例外が伝播される")
    void create_whenRepositoryThrowsException_shouldPropagateException() {
        // Arrange
        UserId userId = new UserId(testUserId);
        ThemeId themeId = new ThemeId(testThemeId);
        FaveContent content = new FaveContent("photos/test-image.jpg");
        
        Fave fave = Fave.create(userId, themeId, content);
        
        RuntimeException expectedException = new RuntimeException("Repository error");
        doThrow(expectedException).when(faveRepository).save(fave);

        // Act & Assert
        RuntimeException thrownException = assertThrows(
            RuntimeException.class,
            () -> faveCommandService.create(fave)
        );
        
        assertEquals("Repository error", thrownException.getMessage());
        verify(faveRepository).save(fave);
    }

    @Test
    @DisplayName("更新処理でリポジトリエラーが発生した場合は例外が伝播される")
    void update_whenRepositoryThrowsException_shouldPropagateException() {
        // Arrange
        UserId userId = new UserId(testUserId);
        ThemeId themeId = new ThemeId(testThemeId);
        FaveContent content = new FaveContent("photos/test-image.jpg");
        
        Fave fave = Fave.create(userId, themeId, content);
        
        RuntimeException expectedException = new RuntimeException("Repository error");
        doThrow(expectedException).when(faveRepository).save(fave);

        // Act & Assert
        RuntimeException thrownException = assertThrows(
            RuntimeException.class,
            () -> faveCommandService.update(fave)
        );
        
        assertEquals("Repository error", thrownException.getMessage());
        verify(faveRepository).save(fave);
    }
}