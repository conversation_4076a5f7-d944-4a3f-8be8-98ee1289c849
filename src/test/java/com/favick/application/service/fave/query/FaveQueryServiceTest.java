package com.favick.application.service.fave.query;

import com.favick.application.port.out.feature.fave.repository.FaveRepository;
import com.favick.domain.feature.fave.model.Fave;
import com.favick.domain.feature.fave.model.FaveContent;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@DisplayName("FaveQueryService のテスト")
class FaveQueryServiceTest {

    @Mock
    private FaveRepository faveRepository;

    @InjectMocks
    private FaveQueryService faveQueryService;

    private ThemeId themeId;

    @BeforeEach
    void setUp() {
        UUID testThemeId = UUID.randomUUID();
        themeId = new ThemeId(testThemeId);
    }

    @Test
    @DisplayName("テーマIDで承認済みお気に入り一覧を正常に取得できる")
    void listApprovedByThemeId_withExistingFaves_shouldReturnFaveList() {
        // Arrange
        UUID user1Id = UUID.randomUUID();
        UUID user2Id = UUID.randomUUID();

        Fave fave1 = Fave.create(
            new UserId(user1Id),
            themeId,
            new FaveContent("photos/image1.jpg")
        );
        Fave fave2 = Fave.create(
            new UserId(user2Id),
            themeId,
            new FaveContent("photos/image2.jpg")
        );

        List<Fave> expectedFaves = List.of(fave1, fave2);

        when(faveRepository.findAllApprovedByThemeId(themeId))
            .thenReturn(expectedFaves);

        // Act
        List<Fave> result = faveQueryService.listApprovedByThemeId(themeId);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(expectedFaves, result);
        verify(faveRepository).findAllApprovedByThemeId(themeId);
    }

    @Test
    @DisplayName("承認済みお気に入りが存在しない場合は空のリストを返す")
    void listApprovedByThemeId_withNoApprovedFaves_shouldReturnEmptyList() {
        // Arrange
        when(faveRepository.findAllApprovedByThemeId(themeId))
            .thenReturn(List.of());

        // Act
        List<Fave> result = faveQueryService.listApprovedByThemeId(themeId);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(faveRepository).findAllApprovedByThemeId(themeId);
    }


    @Test
    @DisplayName("承認済み一覧取得でリポジトリエラーが発生した場合は例外が伝播される")
    void listApprovedByThemeId_whenRepositoryThrowsException_shouldPropagateException() {
        // Arrange
        RuntimeException expectedException = new RuntimeException("Repository error");
        when(faveRepository.findAllApprovedByThemeId(themeId))
            .thenThrow(expectedException);

        // Act & Assert
        RuntimeException thrownException = assertThrows(
            RuntimeException.class,
            () -> faveQueryService.listApprovedByThemeId(themeId)
        );

        assertEquals("Repository error", thrownException.getMessage());
        verify(faveRepository).findAllApprovedByThemeId(themeId);
    }
}
