package com.favick.adapter.out.mail;

import com.favick.application.port.out.feature.token.repository.PasswordResetTokenRepository;
import com.favick.common.helper.MessageHelper;
import com.favick.domain.feature.token.model.PasswordResetToken;
import com.favick.domain.feature.user.event.PasswordResetEvent;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("PasswordResetEventListener のテスト")
class PasswordResetEventListenerTest {

    @Mock
    private PasswordResetTokenRepository passwordResetTokenRepository;

    @Mock
    private JavaMailSender javaMailSender;

    @Mock
    private MessageHelper messageHelper;

    @InjectMocks
    private PasswordResetEventListener passwordResetEventListener;

    @Test
    @DisplayName("正常系：パスワードリセットイベントを処理してメールを送信する")
    void handlePasswordResetEvent_shouldProcessEventAndSendEmail() {
        // Arrange
        UUID userId = UUID.randomUUID();
        String userEmail = "<EMAIL>";
        String origin = "http://localhost:8080";

        ReflectionTestUtils.setField(passwordResetEventListener, "senderAddress", "<EMAIL>");

        when(messageHelper.getMessage("mail.password_reset.subject")).thenReturn("パスワードリセット");
        when(messageHelper.getMessage("mail.password_reset.body")).thenReturn("以下のリンクをクリックしてパスワードをリセットしてください。\nこのリンクは2時間で有効期限が切れます。");

        PasswordResetEvent event = new PasswordResetEvent(this, userId, userEmail, origin);

        // Act
        passwordResetEventListener.handlePasswordResetEvent(event);

        // Assert
        // MessageHelperが正しく呼び出されることを確認
        verify(messageHelper, times(1)).getMessage("mail.password_reset.subject");
        verify(messageHelper, times(1)).getMessage("mail.password_reset.body");

        // 既存トークンの削除を確認
        verify(passwordResetTokenRepository, times(1)).deleteByUserId(new UserId(userId));

        // 新しいトークンの保存を確認
        ArgumentCaptor<PasswordResetToken> tokenCaptor = ArgumentCaptor.forClass(PasswordResetToken.class);
        verify(passwordResetTokenRepository, times(1)).save(tokenCaptor.capture());

        PasswordResetToken savedToken = tokenCaptor.getValue();
        assertNotNull(savedToken);
        assertEquals(userId, savedToken.getUserId().value());
        assertNotNull(savedToken.getToken());
        assertNotNull(savedToken.getExpireAt());

        // メール送信を確認
        ArgumentCaptor<SimpleMailMessage> messageCaptor = ArgumentCaptor.forClass(SimpleMailMessage.class);
        verify(javaMailSender, times(1)).send(messageCaptor.capture());

        SimpleMailMessage sentMessage = messageCaptor.getValue();
        assertNotNull(sentMessage);
        assertEquals("<EMAIL>", sentMessage.getFrom());
        assertArrayEquals(new String[]{userEmail}, sentMessage.getTo());
        assertEquals("パスワードリセット", sentMessage.getSubject());

        String messageText = sentMessage.getText();
        assertNotNull(messageText);
        assertTrue(messageText.contains("以下のリンクをクリックしてパスワードをリセットしてください。"));
        assertTrue(messageText.contains("このリンクは2時間で有効期限が切れます。"));
        assertTrue(messageText.contains(origin + "/auth/password-reset/verify?token="));
        assertTrue(messageText.contains(savedToken.getToken().value()));
    }

    @Test
    @DisplayName("正常系：既存トークンが削除されてから新しいトークンが作成される")
    void handlePasswordResetEvent_shouldDeleteExistingTokenBeforeCreatingNew() {
        // Arrange
        UUID userId = UUID.randomUUID();
        String userEmail = "<EMAIL>";
        String origin = "https://favick.com";

        ReflectionTestUtils.setField(passwordResetEventListener, "senderAddress", "<EMAIL>");

        when(messageHelper.getMessage("mail.password_reset.subject")).thenReturn("パスワードリセット");
        when(messageHelper.getMessage("mail.password_reset.body")).thenReturn("以下のリンクをクリックしてパスワードをリセットしてください。\nこのリンクは2時間で有効期限が切れます。");

        PasswordResetEvent event = new PasswordResetEvent(this, userId, userEmail, origin);

        // Act
        passwordResetEventListener.handlePasswordResetEvent(event);

        // Assert - 処理順序を確認（削除 → 作成 → メール送信）
        var inOrder = inOrder(passwordResetTokenRepository, javaMailSender);
        inOrder.verify(passwordResetTokenRepository).deleteByUserId(new UserId(userId));
        inOrder.verify(passwordResetTokenRepository).save(any(PasswordResetToken.class));
        inOrder.verify(javaMailSender).send(any(SimpleMailMessage.class));
    }

    @Test
    @DisplayName("正常系：HTTPSのoriginでも正しくURLが生成される")
    void handlePasswordResetEvent_withHttpsOrigin_shouldGenerateCorrectUrl() {
        // Arrange
        UUID userId = UUID.randomUUID();
        String userEmail = "<EMAIL>";
        String origin = "https://favick.com";

        ReflectionTestUtils.setField(passwordResetEventListener, "senderAddress", "<EMAIL>");

        when(messageHelper.getMessage("mail.password_reset.subject")).thenReturn("パスワードリセット");
        when(messageHelper.getMessage("mail.password_reset.body")).thenReturn("以下のリンクをクリックしてパスワードをリセットしてください。\nこのリンクは2時間で有効期限が切れます。");

        PasswordResetEvent event = new PasswordResetEvent(this, userId, userEmail, origin);

        // Act
        passwordResetEventListener.handlePasswordResetEvent(event);

        // Assert
        ArgumentCaptor<SimpleMailMessage> messageCaptor = ArgumentCaptor.forClass(SimpleMailMessage.class);
        verify(javaMailSender, times(1)).send(messageCaptor.capture());

        SimpleMailMessage sentMessage = messageCaptor.getValue();
        String messageText = sentMessage.getText();
        assertNotNull(messageText);
        assertTrue(messageText.contains("https://favick.com/auth/password-reset/verify?token="));
    }

    @Test
    @DisplayName("正常系：メールアドレスが正しく設定される")
    void handlePasswordResetEvent_shouldSetCorrectEmailAddress() {
        // Arrange
        UUID userId = UUID.randomUUID();
        String userEmail = "<EMAIL>";
        String origin = "http://localhost:8080";

        ReflectionTestUtils.setField(passwordResetEventListener, "senderAddress", "<EMAIL>");

        when(messageHelper.getMessage("mail.password_reset.subject")).thenReturn("パスワードリセット");
        when(messageHelper.getMessage("mail.password_reset.body")).thenReturn("以下のリンクをクリックしてパスワードをリセットしてください。\nこのリンクは2時間で有効期限が切れます。");

        PasswordResetEvent event = new PasswordResetEvent(this, userId, userEmail, origin);

        // Act
        passwordResetEventListener.handlePasswordResetEvent(event);

        // Assert
        ArgumentCaptor<SimpleMailMessage> messageCaptor = ArgumentCaptor.forClass(SimpleMailMessage.class);
        verify(javaMailSender, times(1)).send(messageCaptor.capture());

        SimpleMailMessage sentMessage = messageCaptor.getValue();
        assertEquals("<EMAIL>", sentMessage.getFrom());
        assertArrayEquals(new String[]{userEmail}, sentMessage.getTo());
    }

    @Test
    @DisplayName("正常系：トークンの有効期限が2時間に設定される")
    void handlePasswordResetEvent_shouldSetTokenExpirationTo2Hours() {
        // Arrange
        UUID userId = UUID.randomUUID();
        String userEmail = "<EMAIL>";
        String origin = "http://localhost:8080";

        ReflectionTestUtils.setField(passwordResetEventListener, "senderAddress", "<EMAIL>");

        when(messageHelper.getMessage("mail.password_reset.subject")).thenReturn("パスワードリセット");
        when(messageHelper.getMessage("mail.password_reset.body")).thenReturn("以下のリンクをクリックしてパスワードをリセットしてください。\nこのリンクは2時間で有効期限が切れます。");

        PasswordResetEvent event = new PasswordResetEvent(this, userId, userEmail, origin);

        // Act
        passwordResetEventListener.handlePasswordResetEvent(event);

        // Assert
        ArgumentCaptor<PasswordResetToken> tokenCaptor = ArgumentCaptor.forClass(PasswordResetToken.class);
        verify(passwordResetTokenRepository, times(1)).save(tokenCaptor.capture());

        PasswordResetToken savedToken = tokenCaptor.getValue();
        assertNotNull(savedToken.getExpireAt());
        // トークンの有効期限が現在時刻から約2時間後であることを確認
        // 実際の実装では PasswordResetToken.EXPIRE_HOURS = 2 が使用される
        assertFalse(savedToken.isExpired());
    }
}
