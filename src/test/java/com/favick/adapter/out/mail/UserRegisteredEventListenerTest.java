package com.favick.adapter.out.mail;

import com.favick.application.port.out.feature.token.repository.VerificationTokenRepository;
import com.favick.common.helper.MessageHelper;
import com.favick.domain.feature.token.model.VerificationToken;
import com.favick.domain.feature.user.event.UserRegisteredEvent;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("UserRegisteredEventListener のテスト")
class UserRegisteredEventListenerTest {

    @Mock
    private VerificationTokenRepository verificationTokenRepository;

    @Mock
    private JavaMailSender javaMailSender;

    @Mock
    private MessageHelper messageHelper;

    @InjectMocks
    private UserRegisteredEventListener userRegisteredEventListener;

    @Test
    @DisplayName("正常系：ユーザー登録イベントを処理してメールを送信する")
    void handleUserRegisteredEvent_shouldProcessEventAndSendEmail() {
        // Arrange
        UUID userId = UUID.randomUUID();
        String userEmail = "<EMAIL>";
        String origin = "http://localhost:8080";

        ReflectionTestUtils.setField(userRegisteredEventListener, "senderAddress", "<EMAIL>");

        UserRegisteredEvent event = new UserRegisteredEvent(this, userId, userEmail, origin);

        when(messageHelper.getMessage("mail.user_registration.subject"))
            .thenReturn("ユーザー登録確認");
        when(messageHelper.getMessage("mail.user_registration.body"))
            .thenReturn("以下のリンクをクリックしてユーザー登録を完了してください。");

        // Act
        userRegisteredEventListener.handleUserRegisteredEvent(event);

        // Assert
        // 既存トークンの削除を確認
        verify(verificationTokenRepository, times(1)).deleteByUserId(new UserId(userId));

        // 新しいトークンの保存を確認
        ArgumentCaptor<VerificationToken> tokenCaptor = ArgumentCaptor.forClass(VerificationToken.class);
        verify(verificationTokenRepository, times(1)).save(tokenCaptor.capture());

        VerificationToken savedToken = tokenCaptor.getValue();
        assertNotNull(savedToken);
        assertEquals(userId, savedToken.getUserId().value());
        assertNotNull(savedToken.getToken());
        assertNotNull(savedToken.getExpireAt());

        // メール送信を確認
        ArgumentCaptor<SimpleMailMessage> messageCaptor = ArgumentCaptor.forClass(SimpleMailMessage.class);
        verify(javaMailSender, times(1)).send(messageCaptor.capture());

        SimpleMailMessage sentMessage = messageCaptor.getValue();
        assertNotNull(sentMessage);
        assertEquals("<EMAIL>", sentMessage.getFrom());
        assertArrayEquals(new String[]{userEmail}, sentMessage.getTo());
        assertEquals("ユーザー登録確認", sentMessage.getSubject());

        String messageText = sentMessage.getText();
        assertNotNull(messageText);
        assertTrue(messageText.contains("以下のリンクをクリックしてユーザー登録を完了してください。"));
        assertTrue(messageText.contains(origin + "/auth/register/verify?token="));
        assertTrue(messageText.contains(savedToken.getToken().value()));
    }

    @Test
    @DisplayName("正常系：既存トークンが削除されてから新しいトークンが作成される")
    void handleUserRegisteredEvent_shouldDeleteExistingTokenBeforeCreatingNew() {
        // Arrange
        UUID userId = UUID.randomUUID();
        String userEmail = "<EMAIL>";
        String origin = "https://favick.com";

        ReflectionTestUtils.setField(userRegisteredEventListener, "senderAddress", "<EMAIL>");

        UserRegisteredEvent event = new UserRegisteredEvent(this, userId, userEmail, origin);

        when(messageHelper.getMessage("mail.user_registration.subject"))
            .thenReturn("ユーザー登録確認");
        when(messageHelper.getMessage("mail.user_registration.body"))
            .thenReturn("登録を完了してください。");

        // Act
        userRegisteredEventListener.handleUserRegisteredEvent(event);

        // Assert - 処理順序を確認（削除 → 作成 → メール送信）
        var inOrder = inOrder(verificationTokenRepository, javaMailSender);
        inOrder.verify(verificationTokenRepository).deleteByUserId(new UserId(userId));
        inOrder.verify(verificationTokenRepository).save(any(VerificationToken.class));
        inOrder.verify(javaMailSender).send(any(SimpleMailMessage.class));
    }

    @Test
    @DisplayName("正常系：HTTPSのoriginでも正しくURLが生成される")
    void handleUserRegisteredEvent_withHttpsOrigin_shouldGenerateCorrectUrl() {
        // Arrange
        UUID userId = UUID.randomUUID();
        String userEmail = "<EMAIL>";
        String origin = "https://favick.com";

        ReflectionTestUtils.setField(userRegisteredEventListener, "senderAddress", "<EMAIL>");

        UserRegisteredEvent event = new UserRegisteredEvent(this, userId, userEmail, origin);

        when(messageHelper.getMessage("mail.user_registration.subject"))
            .thenReturn("ユーザー登録確認");
        when(messageHelper.getMessage("mail.user_registration.body"))
            .thenReturn("登録を完了してください。");

        // Act
        userRegisteredEventListener.handleUserRegisteredEvent(event);

        // Assert
        ArgumentCaptor<SimpleMailMessage> messageCaptor = ArgumentCaptor.forClass(SimpleMailMessage.class);
        verify(javaMailSender, times(1)).send(messageCaptor.capture());

        SimpleMailMessage sentMessage = messageCaptor.getValue();
        String messageText = sentMessage.getText();
        assertNotNull(messageText);
        assertTrue(messageText.contains("https://favick.com/auth/register/verify?token="));
    }

    @Test
    @DisplayName("正常系：メールアドレスが正しく設定される")
    void handleUserRegisteredEvent_shouldSetCorrectEmailAddress() {
        // Arrange
        UUID userId = UUID.randomUUID();
        String userEmail = "<EMAIL>";
        String origin = "http://localhost:8080";

        ReflectionTestUtils.setField(userRegisteredEventListener, "senderAddress", "<EMAIL>");

        UserRegisteredEvent event = new UserRegisteredEvent(this, userId, userEmail, origin);

        when(messageHelper.getMessage("mail.user_registration.subject"))
            .thenReturn("ユーザー登録確認");
        when(messageHelper.getMessage("mail.user_registration.body"))
            .thenReturn("登録を完了してください。");

        // Act
        userRegisteredEventListener.handleUserRegisteredEvent(event);

        // Assert
        ArgumentCaptor<SimpleMailMessage> messageCaptor = ArgumentCaptor.forClass(SimpleMailMessage.class);
        verify(javaMailSender, times(1)).send(messageCaptor.capture());

        SimpleMailMessage sentMessage = messageCaptor.getValue();
        assertEquals("<EMAIL>", sentMessage.getFrom());
        assertArrayEquals(new String[]{userEmail}, sentMessage.getTo());
    }

    @Test
    @DisplayName("正常系：メッセージヘルパーから正しいメッセージを取得する")
    void handleUserRegisteredEvent_shouldGetCorrectMessagesFromHelper() {
        // Arrange
        UUID userId = UUID.randomUUID();
        String userEmail = "<EMAIL>";
        String origin = "http://localhost:8080";

        ReflectionTestUtils.setField(userRegisteredEventListener, "senderAddress", "<EMAIL>");

        UserRegisteredEvent event = new UserRegisteredEvent(this, userId, userEmail, origin);

        when(messageHelper.getMessage("mail.user_registration.subject"))
            .thenReturn("カスタム件名");
        when(messageHelper.getMessage("mail.user_registration.body"))
            .thenReturn("カスタムメッセージ本文");

        // Act
        userRegisteredEventListener.handleUserRegisteredEvent(event);

        // Assert
        verify(messageHelper, times(1)).getMessage("mail.user_registration.subject");
        verify(messageHelper, times(1)).getMessage("mail.user_registration.body");

        ArgumentCaptor<SimpleMailMessage> messageCaptor = ArgumentCaptor.forClass(SimpleMailMessage.class);
        verify(javaMailSender, times(1)).send(messageCaptor.capture());

        SimpleMailMessage sentMessage = messageCaptor.getValue();
        assertEquals("カスタム件名", sentMessage.getSubject());
        String messageText = sentMessage.getText();
        assertNotNull(messageText);
        assertTrue(messageText.contains("カスタムメッセージ本文"));
    }

    @Test
    @DisplayName("正常系：トークンの有効期限が適切に設定される")
    void handleUserRegisteredEvent_shouldSetTokenExpirationCorrectly() {
        // Arrange
        UUID userId = UUID.randomUUID();
        String userEmail = "<EMAIL>";
        String origin = "http://localhost:8080";

        ReflectionTestUtils.setField(userRegisteredEventListener, "senderAddress", "<EMAIL>");

        UserRegisteredEvent event = new UserRegisteredEvent(this, userId, userEmail, origin);

        when(messageHelper.getMessage("mail.user_registration.subject"))
            .thenReturn("ユーザー登録確認");
        when(messageHelper.getMessage("mail.user_registration.body"))
            .thenReturn("登録を完了してください。");

        // Act
        userRegisteredEventListener.handleUserRegisteredEvent(event);

        // Assert
        ArgumentCaptor<VerificationToken> tokenCaptor = ArgumentCaptor.forClass(VerificationToken.class);
        verify(verificationTokenRepository, times(1)).save(tokenCaptor.capture());

        VerificationToken savedToken = tokenCaptor.getValue();
        assertNotNull(savedToken.getExpireAt());
        // トークンの有効期限が現在時刻より後であることを確認
        assertFalse(savedToken.isExpired());
    }
}
