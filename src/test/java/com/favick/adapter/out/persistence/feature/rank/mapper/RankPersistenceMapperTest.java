package com.favick.adapter.out.persistence.feature.rank.mapper;

import com.favick.adapter.out.persistence.feature.rank.entity.RankEntity;
import com.favick.domain.feature.rank.model.Rank;
import com.favick.domain.feature.rank.model.RankId;
import com.favick.domain.feature.rank.model.RankName;
import com.favick.domain.feature.rank.model.RankNameValue;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("RankPersistenceMapper のテスト")
class RankPersistenceMapperTest {
    private static final UUID TEST_ID = UUID.randomUUID();
    private static final RankNameValue TEST_NAME = RankNameValue.RANK_SEED;
    private static final LocalDateTime TEST_CREATED_AT = LocalDateTime.now().minusDays(1);
    private static final LocalDateTime TEST_UPDATED_AT = LocalDateTime.now();

    @Test
    @DisplayName("エンティティからドメインモデルに正しく変換できる")
    void toDomain_shouldCorrectlyMapEntityToDomain() {
        // Arrange
        RankEntity entity = RankEntity.builder()
            .id(TEST_ID)
            .name(TEST_NAME)
            .createdAt(TEST_CREATED_AT)
            .updatedAt(TEST_UPDATED_AT)
            .build();

        // Act
        Rank result = RankPersistenceMapper.toDomain(entity);

        // Assert
        assertAll(
            () -> assertNotNull(result, "変換結果がnullではないこと"),
            () -> assertEquals(TEST_ID, result.getId().value(), "IDが正しく変換されていること"),
            () -> assertEquals(TEST_NAME, result.getName().value(), "名前が正しく変換されていること"),
            () -> assertEquals(TEST_CREATED_AT, result.getCreatedAt(), "作成日時が正しく変換されていること"),
            () -> assertEquals(TEST_UPDATED_AT, result.getUpdatedAt(), "更新日時が正しく変換されていること")
        );
    }

    @Test
    @DisplayName("ドメインモデルからエンティティに正しく変換できる")
    void toEntity_shouldCorrectlyMapDomainToEntity() {
        // Arrange
        Rank domain = Rank.reconstruct(
            new RankId(TEST_ID),
            new RankName(TEST_NAME),
            TEST_CREATED_AT,
            TEST_UPDATED_AT
        );

        // Act
        RankEntity result = RankPersistenceMapper.toEntity(domain);

        // Assert
        assertAll(
            () -> assertNotNull(result, "変換結果がnullではないこと"),
            () -> assertEquals(TEST_ID, result.getId(), "IDが正しく変換されていること"),
            () -> assertEquals(TEST_NAME, result.getName(), "名前が正しく変換されていること"),
            () -> assertEquals(TEST_CREATED_AT, result.getCreatedAt(), "作成日時が正しく変換されていること"),
            () -> assertEquals(TEST_UPDATED_AT, result.getUpdatedAt(), "更新日時が正しく変換されていること")
        );
    }

    @Test
    @DisplayName("ドメインモデルとエンティティ間の双方向変換の一貫性")
    void bidirectionalMapping_shouldMaintainConsistency() {
        // Arrange
        // 元のドメインモデル
        Rank originalRank = Rank.reconstruct(
            new RankId(TEST_ID),
            new RankName(TEST_NAME),
            TEST_CREATED_AT,
            TEST_UPDATED_AT
        );

        // Act
        // ドメイン → エンティティ → ドメイン の変換
        RankEntity entity = RankPersistenceMapper.toEntity(originalRank);
        Rank convertedRank = RankPersistenceMapper.toDomain(entity);

        // Assert
        assertEquals(originalRank.getId().value(), convertedRank.getId().value());
        assertEquals(originalRank.getName().value(), convertedRank.getName().value());
        assertEquals(originalRank.getCreatedAt(), convertedRank.getCreatedAt());
        assertEquals(originalRank.getUpdatedAt(), convertedRank.getUpdatedAt());
    }

    @Test
    @DisplayName("nullのエンティティを変換すると例外がスローされる")
    void toDomain_withNullEntity_shouldThrowException() {
        assertThrows(NullPointerException.class,
            () -> RankPersistenceMapper.toDomain(null),
            "NullPointerExceptionがスローされること"
        );
    }

    @Test
    @DisplayName("nullのドメインモデルを変換すると例外がスローされる")
    void toEntity_withNullDomain_shouldThrowException() {
        assertThrows(NullPointerException.class,
            () -> RankPersistenceMapper.toEntity(null),
            "NullPointerExceptionがスローされること"
        );
    }
}
