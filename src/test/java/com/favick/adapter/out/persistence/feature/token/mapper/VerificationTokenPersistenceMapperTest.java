package com.favick.adapter.out.persistence.feature.token.mapper;

import com.favick.adapter.out.persistence.feature.token.entity.VerificationTokenEntity;
import com.favick.domain.feature.token.model.TokenExpireAt;
import com.favick.domain.feature.token.model.TokenValue;
import com.favick.domain.feature.token.model.VerificationToken;
import com.favick.domain.feature.token.model.VerificationTokenId;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;

class VerificationTokenPersistenceMapperTest {

    @Test
    @DisplayName("エンティティからドメインモデルに正しく変換できる")
    void toDomain_shouldCorrectlyMapEntityToDomain() {
        // Arrange
        UUID id = UUID.randomUUID();
        UUID userId = UUID.randomUUID();
        String token = "test-verification-token";
        LocalDateTime expireAt = LocalDateTime.now().plusHours(24);
        LocalDateTime createdAt = LocalDateTime.now().minusHours(1);

        VerificationTokenEntity entity = VerificationTokenEntity.builder()
            .id(id)
            .userId(userId)
            .token(token)
            .expireAt(expireAt)
            .createdAt(createdAt)
            .build();

        // Act
        VerificationToken verificationToken = VerificationTokenPersistenceMapper.toDomain(entity);

        // Assert
        assertEquals(id, verificationToken.getId().value());
        assertEquals(userId, verificationToken.getUserId().value());
        assertEquals(token, verificationToken.getToken().value());
        assertEquals(expireAt, verificationToken.getExpireAt().value());
        assertEquals(createdAt, verificationToken.getCreatedAt());
    }

    @Test
    @DisplayName("ドメインモデルからエンティティに正しく変換できる")
    void toEntity_shouldCorrectlyMapDomainToEntity() {
        // Arrange
        UUID id = UUID.randomUUID();
        UUID userId = UUID.randomUUID();
        String token = "test-verification-token-2";
        LocalDateTime expireAt = LocalDateTime.now().plusHours(24);
        LocalDateTime createdAt = LocalDateTime.now().minusHours(2);

        VerificationToken verificationToken = VerificationToken.reconstruct(
            new VerificationTokenId(id),
            new UserId(userId),
            new TokenValue(token),
            new TokenExpireAt(expireAt),
            createdAt
        );

        // Act
        VerificationTokenEntity entity = VerificationTokenPersistenceMapper.toEntity(verificationToken);

        // Assert
        assertEquals(id, entity.getId());
        assertEquals(userId, entity.getUserId());
        assertEquals(token, entity.getToken());
        assertEquals(expireAt, entity.getExpireAt());
        assertEquals(createdAt, entity.getCreatedAt());
    }

    @Test
    @DisplayName("ドメインモデルとエンティティ間の双方向変換の一貫性")
    void bidirectionalMapping_shouldMaintainConsistency() {
        // Arrange
        UUID id = UUID.randomUUID();
        UUID userId = UUID.randomUUID();
        LocalDateTime now = LocalDateTime.now();

        // 元のドメインモデル
        VerificationToken originalToken = VerificationToken.reconstruct(
            new VerificationTokenId(id),
            new UserId(userId),
            new TokenValue("bidirectional-test-token"),
            new TokenExpireAt(now.plusHours(24)),
            now.minusHours(3)
        );

        // Act
        // ドメイン → エンティティ → ドメイン の変換
        VerificationTokenEntity entity = VerificationTokenPersistenceMapper.toEntity(originalToken);
        VerificationToken convertedToken = VerificationTokenPersistenceMapper.toDomain(entity);

        // Assert
        assertEquals(originalToken.getId().value(), convertedToken.getId().value());
        assertEquals(originalToken.getUserId().value(), convertedToken.getUserId().value());
        assertEquals(originalToken.getToken().value(), convertedToken.getToken().value());
        assertEquals(originalToken.getExpireAt().value(), convertedToken.getExpireAt().value());
        assertEquals(originalToken.getCreatedAt(), convertedToken.getCreatedAt());
    }
}
