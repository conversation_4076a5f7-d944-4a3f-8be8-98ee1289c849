package com.favick.adapter.out.persistence.feature.role.repository;

import com.favick.domain.feature.role.model.Role;
import com.favick.domain.feature.role.model.RoleId;
import com.favick.domain.feature.role.model.RoleNameValue;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DataJpaTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
@Import(RoleRepositoryImplTest.TestConfig.class)
@Transactional
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@DisplayName("RoleRepositoryImpl のテスト")
class RoleRepositoryImplTest {

    @Autowired
    private RoleRepositoryImpl roleRepository;

    @Test
    @DisplayName("存在するロールIDで検索すると、ロールが取得できる")
    void findById_existingId_shouldReturnRole() {
        // Arrange
        UUID roleId = UUID.fromString("11111111-1111-1111-1111-111111111111");

        // Act
        Optional<Role> result = roleRepository.findById(new RoleId(roleId));

        // Assert
        assertTrue(result.isPresent());
        Role role = result.get();
        assertEquals(roleId, role.getId().value());
        assertEquals(RoleNameValue.ROLE_ADMIN, role.getName().value());
    }

    @Test
    @DisplayName("存在しないロールIDで検索すると、空のOptionalが返る")
    void findById_nonExistingId_shouldReturnEmptyOptional() {
        // Arrange
        UUID nonExistingId = UUID.randomUUID();

        // Act
        Optional<Role> result = roleRepository.findById(new RoleId(nonExistingId));

        // Assert
        assertFalse(result.isPresent());
    }

    @TestConfiguration
    static class TestConfig {
        @Bean
        public RoleRepositoryImpl roleRepository(RoleJpaRepository roleJpaRepository) {
            return new RoleRepositoryImpl(roleJpaRepository);
        }
    }
}
