package com.favick.adapter.out.persistence.feature.fave.mapper;

import com.favick.adapter.out.persistence.feature.fave.entity.FaveEntity;
import com.favick.adapter.out.persistence.feature.fave.projection.IntegratedFave;
import com.favick.domain.feature.fave.model.Fave;
import com.favick.domain.feature.fave.model.FaveContent;
import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.review.model.Review;
import com.favick.domain.feature.review.model.ReviewStatusValue;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.data.util.Pair;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("FavePersistenceMapper のテスト")
class FavePersistenceMapperTest {

    @Test
    @DisplayName("エンティティからドメインモデルに正しく変換できること")
    void toDomain_shouldCorrectlyMapEntityToDomain() {
        // Arrange
        UUID faveId = UUID.randomUUID();
        UUID userId = UUID.randomUUID();
        UUID themeId = UUID.randomUUID();
        String content = "お気に入りの内容";
        LocalDateTime createdAt = LocalDateTime.now().minusDays(1);
        LocalDateTime updatedAt = LocalDateTime.now();

        FaveEntity entity = FaveEntity.builder()
            .id(faveId)
            .userId(userId)
            .themeId(themeId)
            .content(content)
            .createdAt(createdAt)
            .updatedAt(updatedAt)
            .build();

        // Act
        Fave fave = FavePersistenceMapper.toDomain(entity);

        // Assert
        assertEquals(faveId, fave.getId().value());
        assertEquals(userId, fave.getUserId().value());
        assertEquals(themeId, fave.getThemeId().value());
        assertEquals(content, fave.getContent().value());
        assertEquals(createdAt, fave.getCreatedAt());
        assertEquals(updatedAt, fave.getUpdatedAt());
    }

    @Test
    @DisplayName("ドメインモデルからエンティティに正しく変換できること")
    void toEntity_shouldCorrectlyMapDomainToEntity() {
        // Arrange
        FaveId faveId = new FaveId(UUID.randomUUID());
        UserId userId = new UserId(UUID.randomUUID());
        ThemeId themeId = new ThemeId(UUID.randomUUID());
        FaveContent content = new FaveContent("お気に入りの内容");
        LocalDateTime createdAt = LocalDateTime.now().minusDays(1);
        LocalDateTime updatedAt = LocalDateTime.now();

        Fave fave = Fave.reconstruct(faveId, userId, themeId, content, createdAt, updatedAt);

        // Act
        FaveEntity entity = FavePersistenceMapper.toEntity(fave);

        // Assert
        assertEquals(faveId.value(), entity.getId());
        assertEquals(userId.value(), entity.getUserId());
        assertEquals(themeId.value(), entity.getThemeId());
        assertEquals(content.value(), entity.getContent());
        assertEquals(createdAt, entity.getCreatedAt());
        assertEquals(updatedAt, entity.getUpdatedAt());
    }

    @Test
    @DisplayName("統合Projectionからドメインモデルペアに正しく変換できること")
    void toIntegratedDomain_shouldCorrectlyMapProjectionToDomainPair() {
        // Arrange
        UUID faveId = UUID.randomUUID();
        UUID userId = UUID.randomUUID();
        UUID themeId = UUID.randomUUID();
        UUID reviewId = UUID.randomUUID();
        String content = "お気に入りの内容";
        LocalDateTime faveCreatedAt = LocalDateTime.now().minusDays(2);
        LocalDateTime faveUpdatedAt = LocalDateTime.now().minusDays(1);
        LocalDateTime reviewCreatedAt = LocalDateTime.now().minusDays(1);
        LocalDateTime reviewUpdatedAt = LocalDateTime.now();

        IntegratedFave projection = new IntegratedFave() {
            @Override
            public UUID getFaveId() { return faveId; }
            @Override
            public UUID getUserId() { return userId; }
            @Override
            public UUID getThemeId() { return themeId; }
            @Override
            public String getContent() { return content; }
            @Override
            public LocalDateTime getFaveCreatedAt() { return faveCreatedAt; }
            @Override
            public LocalDateTime getFaveUpdatedAt() { return faveUpdatedAt; }
            @Override
            public UUID getReviewId() { return reviewId; }
            @Override
            public ReviewStatusValue getReviewStatus() { return ReviewStatusValue.PENDING; }
            @Override
            public LocalDateTime getReviewCreatedAt() { return reviewCreatedAt; }
            @Override
            public LocalDateTime getReviewUpdatedAt() { return reviewUpdatedAt; }
        };

        // Act
        Pair<Fave, Review> result = FavePersistenceMapper.toIntegratedDomain(projection);

        // Assert
        assertNotNull(result);
        
        Fave fave = result.getFirst();
        assertEquals(faveId, fave.getId().value());
        assertEquals(userId, fave.getUserId().value());
        assertEquals(themeId, fave.getThemeId().value());
        assertEquals(content, fave.getContent().value());
        assertEquals(faveCreatedAt, fave.getCreatedAt());
        assertEquals(faveUpdatedAt, fave.getUpdatedAt());

        Review review = result.getSecond();
        assertEquals(reviewId, review.getId().value());
        assertEquals(faveId, review.getFaveId().value());
        assertEquals(ReviewStatusValue.PENDING, review.getStatus().value());
        assertEquals(reviewCreatedAt, review.getCreatedAt());
        assertEquals(reviewUpdatedAt, review.getUpdatedAt());
    }
}