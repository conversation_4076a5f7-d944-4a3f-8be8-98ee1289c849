package com.favick.adapter.out.persistence.feature.theme.mapper;

import com.favick.adapter.out.persistence.feature.theme.entity.ThemeEntity;
import com.favick.adapter.out.persistence.feature.theme.projection.IntegratedTheme;
import com.favick.adapter.out.persistence.feature.theme.projection.IntegratedThemeMultiLang;
import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import com.favick.domain.feature.theme.model.Theme;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.theme.model.ThemeStartDate;
import com.favick.domain.feature.theme.model.ThemeType;
import com.favick.domain.feature.theme.model.ThemeTypeValue;
import com.favick.domain.feature.theme_localization.model.ThemeLocalization;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.data.util.Pair;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@DisplayName("ThemePersistenceMapper のテスト")
class ThemePersistenceMapperTest {

    @Test
    @DisplayName("エンティティからドメインモデルに正しく変換できること")
    void toDomain_shouldCorrectlyMapEntityToDomain() {
        // Arrange
        UUID themeId = UUID.randomUUID();
        ThemeTypeValue type = ThemeTypeValue.PHOTO;
        LocalDate startDate = LocalDate.of(2024, 1, 1);
        LocalDateTime createdAt = LocalDateTime.now().minusDays(1);
        LocalDateTime updatedAt = LocalDateTime.now();

        ThemeEntity entity = ThemeEntity.builder()
            .id(themeId)
            .type(type)
            .startDate(startDate)
            .createdAt(createdAt)
            .updatedAt(updatedAt)
            .build();

        // Act
        Theme theme = ThemePersistenceMapper.toDomain(entity);

        // Assert
        assertEquals(themeId, theme.getId().value());
        assertEquals(type, theme.getType().value());
        assertEquals(startDate, theme.getStartDate().value());
        assertEquals(createdAt, theme.getCreatedAt());
        assertEquals(updatedAt, theme.getUpdatedAt());
    }

    @Test
    @DisplayName("ドメインモデルからエンティティに正しく変換できること")
    void toEntity_shouldCorrectlyMapDomainToEntity() {
        // Arrange
        ThemeId themeId = new ThemeId(UUID.randomUUID());
        ThemeType type = new ThemeType(ThemeTypeValue.MOVIE);
        ThemeStartDate startDate = new ThemeStartDate(LocalDate.of(2024, 2, 1));
        LocalDateTime createdAt = LocalDateTime.now().minusDays(1);
        LocalDateTime updatedAt = LocalDateTime.now();

        Theme theme = Theme.reconstruct(themeId, type, startDate, createdAt, updatedAt);

        // Act
        ThemeEntity entity = ThemePersistenceMapper.toEntity(theme);

        // Assert
        assertEquals(themeId.value(), entity.getId());
        assertEquals(type.value(), entity.getType());
        assertEquals(startDate.value(), entity.getStartDate());
        assertEquals(createdAt, entity.getCreatedAt());
        assertEquals(updatedAt, entity.getUpdatedAt());
    }

    @Test
    @DisplayName("写真テーマを正しく変換できること")
    void mapPhotoTheme_shouldCorrectlyMapBothDirections() {
        // Arrange
        ThemeEntity photoEntity = ThemeEntity.builder()
            .id(UUID.randomUUID())
            .type(ThemeTypeValue.PHOTO)
            .startDate(LocalDate.of(2024, 3, 1))
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();

        // Act & Assert - エンティティからドメイン
        Theme theme = ThemePersistenceMapper.toDomain(photoEntity);
        assertEquals(ThemeTypeValue.PHOTO, theme.getType().value());

        // Act & Assert - ドメインからエンティティ
        ThemeEntity entity = ThemePersistenceMapper.toEntity(theme);
        assertEquals(ThemeTypeValue.PHOTO, entity.getType());
    }

    @Test
    @DisplayName("動画テーマを正しく変換できること")
    void mapMovieTheme_shouldCorrectlyMapBothDirections() {
        // Arrange
        ThemeEntity movieEntity = ThemeEntity.builder()
            .id(UUID.randomUUID())
            .type(ThemeTypeValue.MOVIE)
            .startDate(LocalDate.of(2024, 4, 1))
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();

        // Act & Assert - エンティティからドメイン
        Theme theme = ThemePersistenceMapper.toDomain(movieEntity);
        assertEquals(ThemeTypeValue.MOVIE, theme.getType().value());

        // Act & Assert - ドメインからエンティティ
        ThemeEntity entity = ThemePersistenceMapper.toEntity(theme);
        assertEquals(ThemeTypeValue.MOVIE, entity.getType());
    }

    @Test
    @DisplayName("音声テーマを正しく変換できること")
    void mapAudioTheme_shouldCorrectlyMapBothDirections() {
        // Arrange
        ThemeEntity audioEntity = ThemeEntity.builder()
            .id(UUID.randomUUID())
            .type(ThemeTypeValue.AUDIO)
            .startDate(LocalDate.of(2024, 5, 1))
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();

        // Act & Assert - エンティティからドメイン
        Theme theme = ThemePersistenceMapper.toDomain(audioEntity);
        assertEquals(ThemeTypeValue.AUDIO, theme.getType().value());

        // Act & Assert - ドメインからエンティティ
        ThemeEntity entity = ThemePersistenceMapper.toEntity(theme);
        assertEquals(ThemeTypeValue.AUDIO, entity.getType());
    }

    @Test
    @DisplayName("統合Projectionをドメインモデルに正しく変換できること")
    void toIntegratedDomain_shouldCorrectlyMapProjectionToDomain() {
        // Arrange
        UUID themeId = UUID.randomUUID();
        UUID localizationId = UUID.randomUUID();
        LocalDateTime themeCreatedAt = LocalDateTime.now().minusDays(1);
        LocalDateTime themeUpdatedAt = LocalDateTime.now();
        LocalDateTime localizationCreatedAt = LocalDateTime.now().minusDays(1);
        LocalDateTime localizationUpdatedAt = LocalDateTime.now();
        
        IntegratedTheme projection = new IntegratedTheme() {
            @Override
            public UUID getThemeId() { return themeId; }
            @Override
            public ThemeTypeValue getType() { return ThemeTypeValue.PHOTO; }
            @Override
            public LocalDate getStartDate() { return LocalDate.of(2024, 6, 1); }
            @Override
            public LocalDateTime getThemeCreatedAt() { return themeCreatedAt; }
            @Override
            public LocalDateTime getThemeUpdatedAt() { return themeUpdatedAt; }
            @Override
            public UUID getThemeLocalizationId() { return localizationId; }
            @Override
            public String getTitle() { return "テストタイトル"; }
            @Override
            public String getDescription() { return "テスト説明"; }
            @Override
            public LanguageCodeValue getLanguageCode() { return LanguageCodeValue.JA; }
            @Override
            public LocalDateTime getLocalizationCreatedAt() { return localizationCreatedAt; }
            @Override
            public LocalDateTime getLocalizationUpdatedAt() { return localizationUpdatedAt; }
        };

        // Act
        Pair<Theme, ThemeLocalization> result = ThemePersistenceMapper.toIntegratedDomain(projection);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getFirst()); // Theme
        assertNotNull(result.getSecond()); // ThemeLocalization
        
        Theme theme = result.getFirst();
        assertEquals(themeId, theme.getId().value());
        assertEquals(ThemeTypeValue.PHOTO, theme.getType().value());
        assertEquals(LocalDate.of(2024, 6, 1), theme.getStartDate().value());
        assertEquals(themeCreatedAt, theme.getCreatedAt());
        assertEquals(themeUpdatedAt, theme.getUpdatedAt());
        
        ThemeLocalization localization = result.getSecond();
        assertEquals(localizationId, localization.getId().value());
        assertEquals("テストタイトル", localization.getTitle().value());
        assertEquals("テスト説明", localization.getDescription().value());
        assertEquals(LanguageCodeValue.JA, localization.getLanguageCode().value());
        assertEquals(localizationCreatedAt, localization.getCreatedAt());
        assertEquals(localizationUpdatedAt, localization.getUpdatedAt());
    }

    @Test
    @DisplayName("多言語統合Projectionをドメインモデルに正しく変換できること")
    void toIntegratedMultiLangDomain_shouldCorrectlyMapProjectionToDomain() {
        // Arrange
        UUID themeId = UUID.randomUUID();
        UUID localizationJaId = UUID.randomUUID();
        UUID localizationEnId = UUID.randomUUID();
        LocalDateTime themeCreatedAt = LocalDateTime.now().minusDays(1);
        LocalDateTime themeUpdatedAt = LocalDateTime.now();
        LocalDateTime localizationJaCreatedAt = LocalDateTime.now().minusDays(1);
        LocalDateTime localizationJaUpdatedAt = LocalDateTime.now();
        LocalDateTime localizationEnCreatedAt = LocalDateTime.now().minusDays(1);
        LocalDateTime localizationEnUpdatedAt = LocalDateTime.now();
        
        IntegratedThemeMultiLang projection = new IntegratedThemeMultiLang() {
            @Override
            public UUID getThemeId() { return themeId; }
            @Override
            public ThemeTypeValue getType() { return ThemeTypeValue.MOVIE; }
            @Override
            public LocalDate getStartDate() { return LocalDate.of(2024, 7, 1); }
            @Override
            public LocalDateTime getThemeCreatedAt() { return themeCreatedAt; }
            @Override
            public LocalDateTime getThemeUpdatedAt() { return themeUpdatedAt; }
            @Override
            public UUID getThemeLocalizationJaId() { return localizationJaId; }
            @Override
            public String getTitleJa() { return "日本語タイトル"; }
            @Override
            public String getDescriptionJa() { return "日本語説明"; }
            @Override
            public LocalDateTime getLocalizationJaCreatedAt() { return localizationJaCreatedAt; }
            @Override
            public LocalDateTime getLocalizationJaUpdatedAt() { return localizationJaUpdatedAt; }
            @Override
            public UUID getThemeLocalizationEnId() { return localizationEnId; }
            @Override
            public String getTitleEn() { return "English Title"; }
            @Override
            public String getDescriptionEn() { return "English Description"; }
            @Override
            public LocalDateTime getLocalizationEnCreatedAt() { return localizationEnCreatedAt; }
            @Override
            public LocalDateTime getLocalizationEnUpdatedAt() { return localizationEnUpdatedAt; }
        };

        // Act
        Pair<Theme, Map<LanguageCode, ThemeLocalization>> result = ThemePersistenceMapper.toIntegratedMultiLangDomain(projection);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getFirst()); // Theme
        assertNotNull(result.getSecond()); // Map<LanguageCode, ThemeLocalization>
        
        Theme theme = result.getFirst();
        assertEquals(themeId, theme.getId().value());
        assertEquals(ThemeTypeValue.MOVIE, theme.getType().value());
        assertEquals(LocalDate.of(2024, 7, 1), theme.getStartDate().value());
        assertEquals(themeCreatedAt, theme.getCreatedAt());
        assertEquals(themeUpdatedAt, theme.getUpdatedAt());
        
        Map<LanguageCode, ThemeLocalization> localizations = result.getSecond();
        assertEquals(2, localizations.size());
        
        // 日本語ローカライゼーション確認
        LanguageCode jaCode = new LanguageCode(LanguageCodeValue.JA);
        ThemeLocalization jaLocalization = localizations.get(jaCode);
        assertNotNull(jaLocalization);
        assertEquals(localizationJaId, jaLocalization.getId().value());
        assertEquals("日本語タイトル", jaLocalization.getTitle().value());
        assertEquals("日本語説明", jaLocalization.getDescription().value());
        assertEquals(LanguageCodeValue.JA, jaLocalization.getLanguageCode().value());
        assertEquals(localizationJaCreatedAt, jaLocalization.getCreatedAt());
        assertEquals(localizationJaUpdatedAt, jaLocalization.getUpdatedAt());
        
        // 英語ローカライゼーション確認
        LanguageCode enCode = new LanguageCode(LanguageCodeValue.EN);
        ThemeLocalization enLocalization = localizations.get(enCode);
        assertNotNull(enLocalization);
        assertEquals(localizationEnId, enLocalization.getId().value());
        assertEquals("English Title", enLocalization.getTitle().value());
        assertEquals("English Description", enLocalization.getDescription().value());
        assertEquals(LanguageCodeValue.EN, enLocalization.getLanguageCode().value());
        assertEquals(localizationEnCreatedAt, enLocalization.getCreatedAt());
        assertEquals(localizationEnUpdatedAt, enLocalization.getUpdatedAt());
    }
}