package com.favick.adapter.out.persistence.feature.token.repository;

import com.favick.adapter.out.persistence.feature.rank.entity.RankEntity;
import com.favick.adapter.out.persistence.feature.rank.repository.RankJpaRepository;
import com.favick.adapter.out.persistence.feature.token.entity.VerificationTokenEntity;
import com.favick.adapter.out.persistence.feature.user.entity.UserEntity;
import com.favick.adapter.out.persistence.feature.user.repository.UserJpaRepository;
import com.favick.domain.feature.token.model.TokenExpireAt;
import com.favick.domain.feature.token.model.TokenValue;
import com.favick.domain.feature.token.model.VerificationToken;
import com.favick.domain.feature.token.model.VerificationTokenId;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DataJpaTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
@Import(VerificationTokenRepositoryImplTest.TestConfig.class)
@Transactional
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@DisplayName("VerificationTokenRepositoryImpl のテスト")
class VerificationTokenRepositoryImplTest {

    @Autowired
    private VerificationTokenJpaRepository verificationTokenJpaRepository;
    @Autowired
    private VerificationTokenRepositoryImpl verificationTokenRepository;
    @Autowired
    private UserJpaRepository userJpaRepository;
    @Autowired
    private RankJpaRepository rankJpaRepository;

    private UUID testUserId;

    @BeforeEach
    void setUp() {
        // 既存のランクを取得（RANK_SEEDは初期データで存在する）
        RankEntity rankEntity = rankJpaRepository.findAll().stream()
            .filter(rank -> rank.getName() == com.favick.domain.feature.rank.model.RankNameValue.RANK_SEED)
            .findFirst()
            .orElseThrow(() -> new RuntimeException("RANK_SEED not found in test data"));

        // テスト用のユーザーを作成
        testUserId = UUID.randomUUID();
        UserEntity userEntity = UserEntity.builder()
            .id(testUserId)
            .rankId(rankEntity.getId())
            .name("Test User")
            .email("<EMAIL>")
            .password("password")
            .enabled(true)
            .imageUrl("")
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();
        userJpaRepository.save(userEntity);
    }

    @Test
    @DisplayName("認証トークンを保存できる")
    void save_shouldSaveVerificationToken() {
        // Arrange
        UUID id = UUID.randomUUID();
        LocalDateTime now = LocalDateTime.now();

        VerificationToken verificationToken = VerificationToken.reconstruct(
            new VerificationTokenId(id),
            new UserId(testUserId),
            new TokenValue("test-verification-token"),
            new TokenExpireAt(now.plusHours(24)),
            now
        );

        // Act
        verificationTokenRepository.save(verificationToken);

        // Assert
        Optional<VerificationTokenEntity> savedEntity = verificationTokenJpaRepository.findById(id);
        assertTrue(savedEntity.isPresent());
        assertEquals(testUserId, savedEntity.get().getUserId());
        assertEquals("test-verification-token", savedEntity.get().getToken());
    }

    @Test
    @DisplayName("トークン値で認証トークンを取得できる")
    void findByToken_shouldReturnVerificationTokenByToken() {
        // Arrange
        UUID id = UUID.randomUUID();
        String tokenValue = "find-by-token-test";
        LocalDateTime now = LocalDateTime.now();

        VerificationTokenEntity entity = VerificationTokenEntity.builder()
            .id(id)
            .userId(testUserId)
            .token(tokenValue)
            .expireAt(now.plusHours(24))
            .createdAt(now)
            .build();

        verificationTokenJpaRepository.save(entity);

        // Act
        Optional<VerificationToken> result = verificationTokenRepository.findByToken(new TokenValue(tokenValue));

        // Assert
        assertTrue(result.isPresent());
        assertEquals(id, result.get().getId().value());
        assertEquals(testUserId, result.get().getUserId().value());
        assertEquals(tokenValue, result.get().getToken().value());
    }

    @Test
    @DisplayName("存在しないトークン値の場合は空のOptionalを返す")
    void findByToken_whenTokenDoesNotExist_shouldReturnEmptyOptional() {
        // Arrange
        String nonExistingToken = "non-existing-token";

        // Act
        Optional<VerificationToken> result = verificationTokenRepository.findByToken(new TokenValue(nonExistingToken));

        // Assert
        assertFalse(result.isPresent());
    }

    @Test
    @DisplayName("ユーザーIDで認証トークンを取得できる")
    void findByUserId_shouldReturnVerificationTokenByUserId() {
        // Arrange
        UUID id = UUID.randomUUID();
        LocalDateTime now = LocalDateTime.now();

        VerificationTokenEntity entity = VerificationTokenEntity.builder()
            .id(id)
            .userId(testUserId)
            .token("find-by-user-id-test")
            .expireAt(now.plusHours(24))
            .createdAt(now)
            .build();

        verificationTokenJpaRepository.save(entity);

        // Act
        Optional<VerificationToken> result = verificationTokenRepository.findByUserId(new UserId(testUserId));

        // Assert
        assertTrue(result.isPresent());
        assertEquals(id, result.get().getId().value());
        assertEquals(testUserId, result.get().getUserId().value());
    }

    @Test
    @DisplayName("存在しないユーザーIDの場合は空のOptionalを返す")
    void findByUserId_whenUserIdDoesNotExist_shouldReturnEmptyOptional() {
        // Arrange
        UUID nonExistingUserId = UUID.randomUUID();

        // Act
        Optional<VerificationToken> result = verificationTokenRepository.findByUserId(new UserId(nonExistingUserId));

        // Assert
        assertFalse(result.isPresent());
    }

    @Test
    @DisplayName("ユーザーIDで認証トークンを削除できる")
    void deleteByUserId_shouldDeleteVerificationTokenByUserId() {
        // Arrange
        UUID id = UUID.randomUUID();
        LocalDateTime now = LocalDateTime.now();

        VerificationTokenEntity entity = VerificationTokenEntity.builder()
            .id(id)
            .userId(testUserId)
            .token("delete-by-user-id-test")
            .expireAt(now.plusHours(24))
            .createdAt(now)
            .build();

        verificationTokenJpaRepository.save(entity);

        // 削除前に存在確認
        assertTrue(verificationTokenJpaRepository.existsById(id));

        // Act
        verificationTokenRepository.deleteByUserId(new UserId(testUserId));

        // Assert
        assertFalse(verificationTokenJpaRepository.existsById(id));
    }

    @Test
    @DisplayName("認証トークンを更新できる")
    void save_shouldUpdateVerificationToken() {
        // Arrange
        UUID id = UUID.randomUUID();
        LocalDateTime now = LocalDateTime.now();

        // 初期データを保存
        VerificationTokenEntity originalEntity = VerificationTokenEntity.builder()
            .id(id)
            .userId(testUserId)
            .token("original-token")
            .expireAt(now.plusHours(24))
            .createdAt(now)
            .build();

        verificationTokenJpaRepository.save(originalEntity);

        // 更新用のドメインモデル
        VerificationToken updatedToken = VerificationToken.reconstruct(
            new VerificationTokenId(id),
            new UserId(testUserId),
            new TokenValue("updated-token"),
            new TokenExpireAt(now.plusHours(48)),
            now
        );

        // Act
        verificationTokenRepository.save(updatedToken);

        // Assert
        Optional<VerificationTokenEntity> result = verificationTokenJpaRepository.findById(id);
        assertTrue(result.isPresent());
        assertEquals("updated-token", result.get().getToken());
        assertEquals(now.plusHours(48), result.get().getExpireAt());
    }

    @TestConfiguration
    static class TestConfig {
        @Bean
        public VerificationTokenRepositoryImpl verificationTokenRepository(
            VerificationTokenJpaRepository verificationTokenJpaRepository) {
            return new VerificationTokenRepositoryImpl(verificationTokenJpaRepository);
        }
    }
}
