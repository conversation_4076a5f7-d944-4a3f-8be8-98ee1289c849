package com.favick.adapter.out.persistence.feature.user.mapper;

import com.favick.adapter.out.persistence.feature.user.entity.UserEntity;
import com.favick.domain.feature.rank.model.RankId;
import com.favick.domain.feature.user.model.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;

@DisplayName("UserPersistenceMapper のテスト")
class UserPersistenceMapperTest {

    @Test
    @DisplayName("エンティティからドメインモデルに正しく変換できること")
    void toDomain_shouldCorrectlyMapEntityToDomain() {
        // Arrange
        UUID userId = UUID.randomUUID();
        UUID rankId = UUID.randomUUID();
        String name = "テストユーザー";
        String email = "<EMAIL>";
        String password = "encodedPassword";
        Boolean enabled = true;
        String imageUrl = "https://example.com/image.jpg";
        LocalDateTime createdAt = LocalDateTime.now().minusDays(1);
        LocalDateTime updatedAt = LocalDateTime.now();

        UserEntity entity = UserEntity.builder()
            .id(userId)
            .rankId(rankId)
            .name(name)
            .email(email)
            .password(password)
            .enabled(enabled)
            .imageUrl(imageUrl)
            .createdAt(createdAt)
            .updatedAt(updatedAt)
            .build();

        // Act
        User user = UserPersistenceMapper.toDomain(entity);

        // Assert
        assertEquals(userId, user.getId().value());
        assertEquals(rankId, user.getRankId().value());
        assertEquals(name, user.getName().value());
        assertEquals(email, user.getEmail().value());
        assertEquals(password, user.getPassword().value());
        assertEquals(enabled, user.getEnabled().value());
        assertEquals(imageUrl, user.getImageUrl().value());
        assertEquals(createdAt, user.getCreatedAt());
        assertEquals(updatedAt, user.getUpdatedAt());
    }

    @Test
    @DisplayName("ドメインモデルからエンティティに正しく変換できること")
    void toEntity_shouldCorrectlyMapDomainToEntity() {
        // Arrange
        UserId userId = new UserId(UUID.randomUUID());
        RankId rankId = new RankId(UUID.randomUUID());
        UserName name = new UserName("テストユーザー");
        UserEmail email = new UserEmail("<EMAIL>");
        UserPassword password = new UserPassword("encodedPassword");
        UserEnabled enabled = new UserEnabled(true);
        UserImageUri imageUrl = new UserImageUri("https://example.com/image.jpg");
        LocalDateTime createdAt = LocalDateTime.now().minusDays(1);
        LocalDateTime updatedAt = LocalDateTime.now();

        User user = User.reconstruct(userId, rankId, name, email, password, enabled, imageUrl, createdAt, updatedAt);

        // Act
        UserEntity entity = UserPersistenceMapper.toEntity(user);

        // Assert
        assertEquals(userId.value(), entity.getId());
        assertEquals(rankId.value(), entity.getRankId());
        assertEquals(name.value(), entity.getName());
        assertEquals(email.value(), entity.getEmail());
        assertEquals(password.value(), entity.getPassword());
        assertEquals(enabled.value(), entity.getEnabled());
        assertEquals(imageUrl.value(), entity.getImageUrl());
        assertEquals(createdAt, entity.getCreatedAt());
        assertEquals(updatedAt, entity.getUpdatedAt());
    }

    @Test
    @DisplayName("有効ユーザーを正しく変換できること")
    void mapEnabledUser_shouldCorrectlyMapBothDirections() {
        // Arrange
        UserEntity enabledEntity = UserEntity.builder()
            .id(UUID.randomUUID())
            .rankId(UUID.randomUUID())
            .name("有効ユーザー")
            .email("<EMAIL>")
            .password("password")
            .enabled(true)
            .imageUrl("https://example.com/image.jpg")
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();

        // Act & Assert - エンティティからドメイン
        User user = UserPersistenceMapper.toDomain(enabledEntity);
        assertEquals(true, user.getEnabled().value());

        // Act & Assert - ドメインからエンティティ
        UserEntity entity = UserPersistenceMapper.toEntity(user);
        assertEquals(true, entity.getEnabled());
    }

    @Test
    @DisplayName("無効ユーザーを正しく変換できること")
    void mapDisabledUser_shouldCorrectlyMapBothDirections() {
        // Arrange
        UserEntity disabledEntity = UserEntity.builder()
            .id(UUID.randomUUID())
            .rankId(UUID.randomUUID())
            .name("無効ユーザー")
            .email("<EMAIL>")
            .password("password")
            .enabled(false)
            .imageUrl("https://example.com/image.jpg")
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();

        // Act & Assert - エンティティからドメイン
        User user = UserPersistenceMapper.toDomain(disabledEntity);
        assertEquals(false, user.getEnabled().value());

        // Act & Assert - ドメインからエンティティ
        UserEntity entity = UserPersistenceMapper.toEntity(user);
        assertEquals(false, entity.getEnabled());
    }
}