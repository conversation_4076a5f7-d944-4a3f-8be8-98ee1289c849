package com.favick.adapter.out.persistence.feature.language.repository;

import com.favick.domain.feature.language.model.Language;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@DataJpaTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
@Import(LanguageRepositoryImplTest.TestConfig.class)
@Transactional
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@DisplayName("LanguageRepositoryImpl のテスト")
class LanguageRepositoryImplTest {

    @Autowired
    private LanguageRepositoryImpl languageRepository;

    @Test
    @DisplayName("全ての言語を取得できる")
    void findAll_shouldReturnAllLanguagesWithCorrectData() {
        // Act
        List<Language> result = languageRepository.findAll();

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size(), "言語は2つである必要があります");

        // ソート順の確認（PostgreSQLのENUMは定義順でソートされる）
        Language jaLanguage = result.get(0);
        Language enLanguage = result.get(1);

        // 日本語の確認
        assertEquals(LanguageCodeValue.JA, jaLanguage.getCode().value());
        assertEquals("日本語", jaLanguage.getDisplayName().value());
        assertNotNull(jaLanguage.getId());
        assertNotNull(jaLanguage.getCreatedAt());
        assertNotNull(jaLanguage.getUpdatedAt());

        // 英語の確認
        assertEquals(LanguageCodeValue.EN, enLanguage.getCode().value());
        assertEquals("English", enLanguage.getDisplayName().value());
        assertNotNull(enLanguage.getId());
        assertNotNull(enLanguage.getCreatedAt());
        assertNotNull(enLanguage.getUpdatedAt());
    }

    @TestConfiguration
    static class TestConfig {
        @Bean
        public LanguageRepositoryImpl languageRepository(LanguageJpaRepository languageJpaRepository) {
            return new LanguageRepositoryImpl(languageJpaRepository);
        }
    }
}
