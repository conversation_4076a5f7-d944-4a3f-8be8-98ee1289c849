package com.favick.adapter.out.persistence.feature.language.mapper;

import com.favick.adapter.out.persistence.feature.language.entity.LanguageEntity;
import com.favick.domain.feature.language.model.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;

class LanguagePersistenceMapperTest {

    @Test
    @DisplayName("エンティティからドメインモデルに正しく変換できる")
    void toDomain_shouldCorrectlyMapEntityToDomain() {
        // Arrange
        UUID languageId = UUID.randomUUID();
        LanguageCodeValue code = LanguageCodeValue.JA;
        String displayName = "日本語";
        LocalDateTime createdAt = LocalDateTime.now().minusDays(1);
        LocalDateTime updatedAt = LocalDateTime.now();

        LanguageEntity entity = LanguageEntity.builder()
            .id(languageId)
            .code(code)
            .displayName(displayName)
            .createdAt(createdAt)
            .updatedAt(updatedAt)
            .build();

        // Act
        Language language = LanguagePersistenceMapper.toDomain(entity);

        // Assert
        assertEquals(languageId, language.getId().value());
        assertEquals(code, language.getCode().value());
        assertEquals(displayName, language.getDisplayName().value());
        assertEquals(createdAt, language.getCreatedAt());
        assertEquals(updatedAt, language.getUpdatedAt());
    }

    @Test
    @DisplayName("ドメインモデルからエンティティに正しく変換できる")
    void toEntity_shouldCorrectlyMapDomainToEntity() {
        // Arrange
        UUID languageId = UUID.randomUUID();
        LanguageCodeValue code = LanguageCodeValue.EN;
        String displayName = "English";
        LocalDateTime createdAt = LocalDateTime.now().minusDays(1);
        LocalDateTime updatedAt = LocalDateTime.now();

        Language language = Language.reconstruct(
            new LanguageId(languageId),
            new LanguageCode(code),
            new LanguageDisplayName(displayName),
            createdAt,
            updatedAt
        );

        // Act
        LanguageEntity entity = LanguagePersistenceMapper.toEntity(language);

        // Assert
        assertEquals(languageId, entity.getId());
        assertEquals(code, entity.getCode());
        assertEquals(displayName, entity.getDisplayName());
        assertEquals(createdAt, entity.getCreatedAt());
        assertEquals(updatedAt, entity.getUpdatedAt());
    }

    @Test
    @DisplayName("異なる言語コードでエンティティが正しく変換される")
    void toDomain_withDifferentLanguageCode_shouldMapCorrectly() {
        // Arrange
        UUID languageId = UUID.randomUUID();
        LanguageCodeValue code = LanguageCodeValue.EN;
        String displayName = "English";
        LocalDateTime createdAt = LocalDateTime.now().minusDays(1);
        LocalDateTime updatedAt = LocalDateTime.now();

        LanguageEntity entity = LanguageEntity.builder()
            .id(languageId)
            .code(code)
            .displayName(displayName)
            .createdAt(createdAt)
            .updatedAt(updatedAt)
            .build();

        // Act
        Language language = LanguagePersistenceMapper.toDomain(entity);

        // Assert
        assertEquals(languageId, language.getId().value());
        assertEquals(LanguageCodeValue.EN, language.getCode().value());
        assertEquals("English", language.getDisplayName().value());
        assertEquals(createdAt, language.getCreatedAt());
        assertEquals(updatedAt, language.getUpdatedAt());
    }
}
