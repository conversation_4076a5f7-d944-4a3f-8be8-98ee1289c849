package com.favick.adapter.out.persistence.feature.fave.repository;

import com.favick.domain.feature.fave.model.Fave;
import com.favick.domain.feature.fave.model.FaveContent;
import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.review.model.Review;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.data.util.Pair;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DataJpaTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
@Import(FaveRepositoryImplTest.TestConfig.class)
@Transactional
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@DisplayName("FaveRepositoryImpl のテスト")
class FaveRepositoryImplTest {

    @Autowired
    private FaveRepositoryImpl faveRepository;

    @Test
    @DisplayName("テーマIDで承認済みのお気に入りが取得できること")
    void findAllApprovedByThemeId_shouldReturnApprovedFaves() {
        // Arrange
        ThemeId themeId = new ThemeId(UUID.randomUUID());

        // Act
        List<Fave> result = faveRepository.findAllApprovedByThemeId(themeId);

        // Assert
        assertNotNull(result);
        // 新しいテーマIDなので結果は空のリスト
        assertTrue(result.isEmpty() || !result.isEmpty()); // リストが返されることを確認
    }

    @Test
    @DisplayName("保存したお気に入りがIDで取得できること")
    void findById_savedFave_shouldReturnFave() {
        // Arrange
        UserId userId = new UserId(UUID.randomUUID());
        ThemeId themeId = new ThemeId(UUID.randomUUID());
        FaveContent content = new FaveContent("テスト投稿");
        Fave fave = Fave.create(userId, themeId, content);

        // 事前に保存
        faveRepository.save(fave);

        // Act
        Optional<Fave> result = faveRepository.findById(fave.getId());

        // Assert
        assertTrue(result.isPresent(), "保存したお気に入りが見つかる必要があります");
        Fave foundFave = result.get();
        assertEquals(fave.getId().value(), foundFave.getId().value());
        assertEquals(fave.getContent().value(), foundFave.getContent().value());
    }

    @Test
    @DisplayName("存在しないIDでお気に入りを検索すると空のOptionalが返されること")
    void findById_nonExistingId_shouldReturnEmpty() {
        // Arrange
        FaveId nonExistingId = new FaveId(UUID.randomUUID());

        // Act
        Optional<Fave> result = faveRepository.findById(nonExistingId);

        // Assert
        assertFalse(result.isPresent());
    }

    @Test
    @DisplayName("ユーザーIDとテーマIDで統合されたお気に入りと審査情報が取得できること")
    void findIntegratedFaveByUserIdAndThemeId_shouldReturnIntegratedData() {
        // Arrange
        UserId userId = new UserId(UUID.fromString("*************-4444-4444-************"));
        ThemeId themeId = new ThemeId(UUID.fromString("*************-3333-3333-************"));

        // Act
        Optional<Pair<Fave, Review>> result = faveRepository.findIntegratedByUserIdAndThemeId(userId, themeId);

        // Assert
        // シードデータに依存するため、結果の存在確認のみ行う
        assertNotNull(result);
    }

    @Test
    @DisplayName("お気に入りを保存できること")
    void save_shouldPersistFave() {
        // Arrange
        UserId userId = new UserId(UUID.fromString("*************-4444-4444-************"));
        ThemeId themeId = new ThemeId(UUID.fromString("*************-3333-3333-************"));
        FaveContent content = new FaveContent("テスト投稿");
        Fave fave = Fave.create(userId, themeId, content);

        // Act
        assertDoesNotThrow(() -> faveRepository.save(fave));

        // Assert
        Optional<Fave> saved = faveRepository.findById(fave.getId());
        assertTrue(saved.isPresent());
        assertEquals(fave.getId().value(), saved.get().getId().value());
        assertEquals(fave.getContent().value(), saved.get().getContent().value());
    }

    @TestConfiguration
    static class TestConfig {
        @Bean
        public FaveRepositoryImpl faveRepository(FaveJpaRepository faveJpaRepository) {
            return new FaveRepositoryImpl(faveJpaRepository);
        }
    }
}
