package com.favick.adapter.out.persistence.feature.role.mapper;

import com.favick.adapter.out.persistence.feature.role.entity.RoleEntity;
import com.favick.domain.feature.role.model.Role;
import com.favick.domain.feature.role.model.RoleId;
import com.favick.domain.feature.role.model.RoleName;
import com.favick.domain.feature.role.model.RoleNameValue;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("RolePersistenceMapper のテスト")
class RolePersistenceMapperTest {
    private static final UUID TEST_ID = UUID.randomUUID();
    private static final RoleNameValue TEST_NAME = RoleNameValue.ROLE_ADMIN;
    private static final LocalDateTime TEST_CREATED_AT = LocalDateTime.now().minusDays(1);
    private static final LocalDateTime TEST_UPDATED_AT = LocalDateTime.now();

    @Test
    @DisplayName("エンティティからドメインモデルに正しく変換できる")
    void toDomain_shouldCorrectlyMapEntityToDomain() {
        // Arrange
        RoleEntity entity = RoleEntity.builder()
            .id(TEST_ID)
            .name(TEST_NAME)
            .createdAt(TEST_CREATED_AT)
            .updatedAt(TEST_UPDATED_AT)
            .build();

        // Act
        Role result = RolePersistenceMapper.toDomain(entity);

        // Assert
        assertAll(
            () -> assertNotNull(result, "変換結果がnullではないこと"),
            () -> assertEquals(TEST_ID, result.getId().value(), "IDが正しく変換されていること"),
            () -> assertEquals(TEST_NAME, result.getName().value(), "名前が正しく変換されていること"),
            () -> assertEquals(TEST_CREATED_AT, result.getCreatedAt(), "作成日時が正しく変換されていること"),
            () -> assertEquals(TEST_UPDATED_AT, result.getUpdatedAt(), "更新日時が正しく変換されていること")
        );
    }

    @Test
    @DisplayName("ドメインモデルからエンティティに正しく変換できる")
    void toEntity_shouldCorrectlyMapDomainToEntity() {
        // Arrange
        Role domain = Role.reconstruct(
            new RoleId(TEST_ID),
            new RoleName(TEST_NAME),
            TEST_CREATED_AT,
            TEST_UPDATED_AT
        );

        // Act
        RoleEntity result = RolePersistenceMapper.toEntity(domain);

        // Assert
        assertAll(
            () -> assertNotNull(result, "変換結果がnullではないこと"),
            () -> assertEquals(TEST_ID, result.getId(), "IDが正しく変換されていること"),
            () -> assertEquals(TEST_NAME, result.getName(), "名前が正しく変換されていること"),
            () -> assertEquals(TEST_CREATED_AT, result.getCreatedAt(), "作成日時が正しく変換されていること"),
            () -> assertEquals(TEST_UPDATED_AT, result.getUpdatedAt(), "更新日時が正しく変換されていること")
        );
    }

    @Test
    @DisplayName("ドメインモデルとエンティティ間の双方向変換の一貫性")
    void bidirectionalMapping_shouldMaintainConsistency() {
        // Arrange
        // 元のドメインモデル
        Role originalRole = Role.reconstruct(
            new RoleId(TEST_ID),
            new RoleName(TEST_NAME),
            TEST_CREATED_AT,
            TEST_UPDATED_AT
        );

        // Act
        // ドメイン → エンティティ → ドメイン の変換
        RoleEntity entity = RolePersistenceMapper.toEntity(originalRole);
        Role convertedRole = RolePersistenceMapper.toDomain(entity);

        // Assert
        assertEquals(originalRole.getId().value(), convertedRole.getId().value());
        assertEquals(originalRole.getName().value(), convertedRole.getName().value());
        assertEquals(originalRole.getCreatedAt(), convertedRole.getCreatedAt());
        assertEquals(originalRole.getUpdatedAt(), convertedRole.getUpdatedAt());
    }

    @Test
    @DisplayName("nullのエンティティを変換すると例外がスローされる")
    void toDomain_withNullEntity_shouldThrowException() {
        assertThrows(NullPointerException.class,
            () -> RolePersistenceMapper.toDomain(null),
            "NullPointerExceptionがスローされること"
        );
    }

    @Test
    @DisplayName("nullのドメインモデルを変換すると例外がスローされる")
    void toEntity_withNullDomain_shouldThrowException() {
        assertThrows(NullPointerException.class,
            () -> RolePersistenceMapper.toEntity(null),
            "NullPointerExceptionがスローされること"
        );
    }
}
