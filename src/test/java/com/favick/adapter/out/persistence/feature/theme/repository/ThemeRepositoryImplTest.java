package com.favick.adapter.out.persistence.feature.theme.repository;


import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import com.favick.domain.feature.theme.model.*;
import com.favick.domain.feature.theme_localization.model.ThemeLocalization;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.data.util.Pair;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DataJpaTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
@Import(ThemeRepositoryImplTest.TestConfig.class)
@Transactional
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@DisplayName("ThemeRepositoryImpl のテスト")
class ThemeRepositoryImplTest {

    @Autowired
    private ThemeRepositoryImpl themeRepository;


    @Test
    @DisplayName("新しいテーマを保存できる")
    void save_shouldSaveTheme() {
        // Arrange
        LocalDateTime now = LocalDateTime.now();
        UUID id = UUID.randomUUID();
        Theme theme = Theme.reconstruct(
            new ThemeId(id),
            new ThemeType(ThemeTypeValue.AUDIO),
            new ThemeStartDate(LocalDate.now().plusDays(10)),
            now,
            now
        );

        // Act & Assert - 例外が発生しないことを確認
        assertDoesNotThrow(() -> themeRepository.save(theme));

        // 保存後に取得できることを確認
        Optional<Theme> savedTheme = themeRepository.findById(new ThemeId(id));
        assertTrue(savedTheme.isPresent());
        assertEquals(ThemeTypeValue.AUDIO, savedTheme.get().getType().value());
    }

    @Test
    @DisplayName("IDでテーマを取得できる")
    void findById_shouldReturnThemeById() {
        // Arrange
        UUID existingId = UUID.fromString("*************-5555-5555-************");

        // Act
        Optional<Theme> result = themeRepository.findById(new ThemeId(existingId));

        // Assert
        assertTrue(result.isPresent());
        assertEquals(ThemeTypeValue.PHOTO, result.get().getType().value());
        assertEquals(LocalDate.of(2025, 4, 1), result.get().getStartDate().value());
    }

    @Test
    @DisplayName("存在しないIDの場合は空のOptionalを返す")
    void findById_whenIdDoesNotExist_shouldReturnEmptyOptional() {
        // Arrange
        UUID nonExistingId = UUID.randomUUID();

        // Act
        Optional<Theme> result = themeRepository.findById(new ThemeId(nonExistingId));

        // Assert
        assertFalse(result.isPresent());
    }

    @Test
    @DisplayName("テーマを更新できる")
    void update_shouldUpdateTheme() {
        // Arrange - 既存のシードデータを使用
        UUID existingId = UUID.fromString("*************-5555-5555-************");
        LocalDateTime now = LocalDateTime.now();

        Theme updatedTheme = Theme.reconstruct(
            new ThemeId(existingId),
            new ThemeType(ThemeTypeValue.MOVIE),
            new ThemeStartDate(LocalDate.now().plusDays(15)),
            now,
            now.plusHours(1)
        );

        // Act
        assertDoesNotThrow(() -> themeRepository.save(updatedTheme));

        // Assert - 更新後に取得して確認
        Optional<Theme> result = themeRepository.findById(new ThemeId(existingId));
        assertTrue(result.isPresent());
        assertEquals(ThemeTypeValue.MOVIE, result.get().getType().value());
    }

    @Test
    @DisplayName("テーマを削除できる")
    void delete_shouldDeleteTheme() {
        // Arrange - まず新しいテーマを作成
        UUID id = UUID.randomUUID();
        LocalDateTime now = LocalDateTime.now();
        Theme theme = Theme.reconstruct(
            new ThemeId(id),
            new ThemeType(ThemeTypeValue.PHOTO),
            new ThemeStartDate(LocalDate.now().plusDays(20)),
            now,
            now
        );

        themeRepository.save(theme);

        // 削除前に存在確認
        assertTrue(themeRepository.findById(new ThemeId(id)).isPresent());

        // Act
        assertDoesNotThrow(() -> themeRepository.delete(new ThemeId(id)));

        // Assert
        assertFalse(themeRepository.findById(new ThemeId(id)).isPresent());
    }

    @Test
    @DisplayName("指定した開始日のテーマが存在するか確認できる")
    void existsByStartDate_shouldCheckIfThemeWithStartDateExists() {
        // Arrange
        LocalDate existingDate = LocalDate.of(2025, 4, 1); // Spring Flowersの開始日

        // Act & Assert
        assertTrue(themeRepository.existsByStartDate(new ThemeStartDate(existingDate)));
        assertFalse(themeRepository.existsByStartDate(new ThemeStartDate(existingDate.plusDays(1))));
    }

    @Test
    @DisplayName("指定したIDを除いて開始日のテーマが存在するか確認できる")
    void existsByStartDateExcludingId_shouldCheckIfThemeWithStartDateExistsExcludingId() {
        // Arrange
        UUID existingId = UUID.fromString("*************-5555-5555-************"); // Spring Flowers
        LocalDate existingDate = LocalDate.of(2025, 4, 1); // Spring Flowersの開始日

        // 新しいテストデータを追加
        UUID newId = UUID.randomUUID();
        LocalDateTime now = LocalDateTime.now();

        Theme newTheme = Theme.reconstruct(
            new ThemeId(newId),
            new ThemeType(ThemeTypeValue.PHOTO),
            new ThemeStartDate(existingDate.plusDays(1)), // 初期データと重複しない日付
            now,
            now
        );

        themeRepository.save(newTheme);

        // Act & Assert
        // 自分自身を除外した場合は初期データが存在する
        assertTrue(themeRepository.existsByStartDateExcludingId(
            new ThemeStartDate(existingDate),
            new ThemeId(newId)
        ));

        // 新しいデータの開始日で検索
        assertTrue(themeRepository.existsByStartDateExcludingId(
            new ThemeStartDate(existingDate.plusDays(1)),
            new ThemeId(existingId)
        ));

        // 存在しない開始日の場合は常に false
        assertFalse(themeRepository.existsByStartDateExcludingId(
            new ThemeStartDate(existingDate.plusDays(2)),
            new ThemeId(newId)
        ));
    }

    @Test
    @DisplayName("言語コードで統合済みのテーマ一覧を取得できる")
    void findAllIntegratedByLanguageCode_shouldReturnIntegratedThemes() {
        // Arrange
        LanguageCode languageCode = new LanguageCode(LanguageCodeValue.JA);

        // Act
        List<Pair<Theme, ThemeLocalization>> result = themeRepository.findAllIntegratedByLanguageCode(languageCode);

        // Assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
        // 全てのテーマが日本語のローカライゼーションを持つことを確認
        result.forEach(pair -> {
            assertNotNull(pair.getFirst()); // Theme
            assertNotNull(pair.getSecond()); // ThemeLocalization
            assertEquals(LanguageCodeValue.JA, pair.getSecond().getLanguageCode().value());
        });
    }

    @Test
    @DisplayName("現在開催中の統合済みのテーマを取得できる")
    void findCurrentIntegratedByLanguageCode_shouldReturnCurrentIntegratedTheme() {
        // Arrange
        LanguageCode languageCode = new LanguageCode(LanguageCodeValue.JA);

        // Act
        Optional<Pair<Theme, ThemeLocalization>> result = themeRepository.findCurrentIntegratedByLanguageCode(languageCode);

        // Assert
        if (result.isPresent()) {
            Pair<Theme, ThemeLocalization> pair = result.get();
            assertNotNull(pair.getFirst()); // Theme
            assertNotNull(pair.getSecond()); // ThemeLocalization
            assertEquals(LanguageCodeValue.JA, pair.getSecond().getLanguageCode().value());
            // 現在の日付以降のテーマであることを確認
            assertFalse(pair.getFirst().getStartDate().value().isAfter(LocalDate.now()));
        }
    }

    @Test
    @DisplayName("テーマIDと言語コードで統合済みのテーマを取得できる")
    void findIntegratedByIdAndLanguageCode_shouldReturnIntegratedTheme() {
        // Arrange
        UUID existingId = UUID.fromString("*************-5555-5555-************");
        ThemeId themeId = new ThemeId(existingId);
        LanguageCode languageCode = new LanguageCode(LanguageCodeValue.JA);

        // Act
        Optional<Pair<Theme, ThemeLocalization>> result = themeRepository.findIntegratedByIdAndLanguageCode(themeId, languageCode);

        // Assert
        assertTrue(result.isPresent());
        Pair<Theme, ThemeLocalization> pair = result.get();
        assertNotNull(pair.getFirst()); // Theme
        assertNotNull(pair.getSecond()); // ThemeLocalization
        assertEquals(themeId.value(), pair.getFirst().getId().value());
        assertEquals(LanguageCodeValue.JA, pair.getSecond().getLanguageCode().value());
    }

    @Test
    @DisplayName("テーマIDで全言語の統合済みのテーマを取得できる")
    void findIntegratedMultiLangById_shouldReturnMultiLangIntegratedTheme() {
        // Arrange
        UUID existingId = UUID.fromString("*************-5555-5555-************");
        ThemeId themeId = new ThemeId(existingId);

        // Act
        Optional<Pair<Theme, Map<LanguageCode, ThemeLocalization>>> result = themeRepository.findIntegratedMultiLangById(themeId);

        // Assert
        assertTrue(result.isPresent());
        Pair<Theme, Map<LanguageCode, ThemeLocalization>> pair = result.get();
        assertNotNull(pair.getFirst()); // Theme
        assertNotNull(pair.getSecond()); // Map<LanguageCode, ThemeLocalization>
        assertEquals(themeId.value(), pair.getFirst().getId().value());
        
        Map<LanguageCode, ThemeLocalization> localizationMap = pair.getSecond();
        assertFalse(localizationMap.isEmpty());
        // 少なくとも日本語のローカライゼーションは存在するはず
        assertTrue(localizationMap.containsKey(new LanguageCode(LanguageCodeValue.JA)));
    }

    @TestConfiguration
    static class TestConfig {
        @Bean
        public ThemeRepositoryImpl themeRepository(ThemeJpaRepository themeJpaRepository) {
            return new ThemeRepositoryImpl(themeJpaRepository);
        }
    }
}
