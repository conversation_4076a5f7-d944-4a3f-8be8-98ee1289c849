package com.favick.adapter.out.persistence.feature.user.repository;


import com.favick.domain.feature.user.model.User;
import com.favick.domain.feature.user.model.UserEmail;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DataJpaTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
@Import(UserRepositoryImplTest.TestConfig.class)
@Transactional
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@DisplayName("UserRepositoryImpl のテスト")
class UserRepositoryImplTest {

    @Autowired
    private UserRepositoryImpl userRepository;

    @Test
    @DisplayName("存在するメールアドレスで検索すると、ユーザーが取得できる")
    void findByEmail_existingEmail_shouldReturnUser() {
        // Arrange
        UserEmail email = new UserEmail("<EMAIL>");

        // Act
        Optional<User> result = userRepository.findByEmail(email);

        // Assert
        assertTrue(result.isPresent(), "シードデータのユーザーが見つかる必要があります");
        User user = result.get();

        assertEquals(UUID.fromString("*************-4444-4444-************"), user.getId().value());
        assertEquals(UUID.fromString("*************-2222-2222-************"), user.getRankId().value());
        assertEquals("User Seed", user.getName().value());
        assertEquals("<EMAIL>", user.getEmail().value());
        assertNotNull(user.getPassword().value());
        assertTrue(user.getEnabled().value());
        assertEquals("", user.getImageUrl().value());
        assertNotNull(user.getCreatedAt());
        assertNotNull(user.getUpdatedAt());
    }

    @Test
    @DisplayName("存在しないメールアドレスで検索すると、空のOptionalが返る")
    void findByEmail_nonExistingEmail_shouldReturnEmptyOptional() {
        // Arrange
        UserEmail nonExistingEmail = new UserEmail("<EMAIL>");

        // Act
        Optional<User> result = userRepository.findByEmail(nonExistingEmail);

        // Assert
        assertFalse(result.isPresent());
    }

    @Test
    @DisplayName("大文字小文字を区別してメールアドレスで検索できる")
    void findByEmail_shouldBeCaseSensitive() {
        // Arrange
        UserEmail upperCaseEmail = new UserEmail("<EMAIL>");

        // Act
        Optional<User> result = userRepository.findByEmail(upperCaseEmail);

        // Assert
        assertFalse(result.isPresent());
    }

    @TestConfiguration
    static class TestConfig {
        @Bean
        public UserRepositoryImpl userRepository(UserJpaRepository userJpaRepository) {
            return new UserRepositoryImpl(userJpaRepository);
        }
    }
}
