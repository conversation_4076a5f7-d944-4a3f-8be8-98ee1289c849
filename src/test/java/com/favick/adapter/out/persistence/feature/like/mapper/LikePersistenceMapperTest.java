package com.favick.adapter.out.persistence.feature.like.mapper;

import com.favick.adapter.out.persistence.feature.like.entity.LikeEntity;
import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.like.model.Like;
import com.favick.domain.feature.like.model.LikeId;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("LikePersistenceMapper のテスト")
class LikePersistenceMapperTest {

    private static final UUID TEST_LIKE_ID = UUID.fromString("*************-8888-8888-************");
    private static final UUID TEST_USER_ID = UUID.fromString("*************-4444-4444-************");
    private static final UUID TEST_FAVE_ID = UUID.fromString("*************-7777-7777-************");
    private static final LocalDateTime TEST_CREATED_AT = LocalDateTime.of(2024, 1, 15, 10, 30, 0);

    @Test
    @DisplayName("エンティティをドメインモデルに正しく変換できる")
    void toDomain_shouldCorrectlyMapEntityToDomain() {
        // Arrange
        LikeEntity entity = LikeEntity.builder()
            .id(TEST_LIKE_ID)
            .userId(TEST_USER_ID)
            .faveId(TEST_FAVE_ID)
            .createdAt(TEST_CREATED_AT)
            .build();

        // Act
        Like domain = LikePersistenceMapper.toDomain(entity);

        // Assert
        assertNotNull(domain);
        assertEquals(TEST_LIKE_ID, domain.getId().value());
        assertEquals(TEST_USER_ID, domain.getUserId().value());
        assertEquals(TEST_FAVE_ID, domain.getFaveId().value());
        assertEquals(TEST_CREATED_AT, domain.getCreatedAt());
    }

    @Test
    @DisplayName("ドメインモデルをエンティティに正しく変換できる")
    void toEntity_shouldCorrectlyMapDomainToEntity() {
        // Arrange
        Like domain = Like.reconstruct(
            new LikeId(TEST_LIKE_ID),
            new UserId(TEST_USER_ID),
            new FaveId(TEST_FAVE_ID),
            TEST_CREATED_AT
        );

        // Act
        LikeEntity entity = LikePersistenceMapper.toEntity(domain);

        // Assert
        assertNotNull(entity);
        assertEquals(TEST_LIKE_ID, entity.getId());
        assertEquals(TEST_USER_ID, entity.getUserId());
        assertEquals(TEST_FAVE_ID, entity.getFaveId());
        assertEquals(TEST_CREATED_AT, entity.getCreatedAt());
    }

    @Test
    @DisplayName("ドメインモデルとエンティティ間の双方向変換の一貫性")
    void bidirectionalMapping_shouldMaintainConsistency() {
        // Arrange
        // 元のドメインモデル
        Like originalDomain = Like.reconstruct(
            new LikeId(TEST_LIKE_ID),
            new UserId(TEST_USER_ID),
            new FaveId(TEST_FAVE_ID),
            TEST_CREATED_AT
        );

        // Act
        // ドメイン → エンティティ → ドメイン の変換
        LikeEntity entity = LikePersistenceMapper.toEntity(originalDomain);
        Like convertedDomain = LikePersistenceMapper.toDomain(entity);

        // Assert
        assertEquals(originalDomain.getId().value(), convertedDomain.getId().value());
        assertEquals(originalDomain.getUserId().value(), convertedDomain.getUserId().value());
        assertEquals(originalDomain.getFaveId().value(), convertedDomain.getFaveId().value());
        assertEquals(originalDomain.getCreatedAt(), convertedDomain.getCreatedAt());
    }

    @Test
    @DisplayName("エンティティとドメインモデル間の双方向変換の一貫性")
    void bidirectionalMappingFromEntity_shouldMaintainConsistency() {
        // Arrange
        // 元のエンティティ
        LikeEntity originalEntity = LikeEntity.builder()
            .id(TEST_LIKE_ID)
            .userId(TEST_USER_ID)
            .faveId(TEST_FAVE_ID)
            .createdAt(TEST_CREATED_AT)
            .build();

        // Act
        // エンティティ → ドメイン → エンティティ の変換
        Like domain = LikePersistenceMapper.toDomain(originalEntity);
        LikeEntity convertedEntity = LikePersistenceMapper.toEntity(domain);

        // Assert
        assertEquals(originalEntity.getId(), convertedEntity.getId());
        assertEquals(originalEntity.getUserId(), convertedEntity.getUserId());
        assertEquals(originalEntity.getFaveId(), convertedEntity.getFaveId());
        assertEquals(originalEntity.getCreatedAt(), convertedEntity.getCreatedAt());
    }

    @Test
    @DisplayName("新しく作成されたドメインモデルをエンティティに変換できる")
    void toEntity_withCreatedDomain_shouldMapCorrectly() {
        // Arrange
        UserId userId = new UserId(TEST_USER_ID);
        FaveId faveId = new FaveId(TEST_FAVE_ID);
        Like createdDomain = Like.create(userId, faveId);

        // Act
        LikeEntity entity = LikePersistenceMapper.toEntity(createdDomain);

        // Assert
        assertNotNull(entity);
        assertEquals(createdDomain.getId().value(), entity.getId());
        assertEquals(TEST_USER_ID, entity.getUserId());
        assertEquals(TEST_FAVE_ID, entity.getFaveId());
        assertEquals(createdDomain.getCreatedAt(), entity.getCreatedAt());
        assertNotNull(entity.getCreatedAt());
    }

    @Test
    @DisplayName("異なるUUIDでも正しく変換できる")
    void mapping_withDifferentUUIDs_shouldMapCorrectly() {
        // Arrange
        UUID differentLikeId = UUID.randomUUID();
        UUID differentUserId = UUID.randomUUID();
        UUID differentFaveId = UUID.randomUUID();
        LocalDateTime differentCreatedAt = LocalDateTime.now().minusDays(1);

        Like domain = Like.reconstruct(
            new LikeId(differentLikeId),
            new UserId(differentUserId),
            new FaveId(differentFaveId),
            differentCreatedAt
        );

        // Act
        LikeEntity entity = LikePersistenceMapper.toEntity(domain);
        Like convertedDomain = LikePersistenceMapper.toDomain(entity);

        // Assert
        assertEquals(differentLikeId, entity.getId());
        assertEquals(differentUserId, entity.getUserId());
        assertEquals(differentFaveId, entity.getFaveId());
        assertEquals(differentCreatedAt, entity.getCreatedAt());

        assertEquals(differentLikeId, convertedDomain.getId().value());
        assertEquals(differentUserId, convertedDomain.getUserId().value());
        assertEquals(differentFaveId, convertedDomain.getFaveId().value());
        assertEquals(differentCreatedAt, convertedDomain.getCreatedAt());
    }

    @Test
    @DisplayName("現在時刻で作成されたドメインモデルの変換")
    void mapping_withCurrentTime_shouldMapCorrectly() {
        // Arrange
        LocalDateTime now = LocalDateTime.now();
        Like domain = Like.reconstruct(
            new LikeId(TEST_LIKE_ID),
            new UserId(TEST_USER_ID),
            new FaveId(TEST_FAVE_ID),
            now
        );

        // Act
        LikeEntity entity = LikePersistenceMapper.toEntity(domain);
        Like convertedDomain = LikePersistenceMapper.toDomain(entity);

        // Assert
        assertEquals(now, entity.getCreatedAt());
        assertEquals(now, convertedDomain.getCreatedAt());
    }

    @Test
    @DisplayName("複数のいいねの変換でも一意性が保たれる")
    void mapping_multipleDistinctLikes_shouldMaintainUniqueness() {
        // Arrange
        UUID likeId1 = UUID.randomUUID();
        UUID likeId2 = UUID.randomUUID();
        UUID userId1 = UUID.randomUUID();
        UUID userId2 = UUID.randomUUID();
        UUID faveId1 = UUID.randomUUID();
        UUID faveId2 = UUID.randomUUID();
        LocalDateTime time1 = LocalDateTime.now().minusHours(1);
        LocalDateTime time2 = LocalDateTime.now();

        Like domain1 = Like.reconstruct(
            new LikeId(likeId1),
            new UserId(userId1),
            new FaveId(faveId1),
            time1
        );

        Like domain2 = Like.reconstruct(
            new LikeId(likeId2),
            new UserId(userId2),
            new FaveId(faveId2),
            time2
        );

        // Act
        LikeEntity entity1 = LikePersistenceMapper.toEntity(domain1);
        LikeEntity entity2 = LikePersistenceMapper.toEntity(domain2);

        // Assert
        assertNotEquals(entity1.getId(), entity2.getId());
        assertNotEquals(entity1.getUserId(), entity2.getUserId());
        assertNotEquals(entity1.getFaveId(), entity2.getFaveId());
        assertNotEquals(entity1.getCreatedAt(), entity2.getCreatedAt());

        assertEquals(likeId1, entity1.getId());
        assertEquals(likeId2, entity2.getId());
        assertEquals(userId1, entity1.getUserId());
        assertEquals(userId2, entity2.getUserId());
        assertEquals(faveId1, entity1.getFaveId());
        assertEquals(faveId2, entity2.getFaveId());
        assertEquals(time1, entity1.getCreatedAt());
        assertEquals(time2, entity2.getCreatedAt());
    }
}
