package com.favick.adapter.out.persistence.feature.review.mapper;

import com.favick.adapter.out.persistence.feature.review.entity.ReviewEntity;
import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.review.model.Review;
import com.favick.domain.feature.review.model.ReviewId;
import com.favick.domain.feature.review.model.ReviewStatus;
import com.favick.domain.feature.review.model.ReviewStatusValue;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("ReviewPersistenceMapper のテスト")
class ReviewPersistenceMapperTest {

    @Test
    @DisplayName("エンティティからドメインモデルに正しく変換できること")
    void toDomain_shouldCorrectlyMapEntityToDomain() {
        // Arrange
        UUID reviewId = UUID.randomUUID();
        UUID faveId = UUID.randomUUID();
        ReviewStatusValue status = ReviewStatusValue.PENDING;
        LocalDateTime createdAt = LocalDateTime.now().minusDays(1);
        LocalDateTime updatedAt = LocalDateTime.now();

        ReviewEntity entity = ReviewEntity.builder()
            .id(reviewId)
            .faveId(faveId)
            .status(status)
            .createdAt(createdAt)
            .updatedAt(updatedAt)
            .build();

        // Act
        Review review = ReviewPersistenceMapper.toDomain(entity);

        // Assert
        assertEquals(reviewId, review.getId().value());
        assertEquals(faveId, review.getFaveId().value());
        assertEquals(status, review.getStatus().value());
        assertEquals(createdAt, review.getCreatedAt());
        assertEquals(updatedAt, review.getUpdatedAt());
    }

    @Test
    @DisplayName("ドメインモデルからエンティティに正しく変換できること")
    void toEntity_shouldCorrectlyMapDomainToEntity() {
        // Arrange
        ReviewId reviewId = new ReviewId(UUID.randomUUID());
        FaveId faveId = new FaveId(UUID.randomUUID());
        ReviewStatus status = new ReviewStatus(ReviewStatusValue.APPROVED);
        LocalDateTime createdAt = LocalDateTime.now().minusDays(1);
        LocalDateTime updatedAt = LocalDateTime.now();

        Review review = Review.reconstruct(reviewId, faveId, status, createdAt, updatedAt);

        // Act
        ReviewEntity entity = ReviewPersistenceMapper.toEntity(review);

        // Assert
        assertEquals(reviewId.value(), entity.getId());
        assertEquals(faveId.value(), entity.getFaveId());
        assertEquals(status.value(), entity.getStatus());
        assertEquals(createdAt, entity.getCreatedAt());
        assertEquals(updatedAt, entity.getUpdatedAt());
    }

    @Test
    @DisplayName("承認状態の審査を正しく変換できること")
    void mapApprovedReview_shouldCorrectlyMapBothDirections() {
        // Arrange
        FaveId faveId = new FaveId(UUID.randomUUID());
        Review originalReview = Review.create(faveId);
        originalReview.approve();

        // Act & Assert - ドメインからエンティティ
        ReviewEntity entity = ReviewPersistenceMapper.toEntity(originalReview);
        assertEquals(ReviewStatusValue.APPROVED, entity.getStatus());

        // Act & Assert - エンティティからドメイン
        Review reconstructedReview = ReviewPersistenceMapper.toDomain(entity);
        assertTrue(reconstructedReview.isApproved());
        assertFalse(reconstructedReview.isPending());
        assertFalse(reconstructedReview.isRejected());
    }

    @Test
    @DisplayName("却下状態の審査を正しく変換できること")
    void mapRejectedReview_shouldCorrectlyMapBothDirections() {
        // Arrange
        FaveId faveId = new FaveId(UUID.randomUUID());
        Review originalReview = Review.create(faveId);
        originalReview.reject();

        // Act & Assert - ドメインからエンティティ
        ReviewEntity entity = ReviewPersistenceMapper.toEntity(originalReview);
        assertEquals(ReviewStatusValue.REJECTED, entity.getStatus());

        // Act & Assert - エンティティからドメイン
        Review reconstructedReview = ReviewPersistenceMapper.toDomain(entity);
        assertTrue(reconstructedReview.isRejected());
        assertFalse(reconstructedReview.isPending());
        assertFalse(reconstructedReview.isApproved());
    }
}