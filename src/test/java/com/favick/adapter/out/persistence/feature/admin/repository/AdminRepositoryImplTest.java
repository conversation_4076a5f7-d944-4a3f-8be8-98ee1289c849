package com.favick.adapter.out.persistence.feature.admin.repository;

import com.favick.domain.feature.admin.model.Admin;
import com.favick.domain.feature.admin.model.AdminEmail;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DataJpaTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
@Import(AdminRepositoryImplTest.TestConfig.class)
@Transactional
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@DisplayName("AdminRepositoryImpl のテスト")
class AdminRepositoryImplTest {

    @Autowired
    private AdminRepositoryImpl adminRepository;

    @Test
    @DisplayName("存在するメールアドレスで検索すると、管理者が取得できる")
    void findByEmail_existingEmail_shouldReturnAdmin() {
        // Arrange
        AdminEmail email = new AdminEmail("<EMAIL>");

        // Act
        Optional<Admin> result = adminRepository.findByEmail(email);

        // Assert
        assertTrue(result.isPresent());
        Admin admin = result.get();
        assertEquals(UUID.fromString("*************-3333-3333-************"), admin.getId().value());
        assertEquals(UUID.fromString("11111111-1111-1111-1111-111111111111"), admin.getRoleId().value());
        assertEquals("Admin User", admin.getName().value());
        assertEquals("<EMAIL>", admin.getEmail().value());
        assertEquals("$2a$10$WvhhQ6ypV0N.U9sIy4MZmOmspsyZDIJh9kOx5XcBS5MtExgCq6nLW", admin.getPassword().value());
        assertNotNull(admin.getCreatedAt());
        assertNotNull(admin.getUpdatedAt());
    }

    @Test
    @DisplayName("存在しないメールアドレスで検索すると、空のOptionalが返る")
    void findByEmail_nonExistingEmail_shouldReturnEmptyOptional() {
        // Arrange
        AdminEmail nonExistingEmail = new AdminEmail("<EMAIL>");

        // Act
        Optional<Admin> result = adminRepository.findByEmail(nonExistingEmail);

        // Assert
        assertFalse(result.isPresent());
    }

    @Test
    @DisplayName("大文字小文字を区別してメールアドレスで検索できる")
    void findByEmail_shouldBeCaseSensitive() {
        // Arrange
        AdminEmail upperCaseEmail = new AdminEmail("<EMAIL>");

        // Act
        Optional<Admin> result = adminRepository.findByEmail(upperCaseEmail);

        // Assert
        assertFalse(result.isPresent());
    }

    @TestConfiguration
    static class TestConfig {
        @Bean
        public AdminRepositoryImpl adminRepository(AdminJpaRepository adminJpaRepository) {
            return new AdminRepositoryImpl(adminJpaRepository);
        }
    }
}
