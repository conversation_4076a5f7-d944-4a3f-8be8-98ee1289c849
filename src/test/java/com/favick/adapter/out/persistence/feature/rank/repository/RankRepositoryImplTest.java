package com.favick.adapter.out.persistence.feature.rank.repository;

import com.favick.domain.feature.rank.model.Rank;
import com.favick.domain.feature.rank.model.RankId;
import com.favick.domain.feature.rank.model.RankNameValue;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DataJpaTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
@Import(RankRepositoryImplTest.TestConfig.class)
@Transactional
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@DisplayName("RankRepositoryImpl のテスト")
class RankRepositoryImplTest {

    @Autowired
    private RankRepositoryImpl rankRepository;

    @Test
    @DisplayName("存在するランクIDで検索すると、ランクが取得できる")
    void findById_existingId_shouldReturnRank() {
        // Arrange
        UUID rankId = UUID.fromString("*************-2222-2222-************");

        // Act
        Optional<Rank> result = rankRepository.findById(new RankId(rankId));

        // Assert
        assertTrue(result.isPresent());
        Rank rank = result.get();
        assertEquals(rankId, rank.getId().value());
        assertEquals(RankNameValue.RANK_SEED, rank.getName().value());
    }

    @Test
    @DisplayName("存在しないランクIDで検索すると、空のOptionalが返る")
    void findById_nonExistingId_shouldReturnEmptyOptional() {
        // Arrange
        UUID nonExistingId = UUID.randomUUID();

        // Act
        Optional<Rank> result = rankRepository.findById(new RankId(nonExistingId));

        // Assert
        assertFalse(result.isPresent());
    }

    @TestConfiguration
    static class TestConfig {
        @Bean
        public RankRepositoryImpl rankRepository(RankJpaRepository rankJpaRepository) {
            return new RankRepositoryImpl(rankJpaRepository);
        }
    }
}
