package com.favick.adapter.out.persistence.feature.review.repository;

import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.review.model.Review;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

@DataJpaTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
@Import(ReviewRepositoryImplTest.TestConfig.class)
@Transactional
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@DisplayName("ReviewRepositoryImpl のテスト")
class ReviewRepositoryImplTest {

    @Autowired
    private ReviewRepositoryImpl reviewRepository;

    @Test
    @DisplayName("審査を保存できること")
    void save_shouldPersistReview() {
        // Arrange
        FaveId faveId = new FaveId(UUID.randomUUID());
        Review review = Review.create(faveId);

        // Act
        assertDoesNotThrow(() -> reviewRepository.save(review));

        // Assert
        // 保存処理が例外なく完了することを確認
        // 実際の保存確認は統合テストまたはJpaRepositoryのテストで行う
    }

    @Test
    @DisplayName("承認済み審査を保存できること")
    void save_approvedReview_shouldPersistReview() {
        // Arrange
        FaveId faveId = new FaveId(UUID.randomUUID());
        Review review = Review.create(faveId);
        review.approve();

        // Act
        assertDoesNotThrow(() -> reviewRepository.save(review));

        // Assert
        // 保存処理が例外なく完了することを確認
    }

    @Test
    @DisplayName("却下済み審査を保存できること")
    void save_rejectedReview_shouldPersistReview() {
        // Arrange
        FaveId faveId = new FaveId(UUID.randomUUID());
        Review review = Review.create(faveId);
        review.reject();

        // Act
        assertDoesNotThrow(() -> reviewRepository.save(review));

        // Assert
        // 保存処理が例外なく完了することを確認
    }

    @TestConfiguration
    static class TestConfig {
        @Bean
        public ReviewRepositoryImpl reviewRepository(ReviewJpaRepository reviewJpaRepository) {
            return new ReviewRepositoryImpl(reviewJpaRepository);
        }
    }
}