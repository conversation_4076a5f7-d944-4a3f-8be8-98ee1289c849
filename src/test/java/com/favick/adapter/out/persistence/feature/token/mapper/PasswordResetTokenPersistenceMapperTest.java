package com.favick.adapter.out.persistence.feature.token.mapper;

import com.favick.adapter.out.persistence.feature.token.entity.PasswordResetTokenEntity;
import com.favick.domain.feature.token.model.PasswordResetToken;
import com.favick.domain.feature.token.model.PasswordResetTokenId;
import com.favick.domain.feature.token.model.TokenExpireAt;
import com.favick.domain.feature.token.model.TokenValue;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;

class PasswordResetTokenPersistenceMapperTest {

    @Test
    @DisplayName("エンティティからドメインモデルに正しく変換できる")
    void toDomain_shouldCorrectlyMapEntityToDomain() {
        // Arrange
        UUID id = UUID.randomUUID();
        UUID userId = UUID.randomUUID();
        String token = "test-password-reset-token";
        LocalDateTime expireAt = LocalDateTime.now().plusHours(2);
        LocalDateTime createdAt = LocalDateTime.now().minusMinutes(30);

        PasswordResetTokenEntity entity = PasswordResetTokenEntity.builder()
            .id(id)
            .userId(userId)
            .token(token)
            .expireAt(expireAt)
            .createdAt(createdAt)
            .build();

        // Act
        PasswordResetToken passwordResetToken = PasswordResetTokenPersistenceMapper.toDomain(entity);

        // Assert
        assertEquals(id, passwordResetToken.getId().value());
        assertEquals(userId, passwordResetToken.getUserId().value());
        assertEquals(token, passwordResetToken.getToken().value());
        assertEquals(expireAt, passwordResetToken.getExpireAt().value());
        assertEquals(createdAt, passwordResetToken.getCreatedAt());
    }

    @Test
    @DisplayName("ドメインモデルからエンティティに正しく変換できる")
    void toEntity_shouldCorrectlyMapDomainToEntity() {
        // Arrange
        UUID id = UUID.randomUUID();
        UUID userId = UUID.randomUUID();
        String token = "test-password-reset-token-2";
        LocalDateTime expireAt = LocalDateTime.now().plusHours(2);
        LocalDateTime createdAt = LocalDateTime.now().minusHours(1);

        PasswordResetToken passwordResetToken = PasswordResetToken.reconstruct(
            new PasswordResetTokenId(id),
            new UserId(userId),
            new TokenValue(token),
            new TokenExpireAt(expireAt),
            createdAt
        );

        // Act
        PasswordResetTokenEntity entity = PasswordResetTokenPersistenceMapper.toEntity(passwordResetToken);

        // Assert
        assertEquals(id, entity.getId());
        assertEquals(userId, entity.getUserId());
        assertEquals(token, entity.getToken());
        assertEquals(expireAt, entity.getExpireAt());
        assertEquals(createdAt, entity.getCreatedAt());
    }

    @Test
    @DisplayName("ドメインモデルとエンティティ間の双方向変換の一貫性")
    void bidirectionalMapping_shouldMaintainConsistency() {
        // Arrange
        UUID id = UUID.randomUUID();
        UUID userId = UUID.randomUUID();
        LocalDateTime now = LocalDateTime.now();

        // 元のドメインモデル
        PasswordResetToken originalToken = PasswordResetToken.reconstruct(
            new PasswordResetTokenId(id),
            new UserId(userId),
            new TokenValue("bidirectional-password-reset-token"),
            new TokenExpireAt(now.plusHours(2)),
            now.minusMinutes(45)
        );

        // Act
        // ドメイン → エンティティ → ドメイン の変換
        PasswordResetTokenEntity entity = PasswordResetTokenPersistenceMapper.toEntity(originalToken);
        PasswordResetToken convertedToken = PasswordResetTokenPersistenceMapper.toDomain(entity);

        // Assert
        assertEquals(originalToken.getId().value(), convertedToken.getId().value());
        assertEquals(originalToken.getUserId().value(), convertedToken.getUserId().value());
        assertEquals(originalToken.getToken().value(), convertedToken.getToken().value());
        assertEquals(originalToken.getExpireAt().value(), convertedToken.getExpireAt().value());
        assertEquals(originalToken.getCreatedAt(), convertedToken.getCreatedAt());
    }
}
