package com.favick.adapter.out.persistence.feature.admin.mapper;

import com.favick.adapter.out.persistence.feature.admin.entity.AdminEntity;
import com.favick.domain.feature.admin.model.*;
import com.favick.domain.feature.role.model.RoleId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;

class AdminPersistenceMapperTest {

    @Test
    @DisplayName("エンティティからドメインモデルに正しく変換できる")
    void toDomain_shouldCorrectlyMapEntityToDomain() {
        // Arrange
        UUID adminId = UUID.randomUUID();
        UUID roleId = UUID.randomUUID();
        String name = "テスト管理者";
        String email = "<EMAIL>";
        String password = "encodedPassword";
        LocalDateTime createdAt = LocalDateTime.now().minusDays(1);
        LocalDateTime updatedAt = LocalDateTime.now();

        AdminEntity entity = AdminEntity.builder()
            .id(adminId)
            .roleId(roleId)
            .name(name)
            .email(email)
            .password(password)
            .createdAt(createdAt)
            .updatedAt(updatedAt)
            .build();

        // Act
        Admin admin = AdminPersistenceMapper.toDomain(entity);

        // Assert
        assertEquals(adminId, admin.getId().value());
        assertEquals(roleId, admin.getRoleId().value());
        assertEquals(name, admin.getName().value());
        assertEquals(email, admin.getEmail().value());
        assertEquals(password, admin.getPassword().value());
        assertEquals(createdAt, admin.getCreatedAt());
        assertEquals(updatedAt, admin.getUpdatedAt());
    }

    @Test
    @DisplayName("ドメインモデルからエンティティに正しく変換できる")
    void toEntity_shouldCorrectlyMapDomainToEntity() {
        // Arrange
        UUID adminId = UUID.randomUUID();
        UUID roleId = UUID.randomUUID();
        String name = "テスト管理者";
        String email = "<EMAIL>";
        String password = "encodedPassword";
        LocalDateTime createdAt = LocalDateTime.now().minusDays(1);
        LocalDateTime updatedAt = LocalDateTime.now();

        Admin admin = Admin.reconstruct(
            new AdminId(adminId),
            new RoleId(roleId),
            new AdminName(name),
            new AdminEmail(email),
            new AdminPassword(password),
            createdAt,
            updatedAt
        );

        // Act
        AdminEntity entity = AdminPersistenceMapper.toEntity(admin);

        // Assert
        assertEquals(adminId, entity.getId());
        assertEquals(roleId, entity.getRoleId());
        assertEquals(name, entity.getName());
        assertEquals(email, entity.getEmail());
        assertEquals(password, entity.getPassword());
        assertEquals(createdAt, entity.getCreatedAt());
        assertEquals(updatedAt, entity.getUpdatedAt());
    }

    @Test
    @DisplayName("ドメインモデルとエンティティ間の双方向変換の一貫性")
    void bidirectionalMapping_shouldMaintainConsistency() {
        // Arrange
        UUID adminId = UUID.randomUUID();
        UUID roleId = UUID.randomUUID();
        LocalDateTime now = LocalDateTime.now();

        // 元のドメインモデル
        Admin originalAdmin = Admin.reconstruct(
            new AdminId(adminId),
            new RoleId(roleId),
            new AdminName("テスト管理者"),
            new AdminEmail("<EMAIL>"),
            new AdminPassword("encodedPassword"),
            now.minusDays(3),
            now.minusHours(2)
        );

        // Act
        // ドメイン → エンティティ → ドメイン の変換
        AdminEntity entity = AdminPersistenceMapper.toEntity(originalAdmin);
        Admin convertedAdmin = AdminPersistenceMapper.toDomain(entity);

        // Assert
        assertEquals(originalAdmin.getId().value(), convertedAdmin.getId().value());
        assertEquals(originalAdmin.getRoleId().value(), convertedAdmin.getRoleId().value());
        assertEquals(originalAdmin.getName().value(), convertedAdmin.getName().value());
        assertEquals(originalAdmin.getEmail().value(), convertedAdmin.getEmail().value());
        assertEquals(originalAdmin.getPassword().value(), convertedAdmin.getPassword().value());
        assertEquals(originalAdmin.getCreatedAt(), convertedAdmin.getCreatedAt());
        assertEquals(originalAdmin.getUpdatedAt(), convertedAdmin.getUpdatedAt());
    }
}
