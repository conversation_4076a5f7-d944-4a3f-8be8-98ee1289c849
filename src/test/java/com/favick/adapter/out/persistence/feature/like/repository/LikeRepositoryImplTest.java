package com.favick.adapter.out.persistence.feature.like.repository;

import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.like.model.Like;
import com.favick.domain.feature.like.model.LikeId;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DataJpaTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
@Import(LikeRepositoryImplTest.TestConfig.class)
@Transactional
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@DisplayName("LikeRepositoryImpl のテスト")
class LikeRepositoryImplTest {

    // テストデータのID（V1__init_schema.sqlから）
    private static final UUID USER_ID_1 = UUID.fromString("*************-4444-4444-************");
    private static final UUID USER_ID_2 = UUID.fromString("*************-4444-4444-************");
    private static final UUID USER_ID_3 = UUID.fromString("*************-4444-4444-************");
    private static final UUID FAVE_ID_1 = UUID.fromString("*************-7777-7777-************");
    private static final UUID FAVE_ID_2 = UUID.fromString("*************-7777-7777-************");
    private static final UUID FAVE_ID_3 = UUID.fromString("*************-7777-7777-************");
    @Autowired
    private LikeRepositoryImpl likeRepository;

    @Test
    @DisplayName("ユーザーIDとFaveIDでいいねを取得できる")
    void findByUserIdAndFaveId_existingLike_shouldReturnLike() {
        // Arrange
        UserId userId = new UserId(USER_ID_1);
        FaveId faveId = new FaveId(FAVE_ID_1);

        // Act
        Optional<Like> result = likeRepository.findByUserIdAndFaveId(userId, faveId);

        // Assert
        assertTrue(result.isPresent());
        Like like = result.get();
        assertEquals(USER_ID_1, like.getUserId().value());
        assertEquals(FAVE_ID_1, like.getFaveId().value());
        assertNotNull(like.getId());
        assertNotNull(like.getCreatedAt());
    }

    @Test
    @DisplayName("存在しないいいねを検索すると空のOptionalが返る")
    void findByUserIdAndFaveId_nonExistingLike_shouldReturnEmptyOptional() {
        // Arrange
        UserId userId = new UserId(UUID.randomUUID());
        FaveId faveId = new FaveId(UUID.randomUUID());

        // Act
        Optional<Like> result = likeRepository.findByUserIdAndFaveId(userId, faveId);

        // Assert
        assertFalse(result.isPresent());
    }

    @Test
    @DisplayName("ユーザーIDと複数のFaveIDでいいね一覧を取得できる")
    void findByUserIdAndFaveIds_existingLikes_shouldReturnLikes() {
        // Arrange
        UserId userId = new UserId(USER_ID_1);
        Set<FaveId> faveIds = Set.of(
            new FaveId(FAVE_ID_1),
            new FaveId(FAVE_ID_2),
            new FaveId(FAVE_ID_3)
        );

        // Act
        List<Like> result = likeRepository.findByUserIdAndFaveIds(userId, faveIds);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size()); // USER_ID_1はFAVE_ID_1とFAVE_ID_2にいいねしている

        // 結果の検証
        assertTrue(result.stream().anyMatch(like ->
            like.getUserId().value().equals(USER_ID_1) &&
                like.getFaveId().value().equals(FAVE_ID_1)));
        assertTrue(result.stream().anyMatch(like ->
            like.getUserId().value().equals(USER_ID_1) &&
                like.getFaveId().value().equals(FAVE_ID_2)));
    }

    @Test
    @DisplayName("空のFaveIDセットを渡すと空のリストが返る")
    void findByUserIdAndFaveIds_emptyFaveIds_shouldReturnEmptyList() {
        // Arrange
        UserId userId = new UserId(USER_ID_1);
        Set<FaveId> emptyFaveIds = Set.of();

        // Act
        List<Like> result = likeRepository.findByUserIdAndFaveIds(userId, emptyFaveIds);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("いいねが存在しないFaveIDセットを渡すと空のリストが返る")
    void findByUserIdAndFaveIds_noMatchingLikes_shouldReturnEmptyList() {
        // Arrange
        UserId userId = new UserId(USER_ID_1);
        Set<FaveId> nonExistentFaveIds = Set.of(
            new FaveId(UUID.randomUUID()),
            new FaveId(UUID.randomUUID())
        );

        // Act
        List<Like> result = likeRepository.findByUserIdAndFaveIds(userId, nonExistentFaveIds);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("新しいいいねを保存できる")
    void save_newLike_shouldSaveSuccessfully() {
        // Arrange
        // USER_ID_3とFAVE_ID_1の組み合わせは存在しない
        UUID newUserId = USER_ID_3;
        UUID newFaveId = FAVE_ID_1;
        Like newLike = Like.create(new UserId(newUserId), new FaveId(newFaveId));

        // Act & Assert - 例外が発生しないことを確認
        assertDoesNotThrow(() -> likeRepository.save(newLike));

        // 保存後に取得できることを確認
        Optional<Like> savedLike = likeRepository.findByUserIdAndFaveId(
            new UserId(newUserId),
            new FaveId(newFaveId)
        );
        assertTrue(savedLike.isPresent());
        assertEquals(newUserId, savedLike.get().getUserId().value());
        assertEquals(newFaveId, savedLike.get().getFaveId().value());
    }

    @Test
    @DisplayName("いいねを削除できる")
    void delete_existingLike_shouldDeleteSuccessfully() {
        // Arrange - 既存のいいねを取得
        UserId userId = new UserId(USER_ID_1);
        FaveId faveId = new FaveId(FAVE_ID_1);
        Optional<Like> existingLike = likeRepository.findByUserIdAndFaveId(userId, faveId);
        assertTrue(existingLike.isPresent());

        LikeId likeId = existingLike.get().getId();

        // Act
        assertDoesNotThrow(() -> likeRepository.delete(likeId));

        // Assert - 削除後に取得できないことを確認
        Optional<Like> deletedLike = likeRepository.findByUserIdAndFaveId(userId, faveId);
        assertFalse(deletedLike.isPresent());
    }

    @Test
    @DisplayName("存在しないいいねIDで削除を実行しても例外が発生しない")
    void delete_nonExistingLikeId_shouldNotThrowException() {
        // Arrange
        LikeId nonExistentId = new LikeId(UUID.randomUUID());

        // Act & Assert
        assertDoesNotThrow(() -> likeRepository.delete(nonExistentId));
    }

    @Test
    @DisplayName("複数のユーザーが同じFaveにいいねしている場合の検索")
    void findByUserIdAndFaveIds_multipleUsersLikingSameFave_shouldReturnCorrectLikes() {
        // Arrange - FAVE_ID_3には複数のユーザーがいいねしている
        UserId userId2 = new UserId(USER_ID_2);
        UserId userId3 = new UserId(USER_ID_3);
        Set<FaveId> faveIds = Set.of(new FaveId(FAVE_ID_3));

        // Act
        List<Like> result2 = likeRepository.findByUserIdAndFaveIds(userId2, faveIds);
        List<Like> result3 = likeRepository.findByUserIdAndFaveIds(userId3, faveIds);

        // Assert
        assertEquals(1, result2.size());
        assertEquals(1, result3.size());
        assertEquals(USER_ID_2, result2.get(0).getUserId().value());
        assertEquals(USER_ID_3, result3.get(0).getUserId().value());
        assertEquals(FAVE_ID_3, result2.get(0).getFaveId().value());
        assertEquals(FAVE_ID_3, result3.get(0).getFaveId().value());
    }

    @Test
    @DisplayName("同じユーザーが複数のFaveにいいねしている場合の検索")
    void findByUserIdAndFaveIds_sameUserLikingMultipleFaves_shouldReturnAllLikes() {
        // Arrange - USER_ID_2は複数のFaveにいいねしている
        UserId userId = new UserId(USER_ID_2);
        Set<FaveId> faveIds = Set.of(
            new FaveId(FAVE_ID_1),
            new FaveId(FAVE_ID_3)
        );

        // Act
        List<Like> result = likeRepository.findByUserIdAndFaveIds(userId, faveIds);

        // Assert
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(like ->
            like.getUserId().value().equals(USER_ID_2)));
        assertTrue(result.stream().anyMatch(like ->
            like.getFaveId().value().equals(FAVE_ID_1)));
        assertTrue(result.stream().anyMatch(like ->
            like.getFaveId().value().equals(FAVE_ID_3)));
    }

    @TestConfiguration
    static class TestConfig {
        @Bean
        public LikeRepositoryImpl likeRepository(LikeJpaRepository likeJpaRepository) {
            return new LikeRepositoryImpl(likeJpaRepository);
        }
    }
}
