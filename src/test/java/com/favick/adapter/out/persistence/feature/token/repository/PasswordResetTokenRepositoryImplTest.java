package com.favick.adapter.out.persistence.feature.token.repository;

import com.favick.adapter.out.persistence.feature.rank.entity.RankEntity;
import com.favick.adapter.out.persistence.feature.rank.repository.RankJpaRepository;
import com.favick.adapter.out.persistence.feature.token.entity.PasswordResetTokenEntity;
import com.favick.adapter.out.persistence.feature.user.entity.UserEntity;
import com.favick.adapter.out.persistence.feature.user.repository.UserJpaRepository;
import com.favick.domain.feature.token.model.PasswordResetToken;
import com.favick.domain.feature.token.model.PasswordResetTokenId;
import com.favick.domain.feature.token.model.TokenExpireAt;
import com.favick.domain.feature.token.model.TokenValue;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DataJpaTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
@Import(PasswordResetTokenRepositoryImplTest.TestConfig.class)
@Transactional
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@DisplayName("PasswordResetTokenRepositoryImpl のテスト")
class PasswordResetTokenRepositoryImplTest {

    @Autowired
    private PasswordResetTokenJpaRepository passwordResetTokenJpaRepository;
    @Autowired
    private PasswordResetTokenRepositoryImpl passwordResetTokenRepository;
    @Autowired
    private UserJpaRepository userJpaRepository;
    @Autowired
    private RankJpaRepository rankJpaRepository;

    private UUID testUserId;

    @BeforeEach
    void setUp() {
        // 既存のランクを取得（RANK_SEEDは初期データで存在する）
        RankEntity rankEntity = rankJpaRepository.findAll().stream()
            .filter(rank -> rank.getName() == com.favick.domain.feature.rank.model.RankNameValue.RANK_SEED)
            .findFirst()
            .orElseThrow(() -> new RuntimeException("RANK_SEED not found in test data"));

        // テスト用のユーザーを作成
        testUserId = UUID.randomUUID();
        UserEntity userEntity = UserEntity.builder()
            .id(testUserId)
            .rankId(rankEntity.getId())
            .name("Test User")
            .email("<EMAIL>")
            .password("password")
            .enabled(true)
            .imageUrl("")
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();
        userJpaRepository.save(userEntity);
    }

    @Test
    @DisplayName("パスワードリセットトークンを保存できる")
    void save_shouldSavePasswordResetToken() {
        // Arrange
        UUID id = UUID.randomUUID();
        LocalDateTime now = LocalDateTime.now();

        PasswordResetToken passwordResetToken = PasswordResetToken.reconstruct(
            new PasswordResetTokenId(id),
            new UserId(testUserId),
            new TokenValue("test-password-reset-token"),
            new TokenExpireAt(now.plusHours(2)),
            now
        );

        // Act
        passwordResetTokenRepository.save(passwordResetToken);

        // Assert
        Optional<PasswordResetTokenEntity> savedEntity = passwordResetTokenJpaRepository.findById(id);
        assertTrue(savedEntity.isPresent());
        assertEquals(testUserId, savedEntity.get().getUserId());
        assertEquals("test-password-reset-token", savedEntity.get().getToken());
    }

    @Test
    @DisplayName("トークン値でパスワードリセットトークンを取得できる")
    void findByToken_shouldReturnPasswordResetTokenByToken() {
        // Arrange
        UUID id = UUID.randomUUID();
        String tokenValue = "find-by-token-test";
        LocalDateTime now = LocalDateTime.now();

        PasswordResetTokenEntity entity = PasswordResetTokenEntity.builder()
            .id(id)
            .userId(testUserId)
            .token(tokenValue)
            .expireAt(now.plusHours(2))
            .createdAt(now)
            .build();

        passwordResetTokenJpaRepository.save(entity);

        // Act
        Optional<PasswordResetToken> result = passwordResetTokenRepository.findByToken(new TokenValue(tokenValue));

        // Assert
        assertTrue(result.isPresent());
        assertEquals(id, result.get().getId().value());
        assertEquals(testUserId, result.get().getUserId().value());
        assertEquals(tokenValue, result.get().getToken().value());
    }

    @Test
    @DisplayName("存在しないトークン値の場合は空のOptionalを返す")
    void findByToken_whenTokenDoesNotExist_shouldReturnEmptyOptional() {
        // Arrange
        String nonExistingToken = "non-existing-password-reset-token";

        // Act
        Optional<PasswordResetToken> result = passwordResetTokenRepository.findByToken(new TokenValue(nonExistingToken));

        // Assert
        assertFalse(result.isPresent());
    }

    @Test
    @DisplayName("ユーザーIDでパスワードリセットトークンを取得できる")
    void findByUserId_shouldReturnPasswordResetTokenByUserId() {
        // Arrange
        UUID id = UUID.randomUUID();
        LocalDateTime now = LocalDateTime.now();

        PasswordResetTokenEntity entity = PasswordResetTokenEntity.builder()
            .id(id)
            .userId(testUserId)
            .token("find-by-user-id-test")
            .expireAt(now.plusHours(2))
            .createdAt(now)
            .build();

        passwordResetTokenJpaRepository.save(entity);

        // Act
        Optional<PasswordResetToken> result = passwordResetTokenRepository.findByUserId(new UserId(testUserId));

        // Assert
        assertTrue(result.isPresent());
        assertEquals(id, result.get().getId().value());
        assertEquals(testUserId, result.get().getUserId().value());
    }

    @Test
    @DisplayName("存在しないユーザーIDの場合は空のOptionalを返す")
    void findByUserId_whenUserIdDoesNotExist_shouldReturnEmptyOptional() {
        // Arrange
        UUID nonExistingUserId = UUID.randomUUID();

        // Act
        Optional<PasswordResetToken> result = passwordResetTokenRepository.findByUserId(new UserId(nonExistingUserId));

        // Assert
        assertFalse(result.isPresent());
    }

    @Test
    @DisplayName("ユーザーIDでパスワードリセットトークンを削除できる")
    void deleteByUserId_shouldDeletePasswordResetTokenByUserId() {
        // Arrange
        UUID id = UUID.randomUUID();
        LocalDateTime now = LocalDateTime.now();

        PasswordResetTokenEntity entity = PasswordResetTokenEntity.builder()
            .id(id)
            .userId(testUserId)
            .token("delete-by-user-id-test")
            .expireAt(now.plusHours(2))
            .createdAt(now)
            .build();

        passwordResetTokenJpaRepository.save(entity);

        // 削除前に存在確認
        assertTrue(passwordResetTokenJpaRepository.existsById(id));

        // Act
        passwordResetTokenRepository.deleteByUserId(new UserId(testUserId));

        // Assert
        assertFalse(passwordResetTokenJpaRepository.existsById(id));
    }

    @Test
    @DisplayName("パスワードリセットトークンを更新できる")
    void save_shouldUpdatePasswordResetToken() {
        // Arrange
        UUID id = UUID.randomUUID();
        LocalDateTime now = LocalDateTime.now();

        // 初期データを保存
        PasswordResetTokenEntity originalEntity = PasswordResetTokenEntity.builder()
            .id(id)
            .userId(testUserId)
            .token("original-password-reset-token")
            .expireAt(now.plusHours(2))
            .createdAt(now)
            .build();

        passwordResetTokenJpaRepository.save(originalEntity);

        // 更新用のドメインモデル
        PasswordResetToken updatedToken = PasswordResetToken.reconstruct(
            new PasswordResetTokenId(id),
            new UserId(testUserId),
            new TokenValue("updated-password-reset-token"),
            new TokenExpireAt(now.plusHours(4)),
            now
        );

        // Act
        passwordResetTokenRepository.save(updatedToken);

        // Assert
        Optional<PasswordResetTokenEntity> result = passwordResetTokenJpaRepository.findById(id);
        assertTrue(result.isPresent());
        assertEquals("updated-password-reset-token", result.get().getToken());
        assertEquals(now.plusHours(4), result.get().getExpireAt());
    }

    @TestConfiguration
    static class TestConfig {
        @Bean
        public PasswordResetTokenRepositoryImpl passwordResetTokenRepository(
            PasswordResetTokenJpaRepository passwordResetTokenJpaRepository) {
            return new PasswordResetTokenRepositoryImpl(passwordResetTokenJpaRepository);
        }
    }
}
