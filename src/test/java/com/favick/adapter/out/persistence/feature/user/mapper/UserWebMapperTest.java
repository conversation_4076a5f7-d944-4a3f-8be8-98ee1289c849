package com.favick.adapter.out.persistence.feature.user.mapper;

import com.favick.adapter.out.persistence.feature.user.entity.UserEntity;
import com.favick.domain.feature.rank.model.RankId;
import com.favick.domain.feature.user.model.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;

class UserWebMapperTest {

    @Test
    @DisplayName("エンティティからドメインモデルに正しく変換できる")
    void toDomain_shouldCorrectlyMapEntityToDomain() {
        // Arrange
        UUID userId = UUID.randomUUID();
        UUID rankId = UUID.randomUUID();
        String name = "テストユーザー";
        String email = "<EMAIL>";
        String password = "encodedPassword";
        Boolean enabled = true;
        String imageUrl = "https://example.com/image.jpg";
        LocalDateTime createdAt = LocalDateTime.now().minusDays(1);
        LocalDateTime updatedAt = LocalDateTime.now();

        UserEntity entity = UserEntity.builder()
            .id(userId)
            .rankId(rankId)
            .name(name)
            .email(email)
            .password(password)
            .enabled(enabled)
            .imageUrl(imageUrl)
            .createdAt(createdAt)
            .updatedAt(updatedAt)
            .build();

        // Act
        User user = UserPersistenceMapper.toDomain(entity);

        // Assert
        assertEquals(userId, user.getId().value());
        assertEquals(rankId, user.getRankId().value());
        assertEquals(name, user.getName().value());
        assertEquals(email, user.getEmail().value());
        assertEquals(password, user.getPassword().value());
        assertEquals(enabled, user.getEnabled().value());
        assertEquals(imageUrl, user.getImageUrl().value());
        assertEquals(createdAt, user.getCreatedAt());
        assertEquals(updatedAt, user.getUpdatedAt());
    }

    @Test
    @DisplayName("ドメインモデルからエンティティに正しく変換できる")
    void toEntity_shouldCorrectlyMapDomainToEntity() {
        // Arrange
        UUID userId = UUID.randomUUID();
        UUID rankId = UUID.randomUUID();
        String name = "テストユーザー";
        String email = "<EMAIL>";
        String password = "encodedPassword";
        boolean enabled = true;
        String imageUrl = "https://example.com/image.jpg";
        LocalDateTime createdAt = LocalDateTime.now().minusDays(1);
        LocalDateTime updatedAt = LocalDateTime.now();

        User user = User.reconstruct(
            new UserId(userId),
            new RankId(rankId),
            new UserName(name),
            new UserEmail(email),
            new UserPassword(password),
            new UserEnabled(enabled),
            new UserImageUri(imageUrl),
            createdAt,
            updatedAt
        );

        // Act
        UserEntity entity = UserPersistenceMapper.toEntity(user);

        // Assert
        assertEquals(userId, entity.getId());
        assertEquals(rankId, entity.getRankId());
        assertEquals(name, entity.getName());
        assertEquals(email, entity.getEmail());
        assertEquals(password, entity.getPassword());
        assertEquals(enabled, entity.getEnabled());
        assertEquals(imageUrl, entity.getImageUrl());
        assertEquals(createdAt, entity.getCreatedAt());
        assertEquals(updatedAt, entity.getUpdatedAt());
    }

    @Test
    @DisplayName("nullのimageUrlを持つエンティティが正しく変換される")
    void toDomain_withNullImageUrl_shouldMapCorrectly() {
        // Arrange
        UUID userId = UUID.randomUUID();
        UUID rankId = UUID.randomUUID();

        UserEntity entity = UserEntity.builder()
            .id(userId)
            .rankId(rankId)
            .name("テストユーザー")
            .email("<EMAIL>")
            .password("encodedPassword")
            .enabled(true)
            .imageUrl(null) // imageUrlがnull
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();

        // Act
        User user = UserPersistenceMapper.toDomain(entity);

        // Assert
        assertEquals("", user.getImageUrl().value());
    }
}
