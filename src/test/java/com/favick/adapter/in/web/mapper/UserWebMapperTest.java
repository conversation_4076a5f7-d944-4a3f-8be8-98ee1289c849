package com.favick.adapter.in.web.mapper;

import com.favick.adapter.in.web.form.CreateUserForm;
import com.favick.application.dto.command.CreateUserCommandDto;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("UserWebMapper のテスト")
class UserWebMapperTest {

    @Test
    @DisplayName("CreateUserFormからCreateUserCommandDtoに正しく変換できる")
    void toCreateCommandDto_shouldCorrectlyMapFormToDto() {
        // Arrange
        CreateUserForm form = new CreateUserForm();
        form.setName("山田太郎");
        form.setEmail("<EMAIL>");
        form.setPassword("password123");
        form.setImageUrl("https://example.com/avatar.jpg");

        // Act
        CreateUserCommandDto result = UserWebMapper.toCreateCommandDto(form, "https://localhost:8080");

        // Assert
        assertNotNull(result);
        assertEquals("山田太郎", result.name());
        assertEquals("<EMAIL>", result.email());
        assertEquals("password123", result.password());
        assertEquals("https://example.com/avatar.jpg", result.imageUrl());
    }

    @Test
    @DisplayName("CreateUserFormのnull値が正しく処理される")
    void toCreateCommandDto_shouldHandleNullValues() {
        // Arrange
        CreateUserForm form = new CreateUserForm();
        form.setName("田中花子");
        form.setEmail("<EMAIL>");
        form.setPassword("securepass456");
        form.setImageUrl(null);

        // Act
        CreateUserCommandDto result = UserWebMapper.toCreateCommandDto(form, "https://localhost:8080");

        // Assert
        assertNotNull(result);
        assertEquals("田中花子", result.name());
        assertEquals("<EMAIL>", result.email());
        assertEquals("securepass456", result.password());
        assertNull(result.imageUrl());
    }

    @Test
    @DisplayName("空文字列が正しく処理される")
    void toCreateCommandDto_shouldHandleEmptyStrings() {
        // Arrange
        CreateUserForm form = new CreateUserForm();
        form.setName("佐藤次郎");
        form.setEmail("<EMAIL>");
        form.setPassword("mypassword789");
        form.setImageUrl("");

        // Act
        CreateUserCommandDto result = UserWebMapper.toCreateCommandDto(form, "https://localhost:8080");

        // Assert
        assertNotNull(result);
        assertEquals("佐藤次郎", result.name());
        assertEquals("<EMAIL>", result.email());
        assertEquals("mypassword789", result.password());
        assertEquals("", result.imageUrl());
    }

    @Test
    @DisplayName("特殊文字を含む値が正しく処理される")
    void toCreateCommandDto_shouldHandleSpecialCharacters() {
        // Arrange
        CreateUserForm form = new CreateUserForm();
        form.setName("O'Connor & Smith");
        form.setEmail("<EMAIL>");
        form.setPassword("P@ssw0rd!#$%");
        form.setImageUrl("https://example.com/images/user?id=123&size=large");

        // Act
        CreateUserCommandDto result = UserWebMapper.toCreateCommandDto(form, "https://localhost:8080");

        // Assert
        assertNotNull(result);
        assertEquals("O'Connor & Smith", result.name());
        assertEquals("<EMAIL>", result.email());
        assertEquals("P@ssw0rd!#$%", result.password());
        assertEquals("https://example.com/images/user?id=123&size=large", result.imageUrl());
    }

    @Test
    @DisplayName("スペースのみの値が正しく処理される")
    void toCreateCommandDto_shouldHandleWhitespaceValues() {
        // Arrange
        CreateUserForm form = new CreateUserForm();
        form.setName("   John Doe   ");
        form.setEmail("  <EMAIL>  ");
        form.setPassword("  password  ");
        form.setImageUrl("   ");

        // Act
        CreateUserCommandDto result = UserWebMapper.toCreateCommandDto(form, "https://localhost:8080");

        // Assert
        assertNotNull(result);
        assertEquals("   John Doe   ", result.name());
        assertEquals("  <EMAIL>  ", result.email());
        assertEquals("  password  ", result.password());
        assertEquals("   ", result.imageUrl());
    }

    @Test
    @DisplayName("日本語を含む値が正しく処理される")
    void toCreateCommandDto_shouldHandleJapaneseCharacters() {
        // Arrange
        CreateUserForm form = new CreateUserForm();
        form.setName("山田太郎（やまだたろう）");
        form.setEmail("山田@例え.jp");
        form.setPassword("パスワード１２３");
        form.setImageUrl("https://例え.jp/画像/アバター.png");

        // Act
        CreateUserCommandDto result = UserWebMapper.toCreateCommandDto(form, "https://localhost:8080");

        // Assert
        assertNotNull(result);
        assertEquals("山田太郎（やまだたろう）", result.name());
        assertEquals("山田@例え.jp", result.email());
        assertEquals("パスワード１２３", result.password());
        assertEquals("https://例え.jp/画像/アバター.png", result.imageUrl());
    }

    @Test
    @DisplayName("最大長の値が正しく処理される")
    void toCreateCommandDto_shouldHandleMaxLengthValues() {
        // Arrange
        CreateUserForm form = new CreateUserForm();
        String maxLengthName = "a".repeat(50);
        String maxLengthEmail = "a".repeat(243) + "@example.com"; // 255文字
        String maxLengthPassword = "a".repeat(100);
        String maxLengthImageUrl = "https://example.com/" + "a".repeat(480); // 500文字

        form.setName(maxLengthName);
        form.setEmail(maxLengthEmail);
        form.setPassword(maxLengthPassword);
        form.setImageUrl(maxLengthImageUrl);

        // Act
        CreateUserCommandDto result = UserWebMapper.toCreateCommandDto(form, "https://localhost:8080");

        // Assert
        assertNotNull(result);
        assertEquals(maxLengthName, result.name());
        assertEquals(maxLengthEmail, result.email());
        assertEquals(maxLengthPassword, result.password());
        assertEquals(maxLengthImageUrl, result.imageUrl());
    }
}
