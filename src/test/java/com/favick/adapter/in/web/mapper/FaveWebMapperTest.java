package com.favick.adapter.in.web.mapper;

import com.favick.adapter.in.web.form.PickFaveForm;
import com.favick.application.dto.command.PickFaveCommandDto;
import com.favick.application.dto.query.GetCurrentFaveQueryDto;
import com.favick.application.dto.query.ListCurrentThemeFavesQueryDto;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import com.favick.domain.feature.theme.model.ThemeTypeValue;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("FaveWebMapper のテスト")
class FaveWebMapperTest {

    @Test
    @DisplayName("言語コードとユーザーIDからListCurrentThemeFavesQueryDtoに正しく変換できる")
    void toListCurrentThemeQueryDto_shouldCorrectlyMapToDto() {
        // Arrange
        LanguageCodeValue languageCode = LanguageCodeValue.JA;
        UUID userId = UUID.randomUUID();
        Optional<UUID> userIdOptional = Optional.of(userId);

        // Act
        ListCurrentThemeFavesQueryDto result = FaveWebMapper.toListCurrentThemeQueryDto(languageCode, userIdOptional);

        // Assert
        assertNotNull(result);
        assertEquals(languageCode, result.languageCode());
        assertEquals(userIdOptional, result.userId());
        assertTrue(result.userId().isPresent());
        assertEquals(userId, result.userId().get());
    }

    @Test
    @DisplayName("言語コードと空のユーザーIDからListCurrentThemeFavesQueryDtoに正しく変換できる")
    void toListCurrentThemeQueryDto_withEmptyUserId_shouldCorrectlyMapToDto() {
        // Arrange
        LanguageCodeValue languageCode = LanguageCodeValue.EN;
        Optional<UUID> emptyUserId = Optional.empty();

        // Act
        ListCurrentThemeFavesQueryDto result = FaveWebMapper.toListCurrentThemeQueryDto(languageCode, emptyUserId);

        // Assert
        assertNotNull(result);
        assertEquals(languageCode, result.languageCode());
        assertEquals(emptyUserId, result.userId());
        assertTrue(result.userId().isEmpty());
    }

    @Test
    @DisplayName("ユーザーIDと言語コードからGetCurrentFaveQueryDtoに正しく変換できる")
    void toGetCurrentFaveQueryDto_shouldCorrectlyMapToDto() {
        // Arrange
        UUID userId = UUID.randomUUID();
        LanguageCodeValue languageCode = LanguageCodeValue.JA;

        // Act
        GetCurrentFaveQueryDto result = FaveWebMapper.toGetCurrentFaveQueryDto(userId, languageCode);

        // Assert
        assertNotNull(result);
        assertEquals(userId, result.userId());
        assertEquals(languageCode, result.languageCode());
    }

    @Test
    @DisplayName("英語言語コードでGetCurrentFaveQueryDtoに正しく変換できる")
    void toGetCurrentFaveQueryDto_withEnglish_shouldCorrectlyMapToDto() {
        // Arrange
        UUID userId = UUID.randomUUID();
        LanguageCodeValue languageCode = LanguageCodeValue.EN;

        // Act
        GetCurrentFaveQueryDto result = FaveWebMapper.toGetCurrentFaveQueryDto(userId, languageCode);

        // Assert
        assertNotNull(result);
        assertEquals(userId, result.userId());
        assertEquals(languageCode, result.languageCode());
    }

    @Test
    @DisplayName("PickFaveFormからPickFaveCommandDtoに正しく変換できる")
    void toPickCommandDto_shouldCorrectlyMapFormToDto() {
        // Arrange
        UUID userId = UUID.randomUUID();
        UUID themeId = UUID.randomUUID();
        ThemeTypeValue themeType = ThemeTypeValue.PHOTO;
        
        MultipartFile mockFile = new MockMultipartFile(
            "file",
            "test.jpg",
            "image/jpeg",
            "test image content".getBytes()
        );
        
        PickFaveForm form = new PickFaveForm();
        form.setFile(mockFile);

        // Act
        PickFaveCommandDto result = FaveWebMapper.toPickCommandDto(userId, themeId, themeType, form);

        // Assert
        assertNotNull(result);
        assertEquals(userId, result.userId());
        assertEquals(themeId, result.themeId());
        assertEquals(themeType, result.themeType());
        assertEquals(mockFile, result.file());
    }

    @Test
    @DisplayName("MOVIEテーマタイプでPickFaveCommandDtoに正しく変換できる")
    void toPickCommandDto_withMovieType_shouldCorrectlyMapFormToDto() {
        // Arrange
        UUID userId = UUID.randomUUID();
        UUID themeId = UUID.randomUUID();
        ThemeTypeValue themeType = ThemeTypeValue.MOVIE;
        
        MultipartFile mockFile = new MockMultipartFile(
            "file",
            "test.mp4",
            "video/mp4",
            "test video content".getBytes()
        );
        
        PickFaveForm form = new PickFaveForm();
        form.setFile(mockFile);

        // Act
        PickFaveCommandDto result = FaveWebMapper.toPickCommandDto(userId, themeId, themeType, form);

        // Assert
        assertNotNull(result);
        assertEquals(userId, result.userId());
        assertEquals(themeId, result.themeId());
        assertEquals(ThemeTypeValue.MOVIE, result.themeType());
        assertEquals(mockFile, result.file());
        assertEquals("test.mp4", result.file().getOriginalFilename());
        assertEquals("video/mp4", result.file().getContentType());
    }

    @Test
    @DisplayName("AUDIOテーマタイプでPickFaveCommandDtoに正しく変換できる")
    void toPickCommandDto_withAudioType_shouldCorrectlyMapFormToDto() {
        // Arrange
        UUID userId = UUID.randomUUID();
        UUID themeId = UUID.randomUUID();
        ThemeTypeValue themeType = ThemeTypeValue.AUDIO;
        
        MultipartFile mockFile = new MockMultipartFile(
            "file",
            "test.mp3",
            "audio/mpeg",
            "test audio content".getBytes()
        );
        
        PickFaveForm form = new PickFaveForm();
        form.setFile(mockFile);

        // Act
        PickFaveCommandDto result = FaveWebMapper.toPickCommandDto(userId, themeId, themeType, form);

        // Assert
        assertNotNull(result);
        assertEquals(userId, result.userId());
        assertEquals(themeId, result.themeId());
        assertEquals(ThemeTypeValue.AUDIO, result.themeType());
        assertEquals(mockFile, result.file());
        assertEquals("test.mp3", result.file().getOriginalFilename());
        assertEquals("audio/mpeg", result.file().getContentType());
    }

    @Test
    @DisplayName("異なるファイル名とコンテンツタイプが正しく処理される")
    void toPickCommandDto_withDifferentFileProperties_shouldCorrectlyMapFormToDto() {
        // Arrange
        UUID userId = UUID.randomUUID();
        UUID themeId = UUID.randomUUID();
        ThemeTypeValue themeType = ThemeTypeValue.PHOTO;
        
        MultipartFile mockFile = new MockMultipartFile(
            "uploadFile",
            "my-photo.png",
            "image/png",
            "png image content".getBytes()
        );
        
        PickFaveForm form = new PickFaveForm();
        form.setFile(mockFile);

        // Act
        PickFaveCommandDto result = FaveWebMapper.toPickCommandDto(userId, themeId, themeType, form);

        // Assert
        assertNotNull(result);
        assertEquals(userId, result.userId());
        assertEquals(themeId, result.themeId());
        assertEquals(themeType, result.themeType());
        assertEquals(mockFile, result.file());
        assertEquals("my-photo.png", result.file().getOriginalFilename());
        assertEquals("image/png", result.file().getContentType());
        assertEquals("uploadFile", result.file().getName());
    }

    @Test
    @DisplayName("すべてのテーマタイプで正しく変換できる")
    void toPickCommandDto_withAllThemeTypes_shouldCorrectlyMapFormToDto() {
        // Arrange
        UUID userId = UUID.randomUUID();
        UUID themeId = UUID.randomUUID();
        PickFaveForm form = new PickFaveForm();
        
        MultipartFile mockFile = new MockMultipartFile(
            "file",
            "test-file",
            "application/octet-stream",
            "test content".getBytes()
        );
        form.setFile(mockFile);

        // Test PHOTO
        PickFaveCommandDto photoResult = FaveWebMapper.toPickCommandDto(userId, themeId, ThemeTypeValue.PHOTO, form);
        assertEquals(ThemeTypeValue.PHOTO, photoResult.themeType());

        // Test MOVIE
        PickFaveCommandDto movieResult = FaveWebMapper.toPickCommandDto(userId, themeId, ThemeTypeValue.MOVIE, form);
        assertEquals(ThemeTypeValue.MOVIE, movieResult.themeType());

        // Test AUDIO
        PickFaveCommandDto audioResult = FaveWebMapper.toPickCommandDto(userId, themeId, ThemeTypeValue.AUDIO, form);
        assertEquals(ThemeTypeValue.AUDIO, audioResult.themeType());
    }

    @Test
    @DisplayName("異なる言語コードでクエリDTOが正しく変換される")
    void queryDtoMapping_withDifferentLanguageCodes_shouldWorkCorrectly() {
        // Arrange
        UUID userId = UUID.randomUUID();

        // Test with JA
        GetCurrentFaveQueryDto jaResult = FaveWebMapper.toGetCurrentFaveQueryDto(userId, LanguageCodeValue.JA);
        assertEquals(LanguageCodeValue.JA, jaResult.languageCode());

        // Test with EN
        GetCurrentFaveQueryDto enResult = FaveWebMapper.toGetCurrentFaveQueryDto(userId, LanguageCodeValue.EN);
        assertEquals(LanguageCodeValue.EN, enResult.languageCode());

        // Test ListCurrentThemeFavesQueryDto with JA
        ListCurrentThemeFavesQueryDto jaListResult = FaveWebMapper.toListCurrentThemeQueryDto(LanguageCodeValue.JA, Optional.of(userId));
        assertEquals(LanguageCodeValue.JA, jaListResult.languageCode());

        // Test ListCurrentThemeFavesQueryDto with EN
        ListCurrentThemeFavesQueryDto enListResult = FaveWebMapper.toListCurrentThemeQueryDto(LanguageCodeValue.EN, Optional.of(userId));
        assertEquals(LanguageCodeValue.EN, enListResult.languageCode());
    }
}
