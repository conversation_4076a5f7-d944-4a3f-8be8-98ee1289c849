package com.favick.adapter.in.web.mapper;

import com.favick.adapter.in.web.form.CreateThemeForm;
import com.favick.adapter.in.web.form.UpdateThemeForm;
import com.favick.application.dto.command.CreateThemeCommandDto;
import com.favick.application.dto.command.LocalizedContent;
import com.favick.application.dto.command.UpdateThemeCommandDto;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("ThemeWebMapper のテスト")
class ThemeWebMapperTest {

    @Test
    @DisplayName("CreateThemeFormからCreateThemeCommandDtoに正しく変換できる")
    void toCreateCommandDto_shouldCorrectlyMapFormToDto() {
        // Arrange
        CreateThemeForm form = new CreateThemeForm();
        form.setTitleJa("春のテーマ");
        form.setDescriptionJa("春の作品を募集します");
        form.setTitleEn("Spring Theme");
        form.setDescriptionEn("Submit your spring works");
        form.setType("PHOTO");
        form.setStartDate(LocalDate.of(2025, 4, 1));

        // Act
        CreateThemeCommandDto result = ThemeWebMapper.toCreateCommandDto(form);

        // Assert
        assertNotNull(result);

        // ローカライゼーションのマッピング確認
        assertNotNull(result.localizations());
        assertEquals(2, result.localizations().size());

        LocalizedContent jaContent = result.localizations().get(LanguageCodeValue.JA);
        assertNotNull(jaContent);
        assertEquals("春のテーマ", jaContent.title());
        assertEquals("春の作品を募集します", jaContent.description());

        LocalizedContent enContent = result.localizations().get(LanguageCodeValue.EN);
        assertNotNull(enContent);
        assertEquals("Spring Theme", enContent.title());
        assertEquals("Submit your spring works", enContent.description());

        // その他のフィールド確認
        assertEquals("PHOTO", result.type());
        assertEquals(LocalDate.of(2025, 4, 1), result.startDate());
    }

    @Test
    @DisplayName("CreateThemeFormのnull値が正しく処理される")
    void toCreateCommandDto_shouldHandleNullValues() {
        // Arrange
        CreateThemeForm form = new CreateThemeForm();
        form.setTitleJa(null);
        form.setDescriptionJa("春の作品を募集します");
        form.setTitleEn("Spring Theme");
        form.setDescriptionEn(null);
        form.setType("MOVIE");
        form.setStartDate(LocalDate.of(2025, 7, 1));

        // Act
        CreateThemeCommandDto result = ThemeWebMapper.toCreateCommandDto(form);

        // Assert
        assertNotNull(result);

        // null値が正しくマッピングされることを確認
        LocalizedContent jaContent = result.localizations().get(LanguageCodeValue.JA);
        assertNotNull(jaContent);
        assertNull(jaContent.title());
        assertEquals("春の作品を募集します", jaContent.description());

        LocalizedContent enContent = result.localizations().get(LanguageCodeValue.EN);
        assertNotNull(enContent);
        assertEquals("Spring Theme", enContent.title());
        assertNull(enContent.description());

        assertEquals("MOVIE", result.type());
        assertEquals(LocalDate.of(2025, 7, 1), result.startDate());
    }

    @Test
    @DisplayName("CreateThemeCommandDtoのヘルパーメソッドが正しく動作する")
    void createCommandDto_helperMethods_shouldWorkCorrectly() {
        // Arrange
        CreateThemeForm form = new CreateThemeForm();
        form.setTitleJa("秋のテーマ");
        form.setDescriptionJa("秋の作品を募集します");
        form.setTitleEn("Autumn Theme");
        form.setDescriptionEn("Submit your autumn works");
        form.setType("AUDIO");
        form.setStartDate(LocalDate.of(2025, 10, 1));

        // Act
        CreateThemeCommandDto result = ThemeWebMapper.toCreateCommandDto(form);

        // Assert
        LocalizedContent jaContent = result.localizations().get(LanguageCodeValue.JA);
        assertNotNull(jaContent);
        assertEquals("秋のテーマ", jaContent.title());
        assertEquals("秋の作品を募集します", jaContent.description());

        LocalizedContent enContent = result.localizations().get(LanguageCodeValue.EN);
        assertNotNull(enContent);
        assertEquals("Autumn Theme", enContent.title());
        assertEquals("Submit your autumn works", enContent.description());
    }

    @Test
    @DisplayName("UpdateThemeFormからUpdateThemeCommandDtoに正しく変換できる")
    void toUpdateCommandDto_shouldCorrectlyMapFormToDto() {
        // Arrange
        UUID themeId = UUID.randomUUID();
        UpdateThemeForm form = new UpdateThemeForm();
        form.setTitleJa("夏のテーマ");
        form.setDescriptionJa("夏の作品を募集します");
        form.setTitleEn("Summer Theme");
        form.setDescriptionEn("Submit your summer works");
        form.setType("PHOTO");
        form.setStartDate(LocalDate.of(2025, 7, 1));

        // Act
        UpdateThemeCommandDto result = ThemeWebMapper.toUpdateCommandDto(themeId, form);

        // Assert
        assertNotNull(result);

        // IDの確認
        assertEquals(themeId, result.id());

        // ローカライゼーションのマッピング確認
        assertNotNull(result.localizations());
        assertEquals(2, result.localizations().size());

        LocalizedContent jaContent = result.localizations().get(LanguageCodeValue.JA);
        assertNotNull(jaContent);
        assertEquals("夏のテーマ", jaContent.title());
        assertEquals("夏の作品を募集します", jaContent.description());

        LocalizedContent enContent = result.localizations().get(LanguageCodeValue.EN);
        assertNotNull(enContent);
        assertEquals("Summer Theme", enContent.title());
        assertEquals("Submit your summer works", enContent.description());

        // その他のフィールド確認
        assertEquals("PHOTO", result.type());
        assertEquals(LocalDate.of(2025, 7, 1), result.startDate());
    }

    @Test
    @DisplayName("UpdateThemeFormのnull値が正しく処理される")
    void toUpdateCommandDto_shouldHandleNullValues() {
        // Arrange
        UUID themeId = UUID.randomUUID();
        UpdateThemeForm form = new UpdateThemeForm();
        form.setTitleJa("冬のテーマ");
        form.setDescriptionJa(null);
        form.setTitleEn(null);
        form.setDescriptionEn("Submit your winter works");
        form.setType("MOVIE");
        form.setStartDate(LocalDate.of(2026, 1, 1));

        // Act
        UpdateThemeCommandDto result = ThemeWebMapper.toUpdateCommandDto(themeId, form);

        // Assert
        assertNotNull(result);
        assertEquals(themeId, result.id());

        // null値が正しくマッピングされることを確認
        LocalizedContent jaContent = result.localizations().get(LanguageCodeValue.JA);
        assertNotNull(jaContent);
        assertEquals("冬のテーマ", jaContent.title());
        assertNull(jaContent.description());

        LocalizedContent enContent = result.localizations().get(LanguageCodeValue.EN);
        assertNotNull(enContent);
        assertNull(enContent.title());
        assertEquals("Submit your winter works", enContent.description());

        assertEquals("MOVIE", result.type());
        assertEquals(LocalDate.of(2026, 1, 1), result.startDate());
    }

    @Test
    @DisplayName("UpdateThemeCommandDtoのヘルパーメソッドが正しく動作する")
    void updateCommandDto_helperMethods_shouldWorkCorrectly() {
        // Arrange
        UUID themeId = UUID.randomUUID();
        UpdateThemeForm form = new UpdateThemeForm();
        form.setTitleJa("更新されたテーマ");
        form.setDescriptionJa("更新された説明");
        form.setTitleEn("Updated Theme");
        form.setDescriptionEn("Updated description");
        form.setType("AUDIO");
        form.setStartDate(LocalDate.of(2025, 12, 1));

        // Act
        UpdateThemeCommandDto result = ThemeWebMapper.toUpdateCommandDto(themeId, form);

        // Assert
        LocalizedContent jaContent = result.localizations().get(LanguageCodeValue.JA);
        assertNotNull(jaContent);
        assertEquals("更新されたテーマ", jaContent.title());
        assertEquals("更新された説明", jaContent.description());

        LocalizedContent enContent = result.localizations().get(LanguageCodeValue.EN);
        assertNotNull(enContent);
        assertEquals("Updated Theme", enContent.title());
        assertEquals("Updated description", enContent.description());
    }

    @Test
    @DisplayName("空文字列が正しく処理される")
    void shouldHandleEmptyStrings() {
        // Arrange
        CreateThemeForm form = new CreateThemeForm();
        form.setTitleJa("");
        form.setDescriptionJa("説明");
        form.setTitleEn("Title");
        form.setDescriptionEn("");
        form.setType("PHOTO");
        form.setStartDate(LocalDate.of(2025, 5, 1));

        // Act
        CreateThemeCommandDto result = ThemeWebMapper.toCreateCommandDto(form);

        // Assert
        LocalizedContent jaContent = result.localizations().get(LanguageCodeValue.JA);
        assertNotNull(jaContent);
        assertEquals("", jaContent.title());
        assertEquals("説明", jaContent.description());

        LocalizedContent enContent = result.localizations().get(LanguageCodeValue.EN);
        assertNotNull(enContent);
        assertEquals("Title", enContent.title());
        assertEquals("", enContent.description());
    }

    @Test
    @DisplayName("異なるテーマタイプが正しく処理される")
    void shouldHandleDifferentThemeTypes() {
        // Arrange & Act & Assert for PHOTO
        CreateThemeForm photoForm = new CreateThemeForm();
        photoForm.setTitleJa("写真テーマ");
        photoForm.setDescriptionJa("写真作品募集");
        photoForm.setTitleEn("Photo Theme");
        photoForm.setDescriptionEn("Photo submission");
        photoForm.setType("PHOTO");
        photoForm.setStartDate(LocalDate.of(2025, 3, 1));

        CreateThemeCommandDto photoResult = ThemeWebMapper.toCreateCommandDto(photoForm);
        assertEquals("PHOTO", photoResult.type());

        // Arrange & Act & Assert for MOVIE
        CreateThemeForm movieForm = new CreateThemeForm();
        movieForm.setTitleJa("動画テーマ");
        movieForm.setDescriptionJa("動画作品募集");
        movieForm.setTitleEn("Movie Theme");
        movieForm.setDescriptionEn("Movie submission");
        movieForm.setType("MOVIE");
        movieForm.setStartDate(LocalDate.of(2025, 6, 1));

        CreateThemeCommandDto movieResult = ThemeWebMapper.toCreateCommandDto(movieForm);
        assertEquals("MOVIE", movieResult.type());

        // Arrange & Act & Assert for AUDIO
        CreateThemeForm audioForm = new CreateThemeForm();
        audioForm.setTitleJa("音声テーマ");
        audioForm.setDescriptionJa("音声作品募集");
        audioForm.setTitleEn("Audio Theme");
        audioForm.setDescriptionEn("Audio submission");
        audioForm.setType("AUDIO");
        audioForm.setStartDate(LocalDate.of(2025, 9, 1));

        CreateThemeCommandDto audioResult = ThemeWebMapper.toCreateCommandDto(audioForm);
        assertEquals("AUDIO", audioResult.type());
    }
}
