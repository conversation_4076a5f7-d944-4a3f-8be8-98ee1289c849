package com.favick.common.util;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("SecurityLogUtil のテスト")
class SecurityLogUtilTest {

    @Test
    @DisplayName("正常系：メールアドレスのマスキングが正しく動作する")
    void maskEmail_validEmail_shouldMaskCorrectly() {
        // Act & Assert
        assertEquals("u***@example.com", SecurityLogUtil.maskEmail("<EMAIL>"));
        assertEquals("a***@company.co.jp", SecurityLogUtil.maskEmail("<EMAIL>"));
        assertEquals("t***@test.org", SecurityLogUtil.maskEmail("<EMAIL>"));
    }

    @Test
    @DisplayName("異常系：無効なメールアドレスは完全にマスキングされる")
    void maskEmail_invalidEmail_shouldReturnMasked() {
        // Act & Assert
        assertEquals("***", SecurityLogUtil.maskEmail(null));
        assertEquals("***", SecurityLogUtil.maskEmail(""));
        assertEquals("***", SecurityLogUtil.maskEmail("ab"));
        assertEquals("***", SecurityLogUtil.maskEmail("invalid"));
        assertEquals("***", SecurityLogUtil.maskEmail("@example.com"));
    }

    @Test
    @DisplayName("正常系：電話番号のマスキングが正しく動作する")
    void maskPhoneNumber_validPhoneNumber_shouldMaskCorrectly() {
        // Act & Assert
        assertEquals("090-****-5678", SecurityLogUtil.maskPhoneNumber("090-1234-5678"));
        assertEquals("090-****-5678", SecurityLogUtil.maskPhoneNumber("09012345678"));
        assertEquals("031-****-5678", SecurityLogUtil.maskPhoneNumber("03-1234-5678"));

        // 10桁未満
        assertEquals("12***78", SecurityLogUtil.maskPhoneNumber("123456778"));
    }

    @Test
    @DisplayName("異常系：無効な電話番号は完全にマスキングされる")
    void maskPhoneNumber_invalidPhoneNumber_shouldReturnMasked() {
        // Act & Assert
        assertEquals("***", SecurityLogUtil.maskPhoneNumber(null));
        assertEquals("***", SecurityLogUtil.maskPhoneNumber(""));
        assertEquals("***", SecurityLogUtil.maskPhoneNumber("12345"));
        assertEquals("***", SecurityLogUtil.maskPhoneNumber("invalid"));
    }

    @Test
    @DisplayName("正常系：クレジットカード番号のマスキングが正しく動作する")
    void maskCardNumber_validCardNumber_shouldMaskCorrectly() {
        // Act & Assert
        assertEquals("1234-****-****-5678", SecurityLogUtil.maskCardNumber("1234567890125678"));
        assertEquals("1234-****-****-5678", SecurityLogUtil.maskCardNumber("1234-5678-9012-5678"));
        assertEquals("1234-****-****-5678", SecurityLogUtil.maskCardNumber("1234 5678 9012 5678"));

        // 16桁未満
        assertEquals("1234***8901", SecurityLogUtil.maskCardNumber("12345678901"));
    }

    @Test
    @DisplayName("異常系：無効なクレジットカード番号は完全にマスキングされる")
    void maskCardNumber_invalidCardNumber_shouldReturnMasked() {
        // Act & Assert
        assertEquals("***", SecurityLogUtil.maskCardNumber(null));
        assertEquals("***", SecurityLogUtil.maskCardNumber(""));
        assertEquals("***", SecurityLogUtil.maskCardNumber("1234567"));
        assertEquals("***", SecurityLogUtil.maskCardNumber("invalid"));
    }

    @Test
    @DisplayName("正常系：トークンのマスキングが正しく動作する")
    void maskToken_validToken_shouldMaskCorrectly() {
        // Act & Assert
        assertEquals("abcd...3456", SecurityLogUtil.maskToken("abcdef123456"));
        assertEquals("1234...9012", SecurityLogUtil.maskToken("1234567890123456789012"));
        assertEquals("test...oken", SecurityLogUtil.maskToken("test_token"));
    }

    @Test
    @DisplayName("異常系：短いトークンは完全にマスキングされる")
    void maskToken_shortToken_shouldReturnMasked() {
        // Act & Assert
        assertEquals("***", SecurityLogUtil.maskToken(null));
        assertEquals("***", SecurityLogUtil.maskToken(""));
        assertEquals("***", SecurityLogUtil.maskToken("short"));
        assertEquals("***", SecurityLogUtil.maskToken("1234567"));
    }

    @Test
    @DisplayName("正常系：ユーザーIDのマスキングが正しく動作する")
    void maskUserId_validUserId_shouldMaskCorrectly() {
        // Act & Assert
        // UUID形式
        assertEquals("12345678-****-****-****-123456789012",
            SecurityLogUtil.maskUserId("12345678-1234-1234-1234-123456789012"));

        // その他の形式（12文字以上）
        assertEquals("1234***9012", SecurityLogUtil.maskUserId("123456789012"));
        assertEquals("test***r_id", SecurityLogUtil.maskUserId("test_user_id"));
    }

    @Test
    @DisplayName("異常系：短いユーザーIDは完全にマスキングされる")
    void maskUserId_shortUserId_shouldReturnMasked() {
        // Act & Assert
        assertEquals("***", SecurityLogUtil.maskUserId(null));
        assertEquals("***", SecurityLogUtil.maskUserId(""));
        assertEquals("***", SecurityLogUtil.maskUserId("short"));
        assertEquals("***", SecurityLogUtil.maskUserId("1234567")); // 7文字（8文字未満）
    }

    @Test
    @DisplayName("正常系：IPアドレスのマスキングが正しく動作する")
    void maskIpAddress_validIp_shouldMaskCorrectly() {
        // Act & Assert
        // IPv4
        assertEquals("192.168.***", SecurityLogUtil.maskIpAddress("*************"));
        assertEquals("10.0.***", SecurityLogUtil.maskIpAddress("********"));

        // IPv6（簡易）
        assertEquals("2001:db8:***:***", SecurityLogUtil.maskIpAddress("2001:db8:85a3:8d3:1319:8a2e:370:7348"));
    }

    @Test
    @DisplayName("異常系：無効なIPアドレスは完全にマスキングされる")
    void maskIpAddress_invalidIp_shouldReturnMasked() {
        // Act & Assert
        assertEquals("***", SecurityLogUtil.maskIpAddress(null));
        assertEquals("***", SecurityLogUtil.maskIpAddress(""));
        assertEquals("***", SecurityLogUtil.maskIpAddress("invalid"));
        assertEquals("***", SecurityLogUtil.maskIpAddress("192.168"));
    }

    @Test
    @DisplayName("異常系：SecurityLogUtilのインスタンス化は禁止されている")
    void constructor_shouldThrowUnsupportedOperationException() {
        // Act & Assert
        var exception = assertThrows(Exception.class, () -> {
            // リフレクションを使用してコンストラクタを呼び出し
            var constructor = SecurityLogUtil.class.getDeclaredConstructor();
            constructor.setAccessible(true);
            constructor.newInstance();
        });

        // InvocationTargetExceptionの原因がUnsupportedOperationExceptionであることを確認
        assertInstanceOf(UnsupportedOperationException.class, exception.getCause());
        assertEquals("This is a utility class and cannot be instantiated", exception.getCause().getMessage());
    }

    @Test
    @DisplayName("正常系：実際のユースケースでのマスキング動作確認")
    void realWorldUseCases_shouldMaskCorrectly() {
        // Arrange
        String email = "<EMAIL>";
        String token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9";
        String userId = "550e8400-e29b-41d4-a716-446655440000";
        String ipAddress = "*************";

        // Act & Assert
        assertEquals("j***@company.com", SecurityLogUtil.maskEmail(email));
        assertEquals("eyJh...VCJ9", SecurityLogUtil.maskToken(token));
        assertEquals("550e8400-****-****-****-446655440000", SecurityLogUtil.maskUserId(userId));
        assertEquals("192.168.***", SecurityLogUtil.maskIpAddress(ipAddress));
    }
}
