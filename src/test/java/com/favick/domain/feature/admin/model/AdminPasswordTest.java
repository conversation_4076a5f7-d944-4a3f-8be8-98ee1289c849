package com.favick.domain.feature.admin.model;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

class AdminPasswordTest {

    @Test
    @DisplayName("正常系：有効なエンコード済み管理者パスワードを作成できる")
    void validEncodedPassword_shouldBeCreated() {
        // Arrange
        String encodedPassword = "$2a$10$N9qo8uLOickgx2ZMRZoMy.Mrq1V1z4M7J9iJ8vqjJQ7Fq";

        // Act
        AdminPassword password = new AdminPassword(encodedPassword);

        // Assert
        assertEquals(encodedPassword, password.value());
    }

    @Test
    @DisplayName("正常系：nullでもエンコード済みパスワードとして受け入れる")
    void nullPassword_shouldBeAccepted() {
        // エンコード済みパスワードはnullも許可（システム内部での使用）
        AdminPassword password = new AdminPassword(null);
        assertNull(password.value());
    }

    @Test
    @DisplayName("正常系：空文字でもエンコード済みパスワードとして受け入れる")
    void emptyPassword_shouldBeAccepted() {
        // エンコード済みパスワードは空文字も許可（システム内部での使用）
        AdminPassword password = new AdminPassword("");
        assertEquals("", password.value());
    }

    @Test
    @DisplayName("正常系：長いパスワードでもエンコード済みパスワードとして受け入れる")
    void longPassword_shouldBeAccepted() {
        // エンコード済みパスワードは長さ制限なし（ハッシュ化済み）
        String longPassword = "a".repeat(256);
        AdminPassword password = new AdminPassword(longPassword);
        assertEquals(longPassword, password.value());
    }

    @Test
    @DisplayName("changePassword()テスト：新しいパスワードに変更できる")
    void changePassword_shouldReturnNewPassword() {
        // Arrange
        String oldEncoded = "$2a$10$N9qo8uLOickgx2ZMRZoMy.Mrq1V1z4M7J9iJ8vqjJQ7Fq";
        String newEncoded = "$2a$10$B6uL8vNQ1oPZ7hYwXk3Z0e";
        AdminPassword oldPassword = new AdminPassword(oldEncoded);
        AdminPassword newPassword = new AdminPassword(newEncoded);

        // Act
        AdminPassword changed = oldPassword.changePassword(newPassword);

        // Assert
        assertEquals(newEncoded, changed.value());
    }
}
