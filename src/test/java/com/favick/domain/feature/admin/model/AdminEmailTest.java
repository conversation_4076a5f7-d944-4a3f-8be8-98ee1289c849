package com.favick.domain.feature.admin.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;

@DisplayName("AdminEmail のテスト")
class AdminEmailTest {
    @Test
    @DisplayName("有効なメールアドレス形式を受け付ける")
    void testValidEmailFormats() {
        // Jakarta Emailバリデーションと同等のテストケース
        assertDoesNotThrow(() -> new AdminEmail("<EMAIL>"));
        assertDoesNotThrow(() -> new AdminEmail("<EMAIL>"));
        assertDoesNotThrow(() -> new AdminEmail("<EMAIL>"));
        assertDoesNotThrow(() -> new AdminEmail("<EMAIL>"));
        assertDoesNotThrow(() -> new AdminEmail("<EMAIL>"));
        assertDoesNotThrow(() -> new AdminEmail("<EMAIL>"));
    }

    @ParameterizedTest
    @DisplayName("無効なメールアドレス形式を拒否する")
    @ValueSource(strings = {
        "invalid",
        "admin@",
        "@example.com",
        "admin@.com",
        "<EMAIL>",
        "<EMAIL>.",
        "あいうえお@example.com",
        "<EMAIL>", // 65文字ローカル部
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa" // 256文字
    })
    void testInvalidEmailFormats(String invalidEmail) {
        assertThrows(ValueObjectException.class, () -> new AdminEmail(invalidEmail));
    }

    @Test
    @DisplayName("nullや空文字を拒否する")
    void testRejectNullOrEmpty() {
        assertThrows(ValueObjectException.class, () -> new AdminEmail(null));
        assertThrows(ValueObjectException.class, () -> new AdminEmail(""));
        assertThrows(ValueObjectException.class, () -> new AdminEmail("   "));
    }
}
