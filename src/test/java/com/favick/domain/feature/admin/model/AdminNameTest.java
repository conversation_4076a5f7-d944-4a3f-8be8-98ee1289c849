package com.favick.domain.feature.admin.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

@DisplayName("AdminName のテスト")
class AdminNameTest {
    @Test
    @DisplayName("正常系：50文字以内の有効な管理者名を指定した場合、インスタンスが生成される")
    void validName_shouldCreateObject() {
        // Arrange
        String validName = "システム管理者";

        // Act
        AdminName adminName = new AdminName(validName);

        // Assert
        assertEquals(validName, adminName.value());
    }

    @Test
    @DisplayName("正常系：ちょうど50文字の管理者名を指定した場合、インスタンスが生成される")
    void maxLengthName_shouldCreateObject() {
        // Arrange
        String maxLengthName = "a".repeat(50);

        // Act
        AdminName adminName = new AdminName(maxLengthName);

        // Assert
        assertEquals(maxLengthName, adminName.value());
    }

    @Test
    @DisplayName("異常系：nullを指定した場合、例外が発生する")
    void nullName_shouldThrowException() {
        assertThrows(ValueObjectException.class, () -> new AdminName(null));
    }

    @Test
    @DisplayName("異常系：空文字を指定した場合、例外が発生する")
    void emptyName_shouldThrowException() {
        assertThrows(ValueObjectException.class, () -> new AdminName(""));
    }

    @Test
    @DisplayName("異常系：空白のみを指定した場合、例外が発生する")
    void blankName_shouldThrowException() {
        assertThrows(ValueObjectException.class, () -> new AdminName("   "));
    }

    @Test
    @DisplayName("異常系：51文字以上の管理者名を指定した場合、例外が発生する")
    void tooLongName_shouldThrowException() {
        // Arrange
        String tooLongName = "a".repeat(51);

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new AdminName(tooLongName));
    }
}
