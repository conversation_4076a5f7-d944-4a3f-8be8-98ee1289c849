package com.favick.domain.feature.admin.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

class RawAdminPasswordTest {

    @Test
    @DisplayName("正常系：有効な管理者パスワードを作成できる")
    void validAdminPassword_shouldBeCreated() {
        // Arrange
        String validPassword = "Admin@123456";

        // Act
        RawAdminPassword password = new RawAdminPassword(validPassword);

        // Assert
        assertEquals(validPassword, password.value());
    }

    @Test
    @DisplayName("異常系：nullは許可しない")
    void nullPassword_shouldThrowException() {
        assertThrows(ValueObjectException.class, () -> {
            new RawAdminPassword(null);
        });
    }

    @Test
    @DisplayName("異常系：空文字は許可しない")
    void emptyPassword_shouldThrowException() {
        assertThrows(ValueObjectException.class, () -> {
            new RawAdminPassword("");
        });
    }

    @Test
    @DisplayName("異常系：短すぎるパスワードは許可しない")
    void tooShortPassword_shouldThrowException() {
        assertThrows(ValueObjectException.class, () -> {
            new RawAdminPassword("Short1@");
        });
    }

    @Test
    @DisplayName("異常系：長すぎるパスワードは許可しない")
    void tooLongPassword_shouldThrowException() {
        assertThrows(ValueObjectException.class, () -> {
            new RawAdminPassword("a".repeat(73));
        });
    }

    @ParameterizedTest
    @ValueSource(strings = {"nouppercase1@", "NOLOWERCASE1@", "NoNumbers@", "NoSpecialChars1"})
    @DisplayName("異常系：パターンに一致しないパスワードは許可しない")
    void invalidPatternPassword_shouldThrowException(String invalidPassword) {
        assertThrows(ValueObjectException.class, () -> {
            new RawAdminPassword(invalidPassword);
        });
    }

    @ParameterizedTest
    @ValueSource(strings = {"Admin1234", "Password1", "Admin@123", "Welcome1"})
    @DisplayName("異常系：一般的なパスワードは許可しない")
    void commonPassword_shouldThrowException(String commonPassword) {
        assertThrows(ValueObjectException.class, () -> {
            new RawAdminPassword(commonPassword);
        });
    }
}
