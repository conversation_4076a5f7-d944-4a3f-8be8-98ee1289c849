package com.favick.domain.feature.admin.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class AdminIdTest {
    @Test
    @DisplayName("正常系：generate()で有効なAdminIdを生成できる")
    void generate_shouldCreateValidAdminId() {
        // Act
        AdminId adminId = AdminId.generate();

        // Assert
        assertNotNull(adminId);
        assertNotNull(adminId.value());
        assertInstanceOf(UUID.class, adminId.value());
    }

    @Test
    @DisplayName("異常系：nullを指定した場合、例外が発生する")
    void nullValue_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new AdminId(null));
    }

    @Test
    @DisplayName("等価性テスト：同じUUIDで生成したAdminIdは等しい")
    void sameUuid_shouldBeEqual() {
        // Arrange
        UUID uuid = UUID.randomUUID();

        // Act
        AdminId id1 = new AdminId(uuid);
        AdminId id2 = new AdminId(uuid);

        // Assert
        assertEquals(id1, id2);
        assertEquals(id1.hashCode(), id2.hashCode());
    }

    @Test
    @DisplayName("非等価性テスト：異なるUUIDで生成したAdminIdは等しくない")
    void differentUuid_shouldNotBeEqual() {
        // Act
        AdminId id1 = AdminId.generate();
        AdminId id2 = AdminId.generate();

        // Assert
        assertNotEquals(id1, id2);
    }

    @Test
    @DisplayName("toString()テスト：UUID文字列を返す")
    void toString_shouldReturnUuidString() {
        // Arrange
        UUID uuid = UUID.randomUUID();
        AdminId adminId = new AdminId(uuid);

        // Act & Assert
        String result = adminId.toString();
        assertTrue(result.contains("AdminId"));
        assertTrue(result.contains(uuid.toString()));
    }
}
