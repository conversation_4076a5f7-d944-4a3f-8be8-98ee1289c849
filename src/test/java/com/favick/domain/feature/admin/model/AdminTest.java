package com.favick.domain.feature.admin.model;

import com.favick.domain.feature.role.model.RoleId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@DisplayName("Admin のテスト")
class AdminTest {
    private static final RoleId TEST_ROLE_ID = RoleId.generate();
    private static final AdminName TEST_NAME = new AdminName("テスト管理者");
    private static final AdminEmail TEST_EMAIL = new AdminEmail("<EMAIL>");
    private static final AdminPassword TEST_PASSWORD = new AdminPassword("$2a$10$encodedPassword");

    @Test
    @DisplayName("コンストラクタテスト：全フィールドを設定できる")
    void constructor_shouldSetAllFields() {
        // Arrange
        AdminId id = AdminId.generate();
        LocalDateTime now = LocalDateTime.now();

        // Act
        Admin admin = Admin.reconstruct(
            id,
            TEST_ROLE_ID,
            TEST_NAME,
            TEST_EMAIL,
            TEST_PASSWORD,
            now,
            now
        );

        // Assert
        assertEquals(id, admin.getId());
        assertEquals(TEST_ROLE_ID, admin.getRoleId());
        assertEquals(TEST_NAME, admin.getName());
        assertEquals(TEST_EMAIL, admin.getEmail());
        assertEquals(TEST_PASSWORD, admin.getPassword());
        assertEquals(now, admin.getCreatedAt());
        assertEquals(now, admin.getUpdatedAt());
    }

    @Test
    @DisplayName("プロフィール更新テスト：名前とメールを更新できる")
    void updateProfile_shouldUpdateFields() {
        // Arrange
        Admin admin = Admin.reconstruct(
            AdminId.generate(),
            TEST_ROLE_ID,
            TEST_NAME,
            TEST_EMAIL,
            TEST_PASSWORD,
            LocalDateTime.now(),
            LocalDateTime.now()
        );
        AdminName newName = new AdminName("新しい管理者名");
        AdminEmail newEmail = new AdminEmail("<EMAIL>");
        LocalDateTime originalUpdatedAt = admin.getUpdatedAt();

        // Act
        admin.updateProfile(newName, newEmail);

        // Assert
        assertEquals(newName, admin.getName());
        assertEquals(newEmail, admin.getEmail());
        assertTrue(admin.getUpdatedAt().isAfter(originalUpdatedAt));
    }

    @Test
    @DisplayName("パスワード変更テスト：新しいパスワードに更新できる")
    void changePassword_shouldUpdatePassword() {
        // Arrange
        Admin admin = Admin.reconstruct(
            AdminId.generate(),
            TEST_ROLE_ID,
            TEST_NAME,
            TEST_EMAIL,
            TEST_PASSWORD,
            LocalDateTime.now(),
            LocalDateTime.now()
        );
        AdminPassword newPassword = new AdminPassword("$2a$10$newEncodedPassword");
        LocalDateTime originalUpdatedAt = admin.getUpdatedAt();

        // Act
        admin.changePassword(newPassword);

        // Assert
        assertEquals(newPassword, admin.getPassword());
        assertTrue(admin.getUpdatedAt().isAfter(originalUpdatedAt));
    }

    @Test
    @DisplayName("権限変更テスト：新しい権限に更新できる")
    void changeRole_shouldUpdateRoleId() {
        // Arrange
        Admin admin = Admin.reconstruct(
            AdminId.generate(),
            TEST_ROLE_ID,
            TEST_NAME,
            TEST_EMAIL,
            TEST_PASSWORD,
            LocalDateTime.now(),
            LocalDateTime.now()
        );
        RoleId newRoleId = RoleId.generate();
        LocalDateTime originalUpdatedAt = admin.getUpdatedAt();

        // Act
        admin.changeRole(newRoleId);

        // Assert
        assertEquals(newRoleId, admin.getRoleId());
        assertTrue(admin.getUpdatedAt().isAfter(originalUpdatedAt));
    }

    @Test
    @DisplayName("toString()テスト：主要なフィールドを含む文字列を返す")
    void toString_shouldContainMainFields() {
        // Arrange
        Admin admin = Admin.reconstruct(
            AdminId.generate(),
            TEST_ROLE_ID,
            TEST_NAME,
            TEST_EMAIL,
            TEST_PASSWORD,
            LocalDateTime.now(),
            LocalDateTime.now()
        );

        // Act
        String result = admin.toString();

        // Assert
        assertTrue(result.contains("Admin{"));
        assertTrue(result.contains("id=" + admin.getId().value()));
        assertTrue(result.contains("name=" + TEST_NAME.value()));
        assertTrue(result.contains("email=" + TEST_EMAIL.value()));
        assertTrue(result.contains("roleId=" + TEST_ROLE_ID.value()));
    }
}
