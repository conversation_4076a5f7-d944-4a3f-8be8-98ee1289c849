package com.favick.domain.feature.fave.model;

import com.favick.domain.exception.ValueObjectException;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("Fave のテスト")
class FaveTest {
    @Test
    @DisplayName("正常系：Faveが正しく生成される")
    void createFave_shouldInitializeCorrectly() {
        // Arrange
        UserId userId = new UserId(UUID.randomUUID());
        ThemeId themeId = new ThemeId(UUID.randomUUID());
        FaveContent content = new FaveContent("最高の作品！");

        // Act
        Fave fave = Fave.create(userId, themeId, content);

        // Assert
        assertNotNull(fave.getId());
        assertEquals(userId, fave.getUserId());
        assertEquals(themeId, fave.getThemeId());
        assertEquals(content, fave.getContent());
        assertNotNull(fave.getCreatedAt());
        assertNotNull(fave.getUpdatedAt());
    }

    @Test
    @DisplayName("正常系：投稿内容を更新できる")
    void updateContent_shouldChangeContent() {
        // Arrange
        Fave fave = createDefaultFave();
        FaveContent newContent = new FaveContent("新しい作品です");

        // Act
        fave.updateContent(newContent);

        // Assert
        assertEquals(newContent, fave.getContent());
    }


    @Test
    @DisplayName("異常系：nullのuserIdでFaveを作成すると例外が発生する")
    void createFave_withNullUserId_shouldThrowException() {
        // Arrange
        ThemeId themeId = new ThemeId(UUID.randomUUID());
        FaveContent content = new FaveContent("作品です");

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> Fave.create(null, themeId, content));
    }

    @Test
    @DisplayName("異常系：nullのthemeIdでFaveを作成すると例外が発生する")
    void createFave_withNullThemeId_shouldThrowException() {
        // Arrange
        UserId userId = new UserId(UUID.randomUUID());
        FaveContent content = new FaveContent("作品です");

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> Fave.create(userId, null, content));
    }

    @Test
    @DisplayName("異常系：nullのcontentでFaveを作成すると例外が発生する")
    void createFave_withNullContent_shouldThrowException() {
        // Arrange
        UserId userId = new UserId(UUID.randomUUID());
        ThemeId themeId = new ThemeId(UUID.randomUUID());

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> Fave.create(userId, themeId, null));
    }


    @Test
    @DisplayName("異常系：nullのcontentで更新すると例外が発生する")
    void updateContent_withNull_shouldThrowException() {
        // Arrange
        Fave fave = createDefaultFave();

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> fave.updateContent(null));
    }

    private Fave createDefaultFave() {
        return Fave.create(
            new UserId(UUID.randomUUID()),
            new ThemeId(UUID.randomUUID()),
            new FaveContent("お気に入り作品！")
        );
    }
}
