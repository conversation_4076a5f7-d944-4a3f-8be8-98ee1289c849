package com.favick.domain.feature.fave.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("FaveContent のテスト")
class FaveContentTest {
    @Test
    @DisplayName("正常系：有効な投稿内容でFaveContentが生成される")
    void validContent_shouldCreateFaveContent() {
        // Arrange
        String content = "これは素敵な作品です";

        // Act
        FaveContent faveContent = new FaveContent(content);

        // Assert
        assertEquals(content, faveContent.value());
    }

    @Test
    @DisplayName("境界値：255文字ぴったりの文字列は通る")
    void contentAtMaxLength_shouldBeValid() {
        // Act
        String max = "あ".repeat(255);

        // Act & Assert
        assertDoesNotThrow(() -> new FaveContent(max));
    }

    @Test
    @DisplayName("異常系：nullを渡すと例外がスローされる")
    void nullContent_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new FaveContent(null));
    }

    @Test
    @DisplayName("異常系：空文字を渡すと例外がスローされる")
    void emptyContent_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new FaveContent(""));
    }

    @Test
    @DisplayName("異常系：255文字を超える文字列は例外がスローされる")
    void contentTooLong_shouldThrowException() {
        // Arrange
        String tooLong = "あ".repeat(256);

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new FaveContent(tooLong));
    }

    @Test
    @DisplayName("等価性：同じ文字列のFaveContentは等しい")
    void sameContent_shouldBeEqual() {
        // Arrange
        String content = "いいね！";
        FaveContent c1 = new FaveContent(content);
        FaveContent c2 = new FaveContent(content);

        // Assert
        assertEquals(c1, c2);
        assertEquals(c1.hashCode(), c2.hashCode());
    }

    @Test
    @DisplayName("等価性：異なる文字列のFaveContentは等しくない")
    void differentContent_shouldNotBeEqual() {
        // Arrange
        FaveContent c1 = new FaveContent("最高！");
        FaveContent c2 = new FaveContent("素晴らしい！");

        // Assert
        assertNotEquals(c1, c2);
    }
}
