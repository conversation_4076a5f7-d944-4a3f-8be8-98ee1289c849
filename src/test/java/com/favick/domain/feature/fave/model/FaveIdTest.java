package com.favick.domain.feature.fave.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("FaveId のテスト")
class FaveIdTest {
    @Test
    @DisplayName("FaveIdの生成が成功すること")
    void generate_shouldCreateValidFaveId() {
        // Act
        FaveId faveId = FaveId.generate();

        // Assert
        assertNotNull(faveId);
        assertNotNull(faveId.value());
        assertInstanceOf(UUID.class, faveId.value());
    }

    @Test
    @DisplayName("nullを指定してFaveIdを生成するとDomainExceptionがスローされること")
    void createWithNull_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new FaveId(null));
    }

    @Test
    @DisplayName("同じUUIDから生成されたFaveIdは等しいと判定されること")
    void faveIdsWithSameValue_shouldBeEqual() {
        // Arrange
        UUID uuid = UUID.randomUUID();

        // Act
        FaveId id1 = new FaveId(uuid);
        FaveId id2 = new FaveId(uuid);

        // Assert
        assertEquals(id1, id2);
        assertEquals(id1.hashCode(), id2.hashCode());
    }

    @Test
    @DisplayName("異なるUUIDのFaveIdは等しくないこと")
    void faveIdsWithDifferentValue_shouldNotBeEqual() {
        // Act
        FaveId id1 = FaveId.generate();
        FaveId id2 = FaveId.generate();

        // Assert
        assertNotEquals(id1, id2);
    }
}
