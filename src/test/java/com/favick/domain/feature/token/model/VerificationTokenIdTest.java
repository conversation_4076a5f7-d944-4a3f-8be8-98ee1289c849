package com.favick.domain.feature.token.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class VerificationTokenIdTest {

    @Test
    @DisplayName("正常系：有効なUUIDでVerificationTokenIdを作成できる")
    void constructor_withValidUuid_shouldCreateVerificationTokenId() {
        // Arrange
        UUID validUuid = UUID.randomUUID();

        // Act
        VerificationTokenId tokenId = new VerificationTokenId(validUuid);

        // Assert
        assertNotNull(tokenId);
        assertEquals(validUuid, tokenId.value());
    }

    @Test
    @DisplayName("正常系：generateメソッドで有効なVerificationTokenIdを生成できる")
    void generate_shouldCreateValidVerificationTokenId() {
        // Act
        VerificationTokenId tokenId = VerificationTokenId.generate();

        // Assert
        assertNotNull(tokenId);
        assertNotNull(tokenId.value());
    }

    @Test
    @DisplayName("正常系：generateメソッドは毎回異なるIDを生成する")
    void generate_shouldCreateUniqueIds() {
        // Act
        VerificationTokenId id1 = VerificationTokenId.generate();
        VerificationTokenId id2 = VerificationTokenId.generate();
        VerificationTokenId id3 = VerificationTokenId.generate();

        // Assert
        assertNotEquals(id1.value(), id2.value());
        assertNotEquals(id2.value(), id3.value());
        assertNotEquals(id1.value(), id3.value());
    }

    @Test
    @DisplayName("異常系：nullのUUIDでVerificationTokenIdを作成すると例外が発生する")
    void constructor_withNull_shouldThrowException() {
        // Act & Assert
        ValueObjectException exception = assertThrows(
            ValueObjectException.class,
            () -> new VerificationTokenId(null)
        );

        assertEquals("id", exception.getField());
        assertEquals("Domain Error", exception.getMessage());
    }

    @Test
    @DisplayName("正常系：equalsとhashCodeが正しく動作する")
    void equalsAndHashCode_shouldWorkCorrectly() {
        // Arrange
        UUID uuid1 = UUID.randomUUID();
        UUID uuid2 = UUID.randomUUID();

        VerificationTokenId id1 = new VerificationTokenId(uuid1);
        VerificationTokenId id2 = new VerificationTokenId(uuid1);
        VerificationTokenId id3 = new VerificationTokenId(uuid2);

        // Act & Assert
        assertEquals(id1, id2); // 同じUUIDなので等しい
        assertNotEquals(id1, id3); // 異なるUUIDなので等しくない
        assertEquals(id1.hashCode(), id2.hashCode()); // 同じUUIDなのでhashCodeも等しい
        assertNotEquals(id1.hashCode(), id3.hashCode()); // 異なるUUIDなのでhashCodeも異なる
    }

    @Test
    @DisplayName("正常系：同じUUIDから作成されたVerificationTokenIdは等しい")
    void constructor_withSameUuid_shouldCreateEqualIds() {
        // Arrange
        UUID uuid = UUID.fromString("12345678-1234-1234-1234-123456789012");

        // Act
        VerificationTokenId id1 = new VerificationTokenId(uuid);
        VerificationTokenId id2 = new VerificationTokenId(uuid);

        // Assert
        assertEquals(id1, id2);
        assertEquals(id1.value(), id2.value());
        assertEquals(id1.hashCode(), id2.hashCode());
        assertEquals(id1.toString(), id2.toString());
    }

    @Test
    @DisplayName("正常系：generateで生成されたIDは有効なUUID形式である")
    void generate_shouldCreateValidUuidFormat() {
        // Act
        VerificationTokenId tokenId = VerificationTokenId.generate();

        // Assert
        UUID uuid = tokenId.value();
        assertNotNull(uuid);

        // UUID文字列形式の検証
        String uuidString = uuid.toString();
        assertTrue(uuidString.matches("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"));
    }

    @Test
    @DisplayName("正常系：VerificationTokenIdは他のTokenIdクラスと区別される")
    void verificationTokenId_shouldBeDifferentFromOtherTokenIds() {
        // Arrange
        UUID uuid = UUID.randomUUID();
        VerificationTokenId verificationTokenId = new VerificationTokenId(uuid);
        PasswordResetTokenId passwordResetTokenId = new PasswordResetTokenId(uuid);

        // Act & Assert
        // 同じUUIDでも異なるクラスなので等しくない
        assertNotEquals(verificationTokenId, passwordResetTokenId);

        // クラス名が異なることを確認
        assertNotEquals(
            verificationTokenId.getClass().getSimpleName(),
            passwordResetTokenId.getClass().getSimpleName()
        );
    }

    @Test
    @DisplayName("正常系：大量のIDを生成しても重複しない")
    void generate_shouldCreateUniqueIdsInBulk() {
        // Arrange
        int count = 1000;
        java.util.Set<UUID> generatedIds = new java.util.HashSet<>();

        // Act
        for (int i = 0; i < count; i++) {
            VerificationTokenId tokenId = VerificationTokenId.generate();
            generatedIds.add(tokenId.value());
        }

        // Assert
        assertEquals(count, generatedIds.size()); // 重複がないことを確認
    }

    @Test
    @DisplayName("正常系：nilUUIDでもVerificationTokenIdを作成できる")
    void constructor_withNilUuid_shouldCreateVerificationTokenId() {
        // Arrange
        UUID nilUuid = new UUID(0L, 0L); // 00000000-0000-0000-0000-000000000000

        // Act
        VerificationTokenId tokenId = new VerificationTokenId(nilUuid);

        // Assert
        assertNotNull(tokenId);
        assertEquals(nilUuid, tokenId.value());
        assertEquals("00000000-0000-0000-0000-000000000000", tokenId.value().toString());
    }

    @Test
    @DisplayName("正常系：VerificationTokenIdとPasswordResetTokenIdは独立して動作する")
    void verificationTokenId_shouldWorkIndependentlyFromPasswordResetTokenId() {
        // Act
        VerificationTokenId verificationId1 = VerificationTokenId.generate();
        PasswordResetTokenId passwordResetId1 = PasswordResetTokenId.generate();
        VerificationTokenId verificationId2 = VerificationTokenId.generate();
        PasswordResetTokenId passwordResetId2 = PasswordResetTokenId.generate();

        // Assert
        // 各タイプ内で一意性が保たれている
        assertNotEquals(verificationId1, verificationId2);
        assertNotEquals(passwordResetId1, passwordResetId2);

        // 異なるタイプ間では比較されない（型安全性）
        assertNotEquals(verificationId1.getClass(), passwordResetId1.getClass());
    }
}
