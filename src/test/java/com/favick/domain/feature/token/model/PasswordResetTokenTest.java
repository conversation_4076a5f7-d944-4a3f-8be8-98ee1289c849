package com.favick.domain.feature.token.model;

import com.favick.domain.exception.ValueObjectException;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class PasswordResetTokenTest {

    @Test
    @DisplayName("正常系：新しいパスワードリセットトークンを作成できる")
    void create_shouldCreateValidPasswordResetToken() {
        // Arrange
        UserId userId = new UserId(UUID.randomUUID());

        // Act
        PasswordResetToken token = PasswordResetToken.create(userId);

        // Assert
        assertNotNull(token);
        assertNotNull(token.getId());
        assertNotNull(token.getId().value());
        assertEquals(userId, token.getUserId());
        assertNotNull(token.getToken());
        assertNotNull(token.getToken().value());
        assertNotNull(token.getExpireAt());
        assertNotNull(token.getCreatedAt());

        // 有効期限が2時間後に設定されていることを確認
        LocalDateTime expectedExpire = LocalDateTime.now().plusHours(2);
        assertTrue(token.getExpireAt().value().isAfter(LocalDateTime.now()));
        assertTrue(token.getExpireAt().value().isBefore(expectedExpire.plusMinutes(1))); // 1分の誤差を許容

        // 作成直後は期限切れでないことを確認
        assertFalse(token.isExpired());
    }

    @Test
    @DisplayName("正常系：DBからパスワードリセットトークンを再構築できる")
    void reconstruct_shouldCreatePasswordResetTokenFromDbData() {
        // Arrange
        PasswordResetTokenId id = PasswordResetTokenId.generate();
        UserId userId = new UserId(UUID.randomUUID());
        TokenValue tokenValue = TokenValue.generate();
        TokenExpireAt expireAt = TokenExpireAt.fromHours(2);
        LocalDateTime createdAt = LocalDateTime.now().minusHours(1);

        // Act
        PasswordResetToken token = PasswordResetToken.reconstruct(
            id, userId, tokenValue, expireAt, createdAt
        );

        // Assert
        assertNotNull(token);
        assertEquals(id, token.getId());
        assertEquals(userId, token.getUserId());
        assertEquals(tokenValue, token.getToken());
        assertEquals(expireAt, token.getExpireAt());
        assertEquals(createdAt, token.getCreatedAt());
    }

    @Test
    @DisplayName("正常系：有効期限切れの判定が正しく動作する")
    void isExpired_shouldReturnCorrectExpiredStatus() {
        // Arrange - 過去の有効期限を持つトークン
        PasswordResetTokenId id = PasswordResetTokenId.generate();
        UserId userId = new UserId(UUID.randomUUID());
        TokenValue tokenValue = TokenValue.generate();
        TokenExpireAt expiredAt = new TokenExpireAt(LocalDateTime.now().minusHours(1)); // 1時間前に期限切れ
        LocalDateTime createdAt = LocalDateTime.now().minusHours(3);

        PasswordResetToken expiredToken = PasswordResetToken.reconstruct(
            id, userId, tokenValue, expiredAt, createdAt
        );

        // Act & Assert
        assertTrue(expiredToken.isExpired());

        // Arrange - 未来の有効期限を持つトークン
        TokenExpireAt futureExpireAt = new TokenExpireAt(LocalDateTime.now().plusHours(1)); // 1時間後に期限切れ
        PasswordResetToken validToken = PasswordResetToken.reconstruct(
            id, userId, tokenValue, futureExpireAt, createdAt
        );

        // Act & Assert
        assertFalse(validToken.isExpired());
    }

    @Test
    @DisplayName("異常系：nullのUserIdでトークンを作成すると例外が発生する")
    void create_withNullUserId_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> {
            PasswordResetToken.create(null);
        });
    }

    @Test
    @DisplayName("異常系：nullのIDで再構築すると例外が発生する")
    void reconstruct_withNullId_shouldThrowException() {
        // Arrange
        UserId userId = new UserId(UUID.randomUUID());
        TokenValue tokenValue = TokenValue.generate();
        TokenExpireAt expireAt = TokenExpireAt.fromHours(2);
        LocalDateTime createdAt = LocalDateTime.now();

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> {
            PasswordResetToken.reconstruct(null, userId, tokenValue, expireAt, createdAt);
        });
    }

    @Test
    @DisplayName("異常系：nullのUserIdで再構築すると例外が発生する")
    void reconstruct_withNullUserId_shouldThrowException() {
        // Arrange
        PasswordResetTokenId id = PasswordResetTokenId.generate();
        TokenValue tokenValue = TokenValue.generate();
        TokenExpireAt expireAt = TokenExpireAt.fromHours(2);
        LocalDateTime createdAt = LocalDateTime.now();

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> {
            PasswordResetToken.reconstruct(id, null, tokenValue, expireAt, createdAt);
        });
    }

    @Test
    @DisplayName("異常系：nullのTokenValueで再構築すると例外が発生する")
    void reconstruct_withNullTokenValue_shouldThrowException() {
        // Arrange
        PasswordResetTokenId id = PasswordResetTokenId.generate();
        UserId userId = new UserId(UUID.randomUUID());
        TokenExpireAt expireAt = TokenExpireAt.fromHours(2);
        LocalDateTime createdAt = LocalDateTime.now();

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> {
            PasswordResetToken.reconstruct(id, userId, null, expireAt, createdAt);
        });
    }

    @Test
    @DisplayName("異常系：nullのExpireAtで再構築すると例外が発生する")
    void reconstruct_withNullExpireAt_shouldThrowException() {
        // Arrange
        PasswordResetTokenId id = PasswordResetTokenId.generate();
        UserId userId = new UserId(UUID.randomUUID());
        TokenValue tokenValue = TokenValue.generate();
        LocalDateTime createdAt = LocalDateTime.now();

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> {
            PasswordResetToken.reconstruct(id, userId, tokenValue, null, createdAt);
        });
    }

    @Test
    @DisplayName("異常系：nullのCreatedAtで再構築すると例外が発生する")
    void reconstruct_withNullCreatedAt_shouldThrowException() {
        // Arrange
        PasswordResetTokenId id = PasswordResetTokenId.generate();
        UserId userId = new UserId(UUID.randomUUID());
        TokenValue tokenValue = TokenValue.generate();
        TokenExpireAt expireAt = TokenExpireAt.fromHours(2);

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> {
            PasswordResetToken.reconstruct(id, userId, tokenValue, expireAt, null);
        });
    }

    @Test
    @DisplayName("正常系：equalsとhashCodeが正しく動作する")
    void equalsAndHashCode_shouldWorkCorrectly() {
        // Arrange
        PasswordResetTokenId id1 = PasswordResetTokenId.generate();
        PasswordResetTokenId id2 = PasswordResetTokenId.generate();
        UserId userId = new UserId(UUID.randomUUID());
        TokenValue tokenValue = TokenValue.generate();
        TokenExpireAt expireAt = TokenExpireAt.fromHours(2);
        LocalDateTime createdAt = LocalDateTime.now();

        PasswordResetToken token1 = PasswordResetToken.reconstruct(id1, userId, tokenValue, expireAt, createdAt);
        PasswordResetToken token2 = PasswordResetToken.reconstruct(id1, userId, tokenValue, expireAt, createdAt);
        PasswordResetToken token3 = PasswordResetToken.reconstruct(id2, userId, tokenValue, expireAt, createdAt);

        // Act & Assert
        assertEquals(token1, token2); // 同じIDなので等しい
        assertNotEquals(token1, token3); // 異なるIDなので等しくない
        assertEquals(token1.hashCode(), token2.hashCode()); // 同じIDなのでhashCodeも等しい
    }

    @Test
    @DisplayName("正常系：toStringが適切な文字列を返す")
    void toString_shouldReturnAppropriateString() {
        // Arrange
        UserId userId = new UserId(UUID.randomUUID());
        PasswordResetToken token = PasswordResetToken.create(userId);

        // Act
        String result = token.toString();

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("PasswordResetToken"));
        assertTrue(result.contains("id="));
        assertTrue(result.contains("userId="));
        assertTrue(result.contains("token="));
    }

    @Test
    @DisplayName("正常系：作成されるトークンは毎回異なる値を持つ")
    void create_shouldGenerateUniqueTokens() {
        // Arrange
        UserId userId = new UserId(UUID.randomUUID());

        // Act
        PasswordResetToken token1 = PasswordResetToken.create(userId);
        PasswordResetToken token2 = PasswordResetToken.create(userId);

        // Assert
        assertNotEquals(token1.getId(), token2.getId());
        assertNotEquals(token1.getToken(), token2.getToken());
        assertEquals(token1.getUserId(), token2.getUserId()); // 同じユーザーIDは同じ
    }
}
