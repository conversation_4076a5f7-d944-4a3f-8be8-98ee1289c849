package com.favick.domain.feature.token.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class PasswordResetTokenIdTest {

    @Test
    @DisplayName("正常系：有効なUUIDでPasswordResetTokenIdを作成できる")
    void constructor_withValidUuid_shouldCreatePasswordResetTokenId() {
        // Arrange
        UUID validUuid = UUID.randomUUID();

        // Act
        PasswordResetTokenId tokenId = new PasswordResetTokenId(validUuid);

        // Assert
        assertNotNull(tokenId);
        assertEquals(validUuid, tokenId.value());
    }

    @Test
    @DisplayName("正常系：generateメソッドで有効なPasswordResetTokenIdを生成できる")
    void generate_shouldCreateValidPasswordResetTokenId() {
        // Act
        PasswordResetTokenId tokenId = PasswordResetTokenId.generate();

        // Assert
        assertNotNull(tokenId);
        assertNotNull(tokenId.value());
    }

    @Test
    @DisplayName("正常系：generateメソッドは毎回異なるIDを生成する")
    void generate_shouldCreateUniqueIds() {
        // Act
        PasswordResetTokenId id1 = PasswordResetTokenId.generate();
        PasswordResetTokenId id2 = PasswordResetTokenId.generate();
        PasswordResetTokenId id3 = PasswordResetTokenId.generate();

        // Assert
        assertNotEquals(id1.value(), id2.value());
        assertNotEquals(id2.value(), id3.value());
        assertNotEquals(id1.value(), id3.value());
    }

    @Test
    @DisplayName("異常系：nullのUUIDでPasswordResetTokenIdを作成すると例外が発生する")
    void constructor_withNull_shouldThrowException() {
        // Act & Assert
        ValueObjectException exception = assertThrows(
            ValueObjectException.class,
            () -> new PasswordResetTokenId(null)
        );

        assertEquals("id", exception.getField());
        assertEquals("Domain Error", exception.getMessage());
    }

    @Test
    @DisplayName("正常系：equalsとhashCodeが正しく動作する")
    void equalsAndHashCode_shouldWorkCorrectly() {
        // Arrange
        UUID uuid1 = UUID.randomUUID();
        UUID uuid2 = UUID.randomUUID();

        PasswordResetTokenId id1 = new PasswordResetTokenId(uuid1);
        PasswordResetTokenId id2 = new PasswordResetTokenId(uuid1);
        PasswordResetTokenId id3 = new PasswordResetTokenId(uuid2);

        // Act & Assert
        assertEquals(id1, id2); // 同じUUIDなので等しい
        assertNotEquals(id1, id3); // 異なるUUIDなので等しくない
        assertEquals(id1.hashCode(), id2.hashCode()); // 同じUUIDなのでhashCodeも等しい
        assertNotEquals(id1.hashCode(), id3.hashCode()); // 異なるUUIDなのでhashCodeも異なる
    }

    @Test
    @DisplayName("正常系：同じUUIDから作成されたPasswordResetTokenIdは等しい")
    void constructor_withSameUuid_shouldCreateEqualIds() {
        // Arrange
        UUID uuid = UUID.fromString("12345678-1234-1234-1234-123456789012");

        // Act
        PasswordResetTokenId id1 = new PasswordResetTokenId(uuid);
        PasswordResetTokenId id2 = new PasswordResetTokenId(uuid);

        // Assert
        assertEquals(id1, id2);
        assertEquals(id1.value(), id2.value());
        assertEquals(id1.hashCode(), id2.hashCode());
        assertEquals(id1.toString(), id2.toString());
    }

    @Test
    @DisplayName("正常系：generateで生成されたIDは有効なUUID形式である")
    void generate_shouldCreateValidUuidFormat() {
        // Act
        PasswordResetTokenId tokenId = PasswordResetTokenId.generate();

        // Assert
        UUID uuid = tokenId.value();
        assertNotNull(uuid);

        // UUID文字列形式の検証
        String uuidString = uuid.toString();
        assertTrue(uuidString.matches("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"));
    }

    @Test
    @DisplayName("正常系：PasswordResetTokenIdは他のTokenIdクラスと区別される")
    void passwordResetTokenId_shouldBeDifferentFromOtherTokenIds() {
        // Arrange
        UUID uuid = UUID.randomUUID();
        PasswordResetTokenId passwordResetTokenId = new PasswordResetTokenId(uuid);
        VerificationTokenId verificationTokenId = new VerificationTokenId(uuid);

        // Act & Assert
        // 同じUUIDでも異なるクラスなので等しくない
        assertNotEquals(passwordResetTokenId, verificationTokenId);

        // クラス名が異なることを確認
        assertNotEquals(
            passwordResetTokenId.getClass().getSimpleName(),
            verificationTokenId.getClass().getSimpleName()
        );
    }

    @Test
    @DisplayName("正常系：大量のIDを生成しても重複しない")
    void generate_shouldCreateUniqueIdsInBulk() {
        // Arrange
        int count = 1000;
        java.util.Set<UUID> generatedIds = new java.util.HashSet<>();

        // Act
        for (int i = 0; i < count; i++) {
            PasswordResetTokenId tokenId = PasswordResetTokenId.generate();
            generatedIds.add(tokenId.value());
        }

        // Assert
        assertEquals(count, generatedIds.size()); // 重複がないことを確認
    }

    @Test
    @DisplayName("正常系：nilUUIDでもPasswordResetTokenIdを作成できる")
    void constructor_withNilUuid_shouldCreatePasswordResetTokenId() {
        // Arrange
        UUID nilUuid = new UUID(0L, 0L); // 00000000-0000-0000-0000-000000000000

        // Act
        PasswordResetTokenId tokenId = new PasswordResetTokenId(nilUuid);

        // Assert
        assertNotNull(tokenId);
        assertEquals(nilUuid, tokenId.value());
        assertEquals("00000000-0000-0000-0000-000000000000", tokenId.value().toString());
    }
}
