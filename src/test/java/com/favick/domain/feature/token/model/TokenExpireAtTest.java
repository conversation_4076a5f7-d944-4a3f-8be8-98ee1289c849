package com.favick.domain.feature.token.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

class TokenExpireAtTest {

    @Test
    @DisplayName("正常系：有効なLocalDateTimeでTokenExpireAtを作成できる")
    void constructor_withValidDateTime_shouldCreateTokenExpireAt() {
        // Arrange
        LocalDateTime expireTime = LocalDateTime.now().plusHours(2);

        // Act
        TokenExpireAt expireAt = new TokenExpireAt(expireTime);

        // Assert
        assertNotNull(expireAt);
        assertEquals(expireTime, expireAt.value());
    }

    @Test
    @DisplayName("正常系：fromHoursメソッドで指定時間後のTokenExpireAtを作成できる")
    void fromHours_shouldCreateTokenExpireAtWithSpecifiedHours() {
        // Arrange
        int hours = 24;
        LocalDateTime beforeCreation = LocalDateTime.now();

        // Act
        TokenExpireAt expireAt = TokenExpireAt.fromHours(hours);

        // Assert
        assertNotNull(expireAt);
        LocalDateTime afterCreation = LocalDateTime.now();

        // 指定した時間後の範囲内であることを確認（1分の誤差を許容）
        LocalDateTime expectedMin = beforeCreation.plusHours(hours);
        LocalDateTime expectedMax = afterCreation.plusHours(hours);

        assertTrue(expireAt.value().isAfter(expectedMin.minusMinutes(1)));
        assertTrue(expireAt.value().isBefore(expectedMax.plusMinutes(1)));
    }

    @Test
    @DisplayName("正常系：fromHoursで異なる時間を指定して作成できる")
    void fromHours_withDifferentHours_shouldCreateCorrectExpireAt() {
        // Arrange & Act
        TokenExpireAt expireAt2Hours = TokenExpireAt.fromHours(2);
        TokenExpireAt expireAt24Hours = TokenExpireAt.fromHours(24);
        TokenExpireAt expireAt1Hour = TokenExpireAt.fromHours(1);

        // Assert
        assertTrue(expireAt1Hour.value().isBefore(expireAt2Hours.value()));
        assertTrue(expireAt2Hours.value().isBefore(expireAt24Hours.value()));

        // 時間差の確認（誤差を考慮）
        long diff2to24 = java.time.Duration.between(
            expireAt2Hours.value(),
            expireAt24Hours.value()
        ).toHours();
        assertTrue(diff2to24 >= 21 && diff2to24 <= 23); // 約22時間差

        long diff1to2 = java.time.Duration.between(
            expireAt1Hour.value(),
            expireAt2Hours.value()
        ).toHours();
        assertTrue(diff1to2 >= 0 && diff1to2 <= 2); // 約1時間差
    }

    @Test
    @DisplayName("正常系：過去の時刻でもTokenExpireAtを作成できる")
    void constructor_withPastDateTime_shouldCreateTokenExpireAt() {
        // Arrange
        LocalDateTime pastTime = LocalDateTime.now().minusHours(1);

        // Act
        TokenExpireAt expireAt = new TokenExpireAt(pastTime);

        // Assert
        assertNotNull(expireAt);
        assertEquals(pastTime, expireAt.value());
    }

    @Test
    @DisplayName("正常系：未来の時刻でTokenExpireAtを作成できる")
    void constructor_withFutureDateTime_shouldCreateTokenExpireAt() {
        // Arrange
        LocalDateTime futureTime = LocalDateTime.now().plusDays(1);

        // Act
        TokenExpireAt expireAt = new TokenExpireAt(futureTime);

        // Assert
        assertNotNull(expireAt);
        assertEquals(futureTime, expireAt.value());
    }

    @Test
    @DisplayName("異常系：nullのLocalDateTimeでTokenExpireAtを作成すると例外が発生する")
    void constructor_withNull_shouldThrowException() {
        // Act & Assert
        ValueObjectException exception = assertThrows(
            ValueObjectException.class,
            () -> new TokenExpireAt(null)
        );

        assertEquals("expireAt", exception.getField());
        assertEquals("Domain Error", exception.getMessage());
    }

    @Test
    @DisplayName("異常系：0時間でfromHoursを呼ぶと例外が発生する")
    void fromHours_withZeroHours_shouldThrowException() {
        // Act & Assert
        ValueObjectException exception = assertThrows(
            ValueObjectException.class,
            () -> TokenExpireAt.fromHours(0)
        );

        assertEquals("hours", exception.getField());
        assertEquals("Domain Error", exception.getMessage());
    }

    @Test
    @DisplayName("異常系：負の時間でfromHoursを呼ぶと例外が発生する")
    void fromHours_withNegativeHours_shouldThrowException() {
        // Act & Assert
        ValueObjectException exception = assertThrows(
            ValueObjectException.class,
            () -> TokenExpireAt.fromHours(-1)
        );

        assertEquals("hours", exception.getField());
        assertEquals("Domain Error", exception.getMessage());
    }

    @Test
    @DisplayName("正常系：equalsとhashCodeが正しく動作する")
    void equalsAndHashCode_shouldWorkCorrectly() {
        // Arrange
        LocalDateTime time1 = LocalDateTime.of(2025, 1, 1, 12, 0, 0);
        LocalDateTime time2 = LocalDateTime.of(2025, 1, 1, 12, 0, 0);
        LocalDateTime time3 = LocalDateTime.of(2025, 1, 1, 13, 0, 0);

        TokenExpireAt expireAt1 = new TokenExpireAt(time1);
        TokenExpireAt expireAt2 = new TokenExpireAt(time2);
        TokenExpireAt expireAt3 = new TokenExpireAt(time3);

        // Act & Assert
        assertEquals(expireAt1, expireAt2); // 同じ時刻なので等しい
        assertNotEquals(expireAt1, expireAt3); // 異なる時刻なので等しくない
        assertEquals(expireAt1.hashCode(), expireAt2.hashCode()); // 同じ時刻なのでhashCodeも等しい
        assertNotEquals(expireAt1.hashCode(), expireAt3.hashCode()); // 異なる時刻なのでhashCodeも異なる
    }

    @Test
    @DisplayName("正常系：大きな時間数でfromHoursを呼んでも正しく動作する")
    void fromHours_withLargeHours_shouldCreateCorrectExpireAt() {
        // Arrange
        int largeHours = 8760; // 1年分の時間

        // Act
        TokenExpireAt expireAt = TokenExpireAt.fromHours(largeHours);

        // Assert
        assertNotNull(expireAt);
        LocalDateTime expectedTime = LocalDateTime.now().plusHours(largeHours);

        // 1分の誤差を許容
        assertTrue(expireAt.value().isAfter(expectedTime.minusMinutes(1)));
        assertTrue(expireAt.value().isBefore(expectedTime.plusMinutes(1)));
    }

    @Test
    @DisplayName("正常系：1時間でfromHoursを呼んで正しく動作する")
    void fromHours_withOneHour_shouldCreateCorrectExpireAt() {
        // Arrange
        LocalDateTime beforeCreation = LocalDateTime.now();

        // Act
        TokenExpireAt expireAt = TokenExpireAt.fromHours(1);

        // Assert
        LocalDateTime afterCreation = LocalDateTime.now();

        // 1時間後の範囲内であることを確認
        LocalDateTime expectedMin = beforeCreation.plusHours(1);
        LocalDateTime expectedMax = afterCreation.plusHours(1);

        assertTrue(expireAt.value().isAfter(expectedMin.minusMinutes(1)));
        assertTrue(expireAt.value().isBefore(expectedMax.plusMinutes(1)));
    }

    @Test
    @DisplayName("正常系：fromHoursで作成されたTokenExpireAtは毎回わずかに異なる時刻を持つ")
    void fromHours_shouldCreateSlightlyDifferentTimes() throws InterruptedException {
        // Act
        TokenExpireAt expireAt1 = TokenExpireAt.fromHours(2);
        Thread.sleep(10); // 10ミリ秒待機
        TokenExpireAt expireAt2 = TokenExpireAt.fromHours(2);

        // Assert
        // 同じ時間数でも作成時刻が異なるため、わずかに異なる有効期限を持つ
        assertNotEquals(expireAt1.value(), expireAt2.value());

        // ただし、差は非常に小さい（1秒以内）
        long diffMillis = Math.abs(java.time.Duration.between(
            expireAt1.value(),
            expireAt2.value()
        ).toMillis());
        assertTrue(diffMillis < 1000); // 1秒未満の差
    }

    @Test
    @DisplayName("正常系：isExpiredメソッドで期限切れを正しく判定できる")
    void isExpired_shouldCorrectlyDetermineExpiration() {
        // Arrange - 過去の時刻（期限切れ）
        LocalDateTime pastTime = LocalDateTime.now().minusHours(1);
        TokenExpireAt expiredToken = new TokenExpireAt(pastTime);

        // Act & Assert
        assertTrue(expiredToken.isExpired());

        // Arrange - 未来の時刻（有効）
        LocalDateTime futureTime = LocalDateTime.now().plusHours(1);
        TokenExpireAt validToken = new TokenExpireAt(futureTime);

        // Act & Assert
        assertFalse(validToken.isExpired());
    }

    @Test
    @DisplayName("正常系：現在時刻に非常に近い時刻でのisExpired判定")
    void isExpired_withTimeCloseToNow_shouldWorkCorrectly() {
        // Arrange - 1秒前（期限切れ）
        LocalDateTime oneSecondAgo = LocalDateTime.now().minusSeconds(1);
        TokenExpireAt expiredToken = new TokenExpireAt(oneSecondAgo);

        // Act & Assert
        assertTrue(expiredToken.isExpired());

        // Arrange - 1秒後（有効）
        LocalDateTime oneSecondLater = LocalDateTime.now().plusSeconds(1);
        TokenExpireAt validToken = new TokenExpireAt(oneSecondLater);

        // Act & Assert
        assertFalse(validToken.isExpired());
    }

    @Test
    @DisplayName("正常系：fromHoursで作成されたトークンの期限切れ判定")
    void isExpired_withFromHoursCreatedToken_shouldNotBeExpired() {
        // Act
        TokenExpireAt expireAt = TokenExpireAt.fromHours(1);

        // Assert
        assertFalse(expireAt.isExpired()); // 1時間後なので期限切れでない
    }
}
