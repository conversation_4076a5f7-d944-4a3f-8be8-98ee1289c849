package com.favick.domain.feature.token.model;

import com.favick.domain.exception.ValueObjectException;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class VerificationTokenTest {

    @Test
    @DisplayName("正常系：新しい認証トークンを作成できる")
    void create_shouldCreateValidVerificationToken() {
        // Arrange
        UserId userId = new UserId(UUID.randomUUID());

        // Act
        VerificationToken token = VerificationToken.create(userId);

        // Assert
        assertNotNull(token);
        assertNotNull(token.getId());
        assertNotNull(token.getId().value());
        assertEquals(userId, token.getUserId());
        assertNotNull(token.getToken());
        assertNotNull(token.getToken().value());
        assertNotNull(token.getExpireAt());
        assertNotNull(token.getCreatedAt());

        // 有効期限が24時間後に設定されていることを確認
        LocalDateTime expectedExpire = LocalDateTime.now().plusHours(24);
        assertTrue(token.getExpireAt().value().isAfter(LocalDateTime.now()));
        assertTrue(token.getExpireAt().value().isBefore(expectedExpire.plusMinutes(1))); // 1分の誤差を許容

        // 作成直後は期限切れでないことを確認
        assertFalse(token.isExpired());
    }

    @Test
    @DisplayName("正常系：DBから認証トークンを再構築できる")
    void reconstruct_shouldCreateVerificationTokenFromDbData() {
        // Arrange
        VerificationTokenId id = VerificationTokenId.generate();
        UserId userId = new UserId(UUID.randomUUID());
        TokenValue tokenValue = TokenValue.generate();
        TokenExpireAt expireAt = TokenExpireAt.fromHours(24);
        LocalDateTime createdAt = LocalDateTime.now().minusHours(1);

        // Act
        VerificationToken token = VerificationToken.reconstruct(
            id, userId, tokenValue, expireAt, createdAt
        );

        // Assert
        assertNotNull(token);
        assertEquals(id, token.getId());
        assertEquals(userId, token.getUserId());
        assertEquals(tokenValue, token.getToken());
        assertEquals(expireAt, token.getExpireAt());
        assertEquals(createdAt, token.getCreatedAt());
    }

    @Test
    @DisplayName("正常系：有効期限切れの判定が正しく動作する")
    void isExpired_shouldReturnCorrectExpiredStatus() {
        // Arrange - 過去の有効期限を持つトークン
        VerificationTokenId id = VerificationTokenId.generate();
        UserId userId = new UserId(UUID.randomUUID());
        TokenValue tokenValue = TokenValue.generate();
        TokenExpireAt expiredAt = new TokenExpireAt(LocalDateTime.now().minusHours(1)); // 1時間前に期限切れ
        LocalDateTime createdAt = LocalDateTime.now().minusHours(25);

        VerificationToken expiredToken = VerificationToken.reconstruct(
            id, userId, tokenValue, expiredAt, createdAt
        );

        // Act & Assert
        assertTrue(expiredToken.isExpired());

        // Arrange - 未来の有効期限を持つトークン
        TokenExpireAt futureExpireAt = new TokenExpireAt(LocalDateTime.now().plusHours(1)); // 1時間後に期限切れ
        VerificationToken validToken = VerificationToken.reconstruct(
            id, userId, tokenValue, futureExpireAt, createdAt
        );

        // Act & Assert
        assertFalse(validToken.isExpired());
    }

    @Test
    @DisplayName("異常系：nullのUserIdでトークンを作成すると例外が発生する")
    void create_withNullUserId_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> {
            VerificationToken.create(null);
        });
    }

    @Test
    @DisplayName("異常系：nullのIDで再構築すると例外が発生する")
    void reconstruct_withNullId_shouldThrowException() {
        // Arrange
        UserId userId = new UserId(UUID.randomUUID());
        TokenValue tokenValue = TokenValue.generate();
        TokenExpireAt expireAt = TokenExpireAt.fromHours(24);
        LocalDateTime createdAt = LocalDateTime.now();

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> {
            VerificationToken.reconstruct(null, userId, tokenValue, expireAt, createdAt);
        });
    }

    @Test
    @DisplayName("異常系：nullのUserIdで再構築すると例外が発生する")
    void reconstruct_withNullUserId_shouldThrowException() {
        // Arrange
        VerificationTokenId id = VerificationTokenId.generate();
        TokenValue tokenValue = TokenValue.generate();
        TokenExpireAt expireAt = TokenExpireAt.fromHours(24);
        LocalDateTime createdAt = LocalDateTime.now();

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> {
            VerificationToken.reconstruct(id, null, tokenValue, expireAt, createdAt);
        });
    }

    @Test
    @DisplayName("異常系：nullのTokenValueで再構築すると例外が発生する")
    void reconstruct_withNullTokenValue_shouldThrowException() {
        // Arrange
        VerificationTokenId id = VerificationTokenId.generate();
        UserId userId = new UserId(UUID.randomUUID());
        TokenExpireAt expireAt = TokenExpireAt.fromHours(24);
        LocalDateTime createdAt = LocalDateTime.now();

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> {
            VerificationToken.reconstruct(id, userId, null, expireAt, createdAt);
        });
    }

    @Test
    @DisplayName("異常系：nullのExpireAtで再構築すると例外が発生する")
    void reconstruct_withNullExpireAt_shouldThrowException() {
        // Arrange
        VerificationTokenId id = VerificationTokenId.generate();
        UserId userId = new UserId(UUID.randomUUID());
        TokenValue tokenValue = TokenValue.generate();
        LocalDateTime createdAt = LocalDateTime.now();

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> {
            VerificationToken.reconstruct(id, userId, tokenValue, null, createdAt);
        });
    }

    @Test
    @DisplayName("異常系：nullのCreatedAtで再構築すると例外が発生する")
    void reconstruct_withNullCreatedAt_shouldThrowException() {
        // Arrange
        VerificationTokenId id = VerificationTokenId.generate();
        UserId userId = new UserId(UUID.randomUUID());
        TokenValue tokenValue = TokenValue.generate();
        TokenExpireAt expireAt = TokenExpireAt.fromHours(24);

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> {
            VerificationToken.reconstruct(id, userId, tokenValue, expireAt, null);
        });
    }

    @Test
    @DisplayName("正常系：equalsとhashCodeが正しく動作する")
    void equalsAndHashCode_shouldWorkCorrectly() {
        // Arrange
        VerificationTokenId id1 = VerificationTokenId.generate();
        VerificationTokenId id2 = VerificationTokenId.generate();
        UserId userId = new UserId(UUID.randomUUID());
        TokenValue tokenValue = TokenValue.generate();
        TokenExpireAt expireAt = TokenExpireAt.fromHours(24);
        LocalDateTime createdAt = LocalDateTime.now();

        VerificationToken token1 = VerificationToken.reconstruct(id1, userId, tokenValue, expireAt, createdAt);
        VerificationToken token2 = VerificationToken.reconstruct(id1, userId, tokenValue, expireAt, createdAt);
        VerificationToken token3 = VerificationToken.reconstruct(id2, userId, tokenValue, expireAt, createdAt);

        // Act & Assert
        assertEquals(token1, token2); // 同じIDなので等しい
        assertNotEquals(token1, token3); // 異なるIDなので等しくない
        assertEquals(token1.hashCode(), token2.hashCode()); // 同じIDなのでhashCodeも等しい
    }

    @Test
    @DisplayName("正常系：toStringが適切な文字列を返す")
    void toString_shouldReturnAppropriateString() {
        // Arrange
        UserId userId = new UserId(UUID.randomUUID());
        VerificationToken token = VerificationToken.create(userId);

        // Act
        String result = token.toString();

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("VerificationToken"));
        assertTrue(result.contains("id="));
        assertTrue(result.contains("userId="));
        assertTrue(result.contains("token="));
    }

    @Test
    @DisplayName("正常系：作成されるトークンは毎回異なる値を持つ")
    void create_shouldGenerateUniqueTokens() {
        // Arrange
        UserId userId = new UserId(UUID.randomUUID());

        // Act
        VerificationToken token1 = VerificationToken.create(userId);
        VerificationToken token2 = VerificationToken.create(userId);

        // Assert
        assertNotEquals(token1.getId(), token2.getId());
        assertNotEquals(token1.getToken(), token2.getToken());
        assertEquals(token1.getUserId(), token2.getUserId()); // 同じユーザーIDは同じ
    }

    @Test
    @DisplayName("正常系：PasswordResetTokenとは異なる有効期限を持つ")
    void create_shouldHaveDifferentExpirationThanPasswordResetToken() {
        // Arrange
        UserId userId = new UserId(UUID.randomUUID());

        // Act
        VerificationToken verificationToken = VerificationToken.create(userId);
        PasswordResetToken passwordResetToken = PasswordResetToken.create(userId);

        // Assert
        // VerificationTokenは24時間、PasswordResetTokenは2時間の有効期限
        assertTrue(verificationToken.getExpireAt().value().isAfter(passwordResetToken.getExpireAt().value()));

        // 約22時間の差があることを確認（誤差を考慮して21-23時間の範囲）
        long hoursDifference = java.time.Duration.between(
            passwordResetToken.getExpireAt().value(),
            verificationToken.getExpireAt().value()
        ).toHours();

        assertTrue(hoursDifference >= 21 && hoursDifference <= 23);
    }
}
