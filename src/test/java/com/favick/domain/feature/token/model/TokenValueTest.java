package com.favick.domain.feature.token.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class TokenValueTest {

    @Test
    @DisplayName("正常系：有効な文字列でTokenValueを作成できる")
    void constructor_withValidString_shouldCreateTokenValue() {
        // Arrange
        String validToken = "valid-token-string";

        // Act
        TokenValue tokenValue = new TokenValue(validToken);

        // Assert
        assertNotNull(tokenValue);
        assertEquals(validToken, tokenValue.value());
    }

    @Test
    @DisplayName("正常系：UUIDでTokenValueを作成できる")
    void constructor_withUuidString_shouldCreateTokenValue() {
        // Arrange
        String uuidToken = UUID.randomUUID().toString();

        // Act
        TokenValue tokenValue = new TokenValue(uuidToken);

        // Assert
        assertNotNull(tokenValue);
        assertEquals(uuidToken, tokenValue.value());
    }

    @Test
    @DisplayName("正常系：generateメソッドで有効なTokenValueを生成できる")
    void generate_shouldCreateValidTokenValue() {
        // Act
        TokenValue tokenValue = TokenValue.generate();

        // Assert
        assertNotNull(tokenValue);
        assertNotNull(tokenValue.value());
        assertFalse(tokenValue.value().isEmpty());

        // UUIDの形式であることを確認
        assertDoesNotThrow(() -> UUID.fromString(tokenValue.value()));
    }

    @Test
    @DisplayName("正常系：generateメソッドは毎回異なる値を生成する")
    void generate_shouldCreateUniqueValues() {
        // Act
        TokenValue token1 = TokenValue.generate();
        TokenValue token2 = TokenValue.generate();
        TokenValue token3 = TokenValue.generate();

        // Assert
        assertNotEquals(token1.value(), token2.value());
        assertNotEquals(token2.value(), token3.value());
        assertNotEquals(token1.value(), token3.value());
    }

    @Test
    @DisplayName("異常系：nullの文字列でTokenValueを作成すると例外が発生する")
    void constructor_withNull_shouldThrowException() {
        // Act & Assert
        ValueObjectException exception = assertThrows(
            ValueObjectException.class,
            () -> new TokenValue(null)
        );

        assertEquals("token", exception.getField());
        assertEquals("Domain Error", exception.getMessage());
    }

    @Test
    @DisplayName("異常系：空文字列でTokenValueを作成すると例外が発生する")
    void constructor_withEmptyString_shouldThrowException() {
        // Act & Assert
        ValueObjectException exception = assertThrows(
            ValueObjectException.class,
            () -> new TokenValue("")
        );

        assertEquals("token", exception.getField());
        assertEquals("Domain Error", exception.getMessage());
    }

    @Test
    @DisplayName("異常系：空白文字のみの文字列でTokenValueを作成すると例外が発生する")
    void constructor_withWhitespaceOnly_shouldThrowException() {
        // Act & Assert
        ValueObjectException exception = assertThrows(
            ValueObjectException.class,
            () -> new TokenValue("   ")
        );

        assertEquals("token", exception.getField());
        assertEquals("Domain Error", exception.getMessage());
    }

    @Test
    @DisplayName("正常系：前後に空白がある文字列でもTokenValueを作成できる")
    void constructor_withWhitespaceAroundValidString_shouldCreateTokenValue() {
        // Arrange
        String tokenWithWhitespace = "  valid-token  ";

        // Act
        TokenValue tokenValue = new TokenValue(tokenWithWhitespace);

        // Assert
        assertNotNull(tokenValue);
        assertEquals(tokenWithWhitespace, tokenValue.value()); // トリムされない
    }

    @Test
    @DisplayName("正常系：equalsとhashCodeが正しく動作する")
    void equalsAndHashCode_shouldWorkCorrectly() {
        // Arrange
        String tokenString = "test-token";
        TokenValue token1 = new TokenValue(tokenString);
        TokenValue token2 = new TokenValue(tokenString);
        TokenValue token3 = new TokenValue("different-token");

        // Act & Assert
        assertEquals(token1, token2); // 同じ値なので等しい
        assertNotEquals(token1, token3); // 異なる値なので等しくない
        assertEquals(token1.hashCode(), token2.hashCode()); // 同じ値なのでhashCodeも等しい
        assertNotEquals(token1.hashCode(), token3.hashCode()); // 異なる値なのでhashCodeも異なる
    }

    @Test
    @DisplayName("正常系：長い文字列でもTokenValueを作成できる")
    void constructor_withLongString_shouldCreateTokenValue() {
        // Arrange
        String longToken = "a".repeat(1000); // 1000文字の文字列

        // Act
        TokenValue tokenValue = new TokenValue(longToken);

        // Assert
        assertNotNull(tokenValue);
        assertEquals(longToken, tokenValue.value());
        assertEquals(1000, tokenValue.value().length());
    }

    @Test
    @DisplayName("正常系：特殊文字を含む文字列でもTokenValueを作成できる")
    void constructor_withSpecialCharacters_shouldCreateTokenValue() {
        // Arrange
        String specialToken = "token-with-special-chars!@#$%^&*()_+-=[]{}|;:,.<>?";

        // Act
        TokenValue tokenValue = new TokenValue(specialToken);

        // Assert
        assertNotNull(tokenValue);
        assertEquals(specialToken, tokenValue.value());
    }

    @Test
    @DisplayName("正常系：数字のみの文字列でもTokenValueを作成できる")
    void constructor_withNumericString_shouldCreateTokenValue() {
        // Arrange
        String numericToken = "1234567890";

        // Act
        TokenValue tokenValue = new TokenValue(numericToken);

        // Assert
        assertNotNull(tokenValue);
        assertEquals(numericToken, tokenValue.value());
    }

    @Test
    @DisplayName("正常系：generateで生成されたTokenValueは有効なUUID形式である")
    void generate_shouldCreateValidUuidFormat() {
        // Act
        TokenValue tokenValue = TokenValue.generate();

        // Assert
        String value = tokenValue.value();

        // UUID形式の検証
        assertTrue(value.matches("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"));

        // UUID.fromStringで例外が発生しないことを確認
        assertDoesNotThrow(() -> {
            UUID uuid = UUID.fromString(value);
            assertNotNull(uuid);
        });
    }
}
