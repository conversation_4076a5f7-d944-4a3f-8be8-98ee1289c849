package com.favick.domain.feature.rank.model;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("RankNameValue のテスト")
class RankNameValueTest {
    @Test
    @DisplayName("RANK_SEED（種）が正しく定義されていること")
    void rankSeed_shouldBeDefined() {
        // Act & Assert
        assertEquals("RANK_SEED", RankNameValue.RANK_SEED.name());
    }

    @Test
    @DisplayName("RANK_SPROUT（芽）が正しく定義されていること")
    void rankSprout_shouldBeDefined() {
        // Act & Assert
        assertEquals("RANK_SPROUT", RankNameValue.RANK_SPROUT.name());
    }

    @Test
    @DisplayName("RANK_BUD（つぼみ）が正しく定義されていること")
    void rankBud_shouldBeDefined() {
        // Act & Assert
        assertEquals("RANK_BUD", RankNameValue.RANK_BUD.name());
    }

    @Test
    @DisplayName("RANK_BLOOM（開花）が正しく定義されていること")
    void rankBloom_shouldBeDefined() {
        // Act & Assert
        assertEquals("RANK_BLOOM", RankNameValue.RANK_BLOOM.name());
    }

    @Test
    @DisplayName("RANK_GARDEN（庭師）が正しく定義されていること")
    void rankGarden_shouldBeDefined() {
        // Act & Assert
        assertEquals("RANK_GARDEN", RankNameValue.RANK_GARDEN.name());
    }

    @Test
    @DisplayName("valueOf()で正しい値を取得できること")
    void valueOf_shouldReturnCorrectValue() {
        // Act & Assert
        assertEquals(RankNameValue.RANK_SEED, RankNameValue.valueOf("RANK_SEED"));
        assertEquals(RankNameValue.RANK_SPROUT, RankNameValue.valueOf("RANK_SPROUT"));
        assertEquals(RankNameValue.RANK_BUD, RankNameValue.valueOf("RANK_BUD"));
        assertEquals(RankNameValue.RANK_BLOOM, RankNameValue.valueOf("RANK_BLOOM"));
        assertEquals(RankNameValue.RANK_GARDEN, RankNameValue.valueOf("RANK_GARDEN"));
    }

    @Test
    @DisplayName("無効な値でvalueOf()を呼ぶとIllegalArgumentExceptionがスローされること")
    void valueOf_withInvalidValue_shouldThrowException() {
        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> RankNameValue.valueOf("INVALID"));
    }

    @Test
    @DisplayName("全てのランク名が5つ定義されていること")
    void values_shouldContainFiveRanks() {
        // Act
        RankNameValue[] values = RankNameValue.values();

        // Assert
        assertEquals(5, values.length);
        assertTrue(java.util.Arrays.asList(values).contains(RankNameValue.RANK_SEED));
        assertTrue(java.util.Arrays.asList(values).contains(RankNameValue.RANK_SPROUT));
        assertTrue(java.util.Arrays.asList(values).contains(RankNameValue.RANK_BUD));
        assertTrue(java.util.Arrays.asList(values).contains(RankNameValue.RANK_BLOOM));
        assertTrue(java.util.Arrays.asList(values).contains(RankNameValue.RANK_GARDEN));
    }

    @Test
    @DisplayName("ランクの順序が正しく定義されていること")
    void rankOrder_shouldBeCorrect() {
        // Act
        RankNameValue[] values = RankNameValue.values();

        // Assert - 定義順序の確認
        assertEquals(RankNameValue.RANK_SEED, values[0]);
        assertEquals(RankNameValue.RANK_SPROUT, values[1]);
        assertEquals(RankNameValue.RANK_BUD, values[2]);
        assertEquals(RankNameValue.RANK_BLOOM, values[3]);
        assertEquals(RankNameValue.RANK_GARDEN, values[4]);
    }
}