package com.favick.domain.feature.rank.model;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("Rank のテスト")
class RankTest {
    @Test
    @DisplayName("正常系：コンストラクタで全ての値を設定できる")
    void constructor_shouldSetAllValues() {
        // Arrange
        RankId id = RankId.generate();
        RankName name = new RankName(RankNameValue.RANK_BLOOM);
        LocalDateTime createdAt = LocalDateTime.now().minusDays(1);
        LocalDateTime updatedAt = LocalDateTime.now();

        // Act
        Rank rank = Rank.reconstruct(
            id,
            name,
            createdAt,
            updatedAt
        );

        // Assert
        assertEquals(id, rank.getId());
        assertEquals(name, rank.getName());
        assertEquals(createdAt, rank.getCreatedAt());
        assertEquals(updatedAt, rank.getUpdatedAt());
    }

    @Test
    @DisplayName("正常系：名前のみ指定のコンストラクタでIDと時間が自動生成される")
    void constructorWithNameOnly_shouldGenerateIdAndTimes() {
        // Arrange
        RankName name = new RankName(RankNameValue.RANK_BLOOM);

        // Act
        Rank rank = Rank.create(name);

        // Assert
        assertNotNull(rank.getId());
        assertNotNull(rank.getId().value());
        assertEquals(name, rank.getName());
        assertNotNull(rank.getCreatedAt());
        assertNotNull(rank.getUpdatedAt());
        // 作成日時と更新日時は初期状態では同じ
        assertEquals(rank.getCreatedAt(), rank.getUpdatedAt());
    }

    @Test
    @DisplayName("正常系：updateメソッドで名前と更新日時が更新される")
    void update_shouldUpdateNameAndUpdateTime() {
        // Arrange
        Rank rank = Rank.create(new RankName(RankNameValue.RANK_SEED));
        LocalDateTime beforeUpdate = rank.getUpdatedAt();
        RankName newName = new RankName(RankNameValue.RANK_BLOOM);

        // 少し時間をおいて更新
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            fail("Thread sleep interrupted");
        }

        // Act
        rank.update(newName);

        // Assert
        assertEquals(newName, rank.getName());
        assertTrue(rank.getUpdatedAt().isAfter(beforeUpdate), "更新日時が更新されていること");
    }

    @Test
    @DisplayName("等価性テスト：同じIDで生成したRankは等しい")
    void sameId_shouldBeEqual() {
        // Arrange
        RankId id = RankId.generate();
        RankName name1 = new RankName(RankNameValue.RANK_BLOOM);
        RankName name2 = new RankName(RankNameValue.RANK_GARDEN);
        LocalDateTime now = LocalDateTime.now();

        // Act
        Rank rank1 = Rank.reconstruct(
            id,
            name1,
            now,
            now
        );
        Rank rank2 = Rank.reconstruct(
            id,
            name2,
            now.plusHours(1),
            now.plusHours(2)
        );

        // Assert
        assertEquals(rank1, rank2);
        assertEquals(rank1.hashCode(), rank2.hashCode());
    }

    @Test
    @DisplayName("非等価性テスト：異なるIDで生成したRankは等しくない")
    void differentId_shouldNotBeEqual() {
        // Arrange
        RankName name = new RankName(RankNameValue.RANK_BLOOM);
        LocalDateTime now = LocalDateTime.now();

        // Act
        Rank rank1 = Rank.reconstruct(
            RankId.generate(),
            name,
            now,
            now
        );
        Rank rank2 = Rank.reconstruct(
            RankId.generate(),
            name,
            now,
            now
        );

        // Assert
        assertNotEquals(rank1, rank2);
    }

    @Test
    @DisplayName("toString()テスト：IDと名前を含む文字列を返す")
    void toString_shouldContainIdAndName() {
        // Arrange
        RankId id = RankId.generate();
        RankName name = new RankName(RankNameValue.RANK_BLOOM);
        LocalDateTime now = LocalDateTime.now();
        Rank rank = Rank.reconstruct(
            id,
            name,
            now,
            now
        );

        // Act
        String result = rank.toString();

        // Assert
        assertTrue(result.contains("Rank{"));
        assertTrue(result.contains("id=" + id.value()));
        assertTrue(result.contains("name=" + name.value()));
    }
}
