package com.favick.domain.feature.rank.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("RankId のテスト")
class RankIdTest {
    @Test
    @DisplayName("正常系：generate()で有効なRankIdを生成できる")
    void generate_shouldCreateValidRankId() {
        // Act
        RankId rankId = RankId.generate();

        // Assert
        assertNotNull(rankId);
        assertNotNull(rankId.value());
        assertInstanceOf(UUID.class, rankId.value());
    }

    @Test
    @DisplayName("異常系：nullを指定した場合、例外が発生する")
    void nullValue_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new RankId(null));
    }

    @Test
    @DisplayName("等価性テスト：同じUUIDで生成したRankIdは等しい")
    void sameUuid_shouldBeEqual() {
        // Arrange
        UUID uuid = UUID.randomUUID();

        // Act
        RankId id1 = new RankId(uuid);
        RankId id2 = new RankId(uuid);

        // Assert
        assertEquals(id1, id2);
        assertEquals(id1.hashCode(), id2.hashCode());
    }

    @Test
    @DisplayName("非等価性テスト：異なるUUIDで生成したRankIdは等しくない")
    void differentUuid_shouldNotBeEqual() {
        // Act
        RankId id1 = RankId.generate();
        RankId id2 = RankId.generate();

        // Assert
        assertNotEquals(id1, id2);
    }
}
