package com.favick.domain.feature.rank.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

@DisplayName("RankName のテスト")
class RankNameTest {
    @Test
    @DisplayName("正常系：有効なRankNameValueを指定した場合、インスタンスが生成される")
    void validRankNameValue_shouldCreateObject() {
        // Arrange
        RankNameValue value = RankNameValue.RANK_SEED;

        // Act
        RankName rankName = new RankName(value);

        // Assert
        assertEquals(value, rankName.value());
    }

    @Test
    @DisplayName("異常系：nullを指定した場合、例外が発生する")
    void nullValue_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new RankName(null));
    }

    @Test
    @DisplayName("正常系：fromStringで有効な文字列を指定した場合、対応するRankNameを取得できる")
    void validString_shouldReturnRankName() {
        // Act
        RankName rankName = RankName.fromString("RANK_SEED");

        // Assert
        assertEquals(RankNameValue.RANK_SEED, rankName.value());
    }

    @Test
    @DisplayName("異常系：fromStringでnullを指定した場合、例外が発生する")
    void nullString_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> RankName.fromString(null));
    }

    @Test
    @DisplayName("異常系：fromStringで無効な文字列を指定した場合、例外が発生する")
    void invalidString_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> RankName.fromString("INVALID_RANK"));
    }
}
