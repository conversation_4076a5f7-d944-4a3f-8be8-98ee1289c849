package com.favick.domain.feature.like.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("LikeId のテスト")
class LikeIdTest {

    @Test
    @DisplayName("LikeIdの生成が成功すること")
    void generate_shouldCreateValidLikeId() {
        // Act
        LikeId likeId = LikeId.generate();

        // Assert
        assertNotNull(likeId);
        assertNotNull(likeId.value());
        assertInstanceOf(UUID.class, likeId.value());
    }

    @Test
    @DisplayName("nullでLikeIdを生成するとDomainExceptionがスローされること")
    void createWithNull_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new LikeId(null));
    }
}
