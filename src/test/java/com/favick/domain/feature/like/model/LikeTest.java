package com.favick.domain.feature.like.model;

import com.favick.domain.exception.ValueObjectException;
import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.user.model.UserId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("Like のテスト")
class LikeTest {

    @Test
    @DisplayName("正常系：Likeが正しく生成される")
    void createLike_shouldInitializeCorrectly() {
        // Arrange
        UserId userId = new UserId(UUID.randomUUID());
        FaveId faveId = new FaveId(UUID.randomUUID());

        // Act
        Like like = Like.create(userId, faveId);

        // Assert
        assertNotNull(like.getId());
        assertEquals(userId, like.getUserId());
        assertEquals(faveId, like.getFaveId());
        assertNotNull(like.getCreatedAt());
    }

    @Test
    @DisplayName("正常系：コンストラクタで全ての値を設定できる")
    void constructor_shouldSetAllValues() {
        // Arrange
        LikeId likeId = new LikeId(UUID.randomUUID());
        UserId userId = new UserId(UUID.randomUUID());
        FaveId faveId = new FaveId(UUID.randomUUID());
        LocalDateTime createdAt = LocalDateTime.now();

        // Act
        Like like = Like.reconstruct(likeId, userId, faveId, createdAt);

        // Assert
        assertEquals(likeId, like.getId());
        assertEquals(userId, like.getUserId());
        assertEquals(faveId, like.getFaveId());
        assertEquals(createdAt, like.getCreatedAt());
    }

    @Test
    @DisplayName("異常系：nullのuserIdでLikeを作成すると例外が発生する")
    void createLike_withNullUserId_shouldThrowException() {
        // Arrange
        FaveId faveId = new FaveId(UUID.randomUUID());

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> Like.create(null, faveId));
    }

    @Test
    @DisplayName("異常系：nullのfaveIdでLikeを作成すると例外が発生する")
    void createLike_withNullFaveId_shouldThrowException() {
        // Arrange
        UserId userId = new UserId(UUID.randomUUID());

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> Like.create(userId, null));
    }
}
