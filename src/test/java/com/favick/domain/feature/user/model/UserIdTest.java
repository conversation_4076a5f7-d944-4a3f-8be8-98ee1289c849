package com.favick.domain.feature.user.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("UserId のテスト")
class UserIdTest {
    @Test
    @DisplayName("UserIdの生成が成功すること")
    void generate_shouldCreateValidUserId() {
        // Act
        UserId userId = UserId.generate();

        // Assert
        assertNotNull(userId);
        assertNotNull(userId.value());
        assertInstanceOf(UUID.class, userId.value());
    }

    @Test
    @DisplayName("nullを指定してUserIdを生成するとDomainExceptionがスローされること")
    void createWithNull_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new UserId(null));
    }

    @Test
    @DisplayName("同じUUIDから生成されたUserIdは等しいと判定されること")
    void userIdsWithSameValue_shouldBeEqual() {
        // Arrange
        UUID uuid = UUID.randomUUID();

        // Act
        UserId id1 = new UserId(uuid);
        UserId id2 = new UserId(uuid);

        // Assert
        assertEquals(id1, id2);
        assertEquals(id1.hashCode(), id2.hashCode());
    }

    @Test
    @DisplayName("異なるUUIDのUserIdは等しくないこと")
    void userIdsWithDifferentValue_shouldNotBeEqual() {
        // Act
        UserId id1 = UserId.generate();
        UserId id2 = UserId.generate();

        // Assert
        assertNotEquals(id1, id2);
    }
}
