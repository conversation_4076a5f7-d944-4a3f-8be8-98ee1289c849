package com.favick.domain.feature.user.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

@DisplayName("UserName のテスト")
class UserNameTest {
    @Test
    @DisplayName("正常系：50文字以内の有効なユーザー名を指定した場合、インスタンスが生成される")
    void validName_shouldCreateObject() {
        // Arrange
        String validName = "テストユーザー";

        // Act
        UserName userName = new UserName(validName);

        // Assert
        assertEquals(validName, userName.value());
    }

    @Test
    @DisplayName("正常系：ちょうど50文字のユーザー名を指定した場合、インスタンスが生成される")
    void maxLengthName_shouldCreateObject() {
        // Arrange
        String maxLengthName = "a".repeat(50);

        // Act
        UserName userName = new UserName(maxLengthName);

        // Assert
        assertEquals(maxLengthName, userName.value());
    }

    @Test
    @DisplayName("異常系：nullを指定した場合、例外が発生する")
    void nullName_shouldThrowException() {
        assertThrows(ValueObjectException.class, () -> new UserName(null));
    }

    @Test
    @DisplayName("異常系：空文字を指定した場合、例外が発生する")
    void emptyName_shouldThrowException() {
        assertThrows(ValueObjectException.class, () -> new UserName(""));
    }

    @Test
    @DisplayName("異常系：空白のみを指定した場合、例外が発生する")
    void blankName_shouldThrowException() {
        assertThrows(ValueObjectException.class, () -> new UserName("   "));
    }

    @Test
    @DisplayName("異常系：51文字以上のユーザー名を指定した場合、例外が発生する")
    void tooLongName_shouldThrowException() {
        // Arrange
        String tooLongName = "a".repeat(51);

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new UserName(tooLongName));
    }
}
