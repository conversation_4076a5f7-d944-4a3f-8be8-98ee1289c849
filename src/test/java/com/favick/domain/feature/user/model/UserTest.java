package com.favick.domain.feature.user.model;

import com.favick.domain.feature.rank.model.RankId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("User のテスト")
class UserTest {
    private static final RankId TEST_RANK_ID = RankId.generate();
    private static final UserName TEST_NAME = new UserName("テストユーザー");
    private static final UserEmail TEST_EMAIL = new UserEmail("<EMAIL>");
    private static final UserPassword TEST_PASSWORD = new UserPassword("$2a$10$encodedPassword");
    private static final UserImageUri TEST_IMAGE_URI = new UserImageUri("https://example.com/image.jpg");

    @Test
    @DisplayName("コンストラクタテスト：全フィールドを設定できる")
    void constructor_shouldSetAllFields() {
        // Arrange
        UserId id = UserId.generate();
        LocalDateTime now = LocalDateTime.now();

        // Act
        User user = User.reconstruct(
            id,
            TEST_RANK_ID,
            TEST_NAME,
            TEST_EMAIL,
            TEST_PASSWORD,
            new UserEnabled(true),
            TEST_IMAGE_URI,
            now,
            now
        );

        // Assert
        assertEquals(id, user.getId());
        assertEquals(TEST_RANK_ID, user.getRankId());
        assertEquals(TEST_NAME, user.getName());
        assertEquals(TEST_EMAIL, user.getEmail());
        assertEquals(TEST_PASSWORD, user.getPassword());
        assertTrue(user.isEnabled());
        assertEquals(TEST_IMAGE_URI, user.getImageUrl());
        assertEquals(now, user.getCreatedAt());
        assertEquals(now, user.getUpdatedAt());
    }

    @Test
    @DisplayName("ファクトリメソッドテスト：新規ユーザーを作成できる")
    void createNewUser_shouldSetDefaultValues() {
        // Act
        User user = User.create(TEST_RANK_ID, TEST_NAME, TEST_EMAIL, TEST_PASSWORD, TEST_IMAGE_URI);

        // Assert
        assertNotNull(user.getId());
        assertEquals(TEST_RANK_ID, user.getRankId());
        assertEquals(TEST_NAME, user.getName());
        assertEquals(TEST_EMAIL, user.getEmail());
        assertEquals(TEST_PASSWORD, user.getPassword());
        assertFalse(user.isEnabled());
        assertEquals(TEST_IMAGE_URI, user.getImageUrl());
        assertNotNull(user.getCreatedAt());
        assertEquals(user.getCreatedAt(), user.getUpdatedAt());
    }

    @Test
    @DisplayName("状態変更テスト：ユーザーを有効にできる")
    void enable_shouldSetEnabledToTrue() {
        // Arrange
        User user = User.create(TEST_RANK_ID, TEST_NAME, TEST_EMAIL, TEST_PASSWORD, TEST_IMAGE_URI);
        LocalDateTime originalUpdatedAt = user.getUpdatedAt();

        // Act
        user.enable();

        // Assert
        assertTrue(user.isEnabled());
        assertTrue(user.getUpdatedAt().isAfter(originalUpdatedAt));
    }

    @Test
    @DisplayName("状態変更テスト：ユーザーを無効にできる")
    void disable_shouldSetEnabledToFalse() {
        // Arrange
        User user = User.create(TEST_RANK_ID, TEST_NAME, TEST_EMAIL, TEST_PASSWORD, TEST_IMAGE_URI);
        user.enable();
        LocalDateTime originalUpdatedAt = user.getUpdatedAt();

        // Act
        user.disable();

        // Assert
        assertFalse(user.isEnabled());
        assertTrue(user.getUpdatedAt().isAfter(originalUpdatedAt));
    }

    @Test
    @DisplayName("プロフィール更新テスト：名前、メール、画像を更新できる")
    void updateProfile_shouldUpdateFields() {
        // Arrange
        User user = User.create(TEST_RANK_ID, TEST_NAME, TEST_EMAIL, TEST_PASSWORD, TEST_IMAGE_URI);
        UserName newName = new UserName("新しい名前");
        UserEmail newEmail = new UserEmail("<EMAIL>");
        UserImageUri newImage = new UserImageUri("https://example.com/new.jpg");
        LocalDateTime originalUpdatedAt = user.getUpdatedAt();

        // Act
        user.updateProfile(newName, newEmail, newImage);

        // Assert
        assertEquals(newName, user.getName());
        assertEquals(newEmail, user.getEmail());
        assertEquals(newImage, user.getImageUrl());
        assertTrue(user.getUpdatedAt().isAfter(originalUpdatedAt));
    }

    @Test
    @DisplayName("パスワード変更テスト：新しいパスワードに更新できる")
    void changePassword_shouldUpdatePassword() {
        // Arrange
        User user = User.create(TEST_RANK_ID, TEST_NAME, TEST_EMAIL, TEST_PASSWORD, TEST_IMAGE_URI);
        UserPassword newPassword = new UserPassword("$2a$10$newEncodedPassword");
        LocalDateTime originalUpdatedAt = user.getUpdatedAt();

        // Act
        user.changePassword(newPassword);

        // Assert
        assertEquals(newPassword, user.getPassword());
        assertTrue(user.getUpdatedAt().isAfter(originalUpdatedAt));
    }

    @Test
    @DisplayName("ランク変更テスト：新しいランクに更新できる")
    void changeRank_shouldUpdateRankId() {
        // Arrange
        User user = User.create(TEST_RANK_ID, TEST_NAME, TEST_EMAIL, TEST_PASSWORD, TEST_IMAGE_URI);
        RankId newRankId = RankId.generate();
        LocalDateTime originalUpdatedAt = user.getUpdatedAt();

        // Act
        user.changeRank(newRankId);

        // Assert
        assertEquals(newRankId, user.getRankId());
        assertTrue(user.getUpdatedAt().isAfter(originalUpdatedAt));
    }

    @Test
    @DisplayName("toString()テスト：主要なフィールドを含む文字列を返す")
    void toString_shouldContainMainFields() {
        // Arrange
        User user = User.create(TEST_RANK_ID, TEST_NAME, TEST_EMAIL, TEST_PASSWORD, TEST_IMAGE_URI);

        // Act
        String result = user.toString();

        // Assert
        assertTrue(result.contains("User{"));
        assertTrue(result.contains("id=" + user.getId().value()));
        assertTrue(result.contains("name=" + TEST_NAME.value()));
        assertTrue(result.contains("email=" + TEST_EMAIL.value()));
        assertTrue(result.contains("rankId=" + TEST_RANK_ID.value()));
    }
}
