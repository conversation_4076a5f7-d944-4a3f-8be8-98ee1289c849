package com.favick.domain.feature.user.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

class UserImageUriTest {
    @Test
    @DisplayName("正常系：255文字以内の有効なURIを指定した場合、インスタンスが生成される")
    void validUri_shouldCreateObject() {
        // Arrange
        String validUri = "https://example.com/image.jpg";

        // Act
        UserImageUri uri = new UserImageUri(validUri);

        // Assert
        assertEquals(validUri, uri.value());
    }

    @Test
    @DisplayName("正常系：ちょうど255文字のURIを指定した場合、インスタンスが生成される")
    void maxLengthUri_shouldCreateObject() {
        // Arrange
        String base = "https://example.com/";
        String extension = ".jpg";
        String middle = "a".repeat(255 - base.length() - extension.length());
        String maxLengthUri = base + middle + extension;

        // 文字数確認 (デバッグ用)
        assertEquals(255, maxLengthUri.length(), "URI length should be exactly 255 characters");

        // Act
        UserImageUri uri = new UserImageUri(maxLengthUri);

        // Assert
        assertEquals(maxLengthUri, uri.value());
    }

    @Test
    @DisplayName("正常系：nullを指定した場合、空文字列が設定される")
    void nullUri_shouldSetEmptyString() {
        // Act
        UserImageUri uri = new UserImageUri(null);

        // Assert
        assertEquals("", uri.value());
    }

    @Test
    @DisplayName("異常系：256文字以上のURIを指定した場合、例外が発生する")
    void tooLongUri_shouldThrowException() {
        // Arrange
        String tooLongUri = "https://example.com/" + "a".repeat(236) + ".jpg";

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new UserImageUri(tooLongUri));
    }
}
