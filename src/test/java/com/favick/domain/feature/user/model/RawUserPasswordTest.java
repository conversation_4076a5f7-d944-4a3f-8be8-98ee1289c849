package com.favick.domain.feature.user.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

@DisplayName("RawUserPassword のテスト")
class RawUserPasswordTest {

    @Test
    @DisplayName("正常系：有効なパスワードを作成できる")
    void validPassword_shouldBeCreated() {
        // Arrange
        String validPassword = "ValidPass123";

        // Act
        RawUserPassword password = new RawUserPassword(validPassword);

        // Assert
        assertEquals(validPassword, password.value());
    }

    @Test
    @DisplayName("異常系：nullは許可しない")
    void nullPassword_shouldThrowException() {
        assertThrows(ValueObjectException.class, () -> {
            new RawUserPassword(null);
        });
    }

    @Test
    @DisplayName("異常系：空文字は許可しない")
    void emptyPassword_shouldThrowException() {
        assertThrows(ValueObjectException.class, () -> {
            new RawUserPassword("");
        });
    }

    @Test
    @DisplayName("異常系：短すぎるパスワードは許可しない")
    void tooShortPassword_shouldThrowException() {
        assertThrows(ValueObjectException.class, () -> {
            new RawUserPassword("Short1");
        });
    }

    @Test
    @DisplayName("異常系：長すぎるパスワードは許可しない")
    void tooLongPassword_shouldThrowException() {
        assertThrows(ValueObjectException.class, () -> {
            new RawUserPassword("a".repeat(73));
        });
    }

    @ParameterizedTest
    @ValueSource(strings = {"nouppercase1", "NOLOWERCASE1", "NoNumbers"})
    @DisplayName("異常系：パターンに一致しないパスワードは許可しない")
    void invalidPatternPassword_shouldThrowException(String invalidPassword) {
        assertThrows(ValueObjectException.class, () -> {
            new RawUserPassword(invalidPassword);
        });
    }

    @ParameterizedTest
    @ValueSource(strings = {"password", "12345678", "11111111"})
    @DisplayName("異常系：一般的なパスワードは許可しない")
    void commonPassword_shouldThrowException(String commonPassword) {
        assertThrows(ValueObjectException.class, () -> {
            new RawUserPassword(commonPassword);
        });
    }
}
