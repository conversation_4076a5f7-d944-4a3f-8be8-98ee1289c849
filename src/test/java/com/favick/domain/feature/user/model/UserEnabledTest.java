package com.favick.domain.feature.user.model;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class UserEnabledTest {
    @Test
    @DisplayName("正常系：コンストラクタでtrueを設定した場合、有効状態になる")
    void trueConstructor_shouldBeEnabled() {
        // Act
        UserEnabled enabled = new UserEnabled(true);

        // Assert
        assertTrue(enabled.value());
    }

    @Test
    @DisplayName("正常系：コンストラクタでfalseを設定した場合、無効状態になる")
    void falseConstructor_shouldBeDisabled() {
        // Act
        UserEnabled enabled = new UserEnabled(false);

        // Assert
        assertFalse(enabled.value());
    }

    @Test
    @DisplayName("正常系：enable()で有効状態になる")
    void enable_shouldSetTrue() {
        // Arrange
        UserEnabled disabled = new UserEnabled(false);

        // Act
        UserEnabled enabled = disabled.enable();

        // Assert
        assertTrue(enabled.value());
    }

    @Test
    @DisplayName("正常系：disable()で無効状態になる")
    void disable_shouldSetFalse() {
        // Arrange
        UserEnabled enabled = new UserEnabled(true);

        // Act
        UserEnabled disabled = enabled.disable();

        // Assert
        assertFalse(disabled.value());
    }
}
