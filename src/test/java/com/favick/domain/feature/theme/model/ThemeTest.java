package com.favick.domain.feature.theme.model;

import com.favick.domain.exception.EntityException;
import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("Theme のテスト")
class ThemeTest {
    @Test
    @DisplayName("正常系：開始日が今日の場合、生成に成功する")
    void todayDate_shouldBeAccepted() {
        // Arrange
        LocalDate today = LocalDate.now();

        // Act & Assert
        assertDoesNotThrow(() -> new ThemeStartDate(today));
    }

    @Test
    @DisplayName("正常系：テーマを新しく生成した場合、IDがUUIDとして生成されていること")
    void newTheme_shouldGenerateValidId() {
        // Act
        Theme theme = Theme.create(
            new ThemeType(ThemeTypeValue.MOVIE),
            new ThemeStartDate(LocalDate.now().plusDays(1))
        );

        // Assert
        assertNotNull(theme.getId());
        assertNotNull(theme.getId().value());
    }

    @Test
    @DisplayName("正常系：テーマを新しい値で更新すると、各プロパティが更新される")
    void updateWithValidValues_shouldUpdateTheme() {
        // Arrange
        Theme theme = Theme.create(
            new ThemeType(ThemeTypeValue.PHOTO),
            new ThemeStartDate(LocalDate.now().plusDays(1))
        );
        ThemeId beforeUpdateId = theme.getId();
        ThemeType newType = new ThemeType((ThemeTypeValue.AUDIO));
        ThemeStartDate futureDate = new ThemeStartDate(LocalDate.now().plusDays(2));

        // Act
        theme.update(newType, futureDate);

        // Assert
        assertEquals(beforeUpdateId, theme.getId());
        assertEquals(newType, theme.getType());
        assertEquals(futureDate, theme.getStartDate());
    }

    @Test
    @DisplayName("異常系：過去の日付でテーマを作成すると例外が発生する")
    void createWithPastDate_shouldThrowException() {
        // Arrange
        LocalDate pastDate = LocalDate.now().minusDays(1);
        ThemeStartDate pastStartDate = new ThemeStartDate(pastDate);

        // Act & Assert
        assertThrows(ValueObjectException.class, () ->
            Theme.create(new ThemeType(ThemeTypeValue.PHOTO), pastStartDate)
        );
    }

    @Test
    @DisplayName("異常系：過去の日付でテーマを更新すると例外が発生する")
    void updateWithPastDate_shouldThrowException() {
        // Arrange
        Theme theme = Theme.create(
            new ThemeType(ThemeTypeValue.PHOTO),
            new ThemeStartDate(LocalDate.now().plusDays(1))
        );
        LocalDate pastDate = LocalDate.now().minusDays(1);

        // Act & Assert
        assertThrows(ValueObjectException.class, () ->
            theme.update(new ThemeType(ThemeTypeValue.MOVIE), new ThemeStartDate(pastDate))
        );
    }

    @Test
    @DisplayName("異常系：過去または現在のテーマを更新すると例外が発生する")
    void updatePastOrPresentTheme_shouldThrowException() {
        // Arrange - 過去のテーマを再構築
        LocalDate pastDate = LocalDate.now().minusDays(1);
        LocalDateTime now = LocalDateTime.now();
        Theme pastTheme = Theme.reconstruct(
            ThemeId.generate(),
            new ThemeType(ThemeTypeValue.PHOTO),
            new ThemeStartDate(pastDate),
            now.minusDays(2),
            now.minusDays(1)
        );

        // Act & Assert
        assertThrows(EntityException.class, () ->
            pastTheme.update(
                new ThemeType(ThemeTypeValue.MOVIE),
                new ThemeStartDate(LocalDate.now().plusDays(1))
            )
        );
    }

    @Test
    @DisplayName("正常系：getCurrentStatus()でUPCOMINGステータスを取得できる")
    void getCurrentStatus_shouldReturnUpcoming() {
        // Arrange
        LocalDate futureDate = LocalDate.now().plusDays(1);
        Theme theme = Theme.create(
            new ThemeType(ThemeTypeValue.PHOTO),
            new ThemeStartDate(futureDate)
        );

        // Act
        ThemeStatus status = theme.getCurrentStatus();

        // Assert
        assertEquals(ThemeStatusValue.UPCOMING, status.value());
    }

    @Test
    @DisplayName("正常系：getCurrentStatus()でACTIVEステータスを取得できる")
    void getCurrentStatus_shouldReturnActive() {
        // Arrange
        LocalDate today = LocalDate.now();
        Theme theme = Theme.create(
            new ThemeType(ThemeTypeValue.PHOTO),
            new ThemeStartDate(today)
        );

        // Act
        ThemeStatus status = theme.getCurrentStatus();

        // Assert
        assertEquals(ThemeStatusValue.ACTIVE, status.value());
    }

    @Test
    @DisplayName("正常系：getCurrentStatus()でPASTステータスを取得できる")
    void getCurrentStatus_shouldReturnPast() {
        // Arrange - 過去のテーマを再構築
        LocalDate pastDate = LocalDate.now().minusDays(1);
        LocalDateTime now = LocalDateTime.now();
        Theme pastTheme = Theme.reconstruct(
            ThemeId.generate(),
            new ThemeType(ThemeTypeValue.PHOTO),
            new ThemeStartDate(pastDate),
            now.minusDays(2),
            now.minusDays(1)
        );

        // Act
        ThemeStatus status = pastTheme.getCurrentStatus();

        // Assert
        assertEquals(ThemeStatusValue.PAST, status.value());
    }

    @Test
    @DisplayName("正常系：未来のテーマはisEditable()でtrueを返す")
    void isEditable_shouldReturnTrueForFutureTheme() {
        // Arrange
        LocalDate futureDate = LocalDate.now().plusDays(1);
        Theme theme = Theme.create(
            new ThemeType(ThemeTypeValue.PHOTO),
            new ThemeStartDate(futureDate)
        );

        // Act & Assert
        assertTrue(theme.isEditable());
    }

    @Test
    @DisplayName("正常系：過去のテーマはisEditable()でfalseを返す")
    void isEditable_shouldReturnFalseForPastTheme() {
        // Arrange - 過去のテーマを再構築
        LocalDate pastDate = LocalDate.now().minusDays(1);
        LocalDateTime now = LocalDateTime.now();
        Theme pastTheme = Theme.reconstruct(
            ThemeId.generate(),
            new ThemeType(ThemeTypeValue.PHOTO),
            new ThemeStartDate(pastDate),
            now.minusDays(2),
            now.minusDays(1)
        );

        // Act & Assert
        assertFalse(pastTheme.isEditable());
    }
}
