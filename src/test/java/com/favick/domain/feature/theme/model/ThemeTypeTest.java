package com.favick.domain.feature.theme.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@DisplayName("ThemeType のテスト")
class ThemeTypeTest {
    @Test
    @DisplayName("正常系: PHOTO を生成できる")
    void shouldCreatePhotoTypeFromString() {
        ThemeType type = ThemeType.fromString("PHOTO");

        assertThat(type.value()).isEqualTo(ThemeTypeValue.PHOTO);
    }

    @Test
    @DisplayName("正常系: MOVIE を生成できる")
    void shouldCreateMovieTypeFromString() {
        ThemeType type = ThemeType.fromString("MOVIE");

        assertThat(type.value()).isEqualTo(ThemeTypeValue.MOVIE);
    }

    @Test
    @DisplayName("正常系: AUDIO を生成できる")
    void shouldCreateAudioTypeFromString() {
        ThemeType type = ThemeType.fromString("AUDIO");

        assertThat(type.value()).isEqualTo(ThemeTypeValue.AUDIO);
    }

    @Test
    @DisplayName("異常系: 無効な種類を指定すると例外を投げる")
    void shouldThrowExceptionWhenInvalidType() {
        assertThatThrownBy(() -> ThemeType.fromString("INVALID"))
            .isInstanceOf(ValueObjectException.class)
            .hasMessageContaining("Domain Error");
    }

    @Test
    @DisplayName("異常系: nullを指定すると例外を投げる")
    void shouldThrowExceptionWhenNullType() {
        assertThatThrownBy(() -> ThemeType.fromString(null))
            .isInstanceOf(ValueObjectException.class)
            .hasMessageContaining("Domain Error");
    }

    @Test
    @DisplayName("正常系：コンストラクタでPHOTOタイプを生成できる")
    void shouldCreatePhotoTypeFromConstructor() {
        // Act
        ThemeType type = new ThemeType(ThemeTypeValue.PHOTO);

        // Assert
        assertThat(type.value()).isEqualTo(ThemeTypeValue.PHOTO);
    }

    @Test
    @DisplayName("異常系：コンストラクタでnullを指定すると例外を投げる")
    void shouldThrowExceptionWhenNullInConstructor() {
        // Act & Assert
        assertThatThrownBy(() -> new ThemeType(null))
            .isInstanceOf(ValueObjectException.class)
            .hasMessageContaining("Domain Error");
    }

    @Test
    @DisplayName("正常系：同じ値のThemeTypeは等しいと判定される")
    void themeTypeWithSameValue_shouldBeEqual() {
        // Arrange
        ThemeType type1 = new ThemeType(ThemeTypeValue.PHOTO);
        ThemeType type2 = new ThemeType(ThemeTypeValue.PHOTO);

        // Act & Assert
        assertThat(type1).isEqualTo(type2);
        assertThat(type1.hashCode()).isEqualTo(type2.hashCode());
    }

    @Test
    @DisplayName("正常系：異なる値のThemeTypeは等しくないと判定される")
    void themeTypeWithDifferentValue_shouldNotBeEqual() {
        // Arrange
        ThemeType type1 = new ThemeType(ThemeTypeValue.PHOTO);
        ThemeType type2 = new ThemeType(ThemeTypeValue.MOVIE);

        // Act & Assert
        assertThat(type1).isNotEqualTo(type2);
    }
}
