package com.favick.domain.feature.theme.model;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("ThemeStatusValue のテスト")
class ThemeStatusValueTest {
    @Test
    @DisplayName("UPCOMING（開催予定）が正しく定義されていること")
    void upcoming_shouldBeDefined() {
        // Act & Assert
        assertEquals("UPCOMING", ThemeStatusValue.UPCOMING.name());
    }

    @Test
    @DisplayName("ACTIVE（開催中）が正しく定義されていること")
    void active_shouldBeDefined() {
        // Act & Assert
        assertEquals("ACTIVE", ThemeStatusValue.ACTIVE.name());
    }

    @Test
    @DisplayName("PAST（終了）が正しく定義されていること")
    void past_shouldBeDefined() {
        // Act & Assert
        assertEquals("PAST", ThemeStatusValue.PAST.name());
    }

    @Test
    @DisplayName("valueOf()で正しい値を取得できること")
    void valueOf_shouldReturnCorrectValue() {
        // Act & Assert
        assertEquals(ThemeStatusValue.UPCOMING, ThemeStatusValue.valueOf("UPCOMING"));
        assertEquals(ThemeStatusValue.ACTIVE, ThemeStatusValue.valueOf("ACTIVE"));
        assertEquals(ThemeStatusValue.PAST, ThemeStatusValue.valueOf("PAST"));
    }

    @Test
    @DisplayName("無効な値でvalueOf()を呼ぶとIllegalArgumentExceptionがスローされること")
    void valueOf_withInvalidValue_shouldThrowException() {
        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> ThemeStatusValue.valueOf("INVALID"));
    }

    @Test
    @DisplayName("全てのテーマステータスが3つ定義されていること")
    void values_shouldContainThreeStatuses() {
        // Act
        ThemeStatusValue[] values = ThemeStatusValue.values();

        // Assert
        assertEquals(3, values.length);
        assertTrue(java.util.Arrays.asList(values).contains(ThemeStatusValue.UPCOMING));
        assertTrue(java.util.Arrays.asList(values).contains(ThemeStatusValue.ACTIVE));
        assertTrue(java.util.Arrays.asList(values).contains(ThemeStatusValue.PAST));
    }
}