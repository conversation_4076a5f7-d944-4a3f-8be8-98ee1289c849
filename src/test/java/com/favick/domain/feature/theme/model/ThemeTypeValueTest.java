package com.favick.domain.feature.theme.model;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("ThemeTypeValue のテスト")
class ThemeTypeValueTest {
    @Test
    @DisplayName("PHOTO（写真）が正しく定義されていること")
    void photo_shouldBeDefined() {
        // Act & Assert
        assertEquals("PHOTO", ThemeTypeValue.PHOTO.name());
    }

    @Test
    @DisplayName("MOVIE（動画）が正しく定義されていること")
    void movie_shouldBeDefined() {
        // Act & Assert
        assertEquals("MOVIE", ThemeTypeValue.MOVIE.name());
    }

    @Test
    @DisplayName("AUDIO（音声）が正しく定義されていること")
    void audio_shouldBeDefined() {
        // Act & Assert
        assertEquals("AUDIO", ThemeTypeValue.AUDIO.name());
    }

    @Test
    @DisplayName("valueOf()で正しい値を取得できること")
    void valueOf_shouldReturnCorrectValue() {
        // Act & Assert
        assertEquals(ThemeTypeValue.PHOTO, ThemeTypeValue.valueOf("PHOTO"));
        assertEquals(ThemeTypeValue.MOVIE, ThemeTypeValue.valueOf("MOVIE"));
        assertEquals(ThemeTypeValue.AUDIO, ThemeTypeValue.valueOf("AUDIO"));
    }

    @Test
    @DisplayName("無効な値でvalueOf()を呼ぶとIllegalArgumentExceptionがスローされること")
    void valueOf_withInvalidValue_shouldThrowException() {
        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> ThemeTypeValue.valueOf("INVALID"));
    }

    @Test
    @DisplayName("全てのテーマタイプが3つ定義されていること")
    void values_shouldContainThreeTypes() {
        // Act
        ThemeTypeValue[] values = ThemeTypeValue.values();

        // Assert
        assertEquals(3, values.length);
        assertTrue(java.util.Arrays.asList(values).contains(ThemeTypeValue.PHOTO));
        assertTrue(java.util.Arrays.asList(values).contains(ThemeTypeValue.MOVIE));
        assertTrue(java.util.Arrays.asList(values).contains(ThemeTypeValue.AUDIO));
    }
}