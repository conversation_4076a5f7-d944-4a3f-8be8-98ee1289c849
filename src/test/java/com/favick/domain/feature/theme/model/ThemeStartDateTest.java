package com.favick.domain.feature.theme.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("ThemeStartDate のテスト")
class ThemeStartDateTest {
    @Test
    @DisplayName("正常系：任意の開始日でインスタンスが生成される")
    void validStartDate_shouldCreateObject() {
        // Arrange
        LocalDate value = LocalDate.of(2025, 5, 15);

        // Act
        ThemeStartDate startDate = new ThemeStartDate(value);

        // Assert
        assertEquals(value, startDate.value());
    }

    @Test
    @DisplayName("正常系：開始日が今日の場合、インスタンスが生成される")
    void todayDate_shouldBeAccepted() {
        // Arrange
        LocalDate today = LocalDate.now();

        // Act & Assert
        assertDoesNotThrow(() -> new ThemeStartDate(today));
    }

    @Test
    @DisplayName("正常系：開始日が未来日の場合、インスタンスが生成される")
    void futureDate_shouldBeAccepted() {
        // Arrange
        LocalDate futureDate = LocalDate.now().plusDays(10);

        // Act
        ThemeStartDate startDate = new ThemeStartDate(futureDate);

        // Assert
        assertEquals(futureDate, startDate.value());
    }

    @Test
    @DisplayName("異常系：nullを指定した場合、例外が発生する")
    void nullStartDate_shouldThrowException() {
        // Arrange
        LocalDate value = null;

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new ThemeStartDate(value));
    }

    @Test
    @DisplayName("正常系：同じ日付のThemeStartDateは等しいと判定される")
    void themeStartDateWithSameValue_shouldBeEqual() {
        // Arrange
        LocalDate date = LocalDate.of(2025, 5, 1);
        ThemeStartDate startDate1 = new ThemeStartDate(date);
        ThemeStartDate startDate2 = new ThemeStartDate(date);

        // Act & Assert
        assertEquals(startDate1, startDate2);
        assertEquals(startDate1.hashCode(), startDate2.hashCode());
    }

    @Test
    @DisplayName("正常系：異なる日付のThemeStartDateは等しくないと判定される")
    void themeStartDateWithDifferentValue_shouldNotBeEqual() {
        // Arrange
        LocalDate date1 = LocalDate.of(2025, 5, 1);
        LocalDate date2 = LocalDate.of(2025, 6, 1);
        ThemeStartDate startDate1 = new ThemeStartDate(date1);
        ThemeStartDate startDate2 = new ThemeStartDate(date2);

        // Act & Assert
        assertNotEquals(startDate1, startDate2);
    }
}
