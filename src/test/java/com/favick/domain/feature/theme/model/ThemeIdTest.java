package com.favick.domain.feature.theme.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("ThemeId のテスト")
class ThemeIdTest {
    @Test
    @DisplayName("ThemeIdの生成が成功すること")
    void generate_shouldCreateValidThemeId() {
        // Act
        ThemeId themeId = ThemeId.generate();

        // Assert
        assertNotNull(themeId);
        assertNotNull(themeId.value());
        assertInstanceOf(UUID.class, themeId.value());
    }

    @Test
    @DisplayName("nullでThemeIdを生成するとDomainExceptionがスローされること")
    void createWithNull_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new ThemeId(null));
    }

    @Test
    @DisplayName("同じUUIDから生成されたThemeIdは等しいと判定されること")
    void themeIdsWithSameValue_shouldBeEqual() {
        // Arrange
        UUID uuid = UUID.randomUUID();

        // Act
        ThemeId id1 = new ThemeId(uuid);
        ThemeId id2 = new ThemeId(uuid);

        // Assert
        assertEquals(id1, id2);
        assertEquals(id1.hashCode(), id2.hashCode());
    }

    @Test
    @DisplayName("異なるUUIDのThemeIdは等しくないこと")
    void themeIdsWithDifferentValue_shouldNotBeEqual() {
        // Act
        ThemeId id1 = ThemeId.generate();
        ThemeId id2 = ThemeId.generate();

        // Assert
        assertNotEquals(id1, id2);
    }
}
