package com.favick.domain.feature.theme.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("ThemeStatus のテスト")
class ThemeStatusTest {
    @Test
    @DisplayName("正常系：UPCOMINGステータスを生成できる")
    void shouldCreateUpcomingStatus() {
        // Act
        ThemeStatus status = new ThemeStatus(ThemeStatusValue.UPCOMING);

        // Assert
        assertEquals(ThemeStatusValue.UPCOMING, status.value());
    }

    @Test
    @DisplayName("正常系：ACTIVEステータスを生成できる")
    void shouldCreateActiveStatus() {
        // Act
        ThemeStatus status = new ThemeStatus(ThemeStatusValue.ACTIVE);

        // Assert
        assertEquals(ThemeStatusValue.ACTIVE, status.value());
    }

    @Test
    @DisplayName("正常系：PASTステータスを生成できる")
    void shouldCreatePastStatus() {
        // Act
        ThemeStatus status = new ThemeStatus(ThemeStatusValue.PAST);

        // Assert
        assertEquals(ThemeStatusValue.PAST, status.value());
    }

    @Test
    @DisplayName("異常系：nullでステータスを生成すると例外が発生する")
    void shouldThrowExceptionWhenNullValue() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new ThemeStatus(null));
    }

    @Test
    @DisplayName("正常系：同じ値のThemeStatusは等しいと判定される")
    void themeStatusWithSameValue_shouldBeEqual() {
        // Arrange
        ThemeStatus status1 = new ThemeStatus(ThemeStatusValue.UPCOMING);
        ThemeStatus status2 = new ThemeStatus(ThemeStatusValue.UPCOMING);

        // Act & Assert
        assertEquals(status1, status2);
        assertEquals(status1.hashCode(), status2.hashCode());
    }

    @Test
    @DisplayName("正常系：異なる値のThemeStatusは等しくないと判定される")
    void themeStatusWithDifferentValue_shouldNotBeEqual() {
        // Arrange
        ThemeStatus status1 = new ThemeStatus(ThemeStatusValue.UPCOMING);
        ThemeStatus status2 = new ThemeStatus(ThemeStatusValue.ACTIVE);

        // Act & Assert
        assertNotEquals(status1, status2);
    }
}
