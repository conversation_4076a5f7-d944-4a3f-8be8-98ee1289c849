package com.favick.domain.feature.review.model;

import com.favick.domain.exception.ValueObjectException;
import com.favick.domain.feature.fave.model.FaveId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("Review のテスト")
class ReviewTest {
    private static final ReviewStatus PENDING_STATUS = new ReviewStatus(ReviewStatusValue.PENDING);
    private static final ReviewStatus APPROVED_STATUS = new ReviewStatus(ReviewStatusValue.APPROVED);

    @Test
    @DisplayName("新しい審査を作成できること")
    void create_shouldCreateNewReview() {
        // Arrange
        FaveId faveId = new FaveId(UUID.randomUUID());

        // Act
        Review review = Review.create(faveId);

        // Assert
        assertNotNull(review);
        assertNotNull(review.getId());
        assertEquals(faveId, review.getFaveId());
        assertTrue(review.isPending());
        assertNotNull(review.getCreatedAt());
        assertNotNull(review.getUpdatedAt());
        assertEquals(review.getCreatedAt(), review.getUpdatedAt());
    }

    @Test
    @DisplayName("DB から審査を再構築できること")
    void reconstruct_shouldReconstructReview() {
        // Arrange
        ReviewId reviewId = new ReviewId(UUID.randomUUID());
        FaveId faveId = new FaveId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();

        // Act
        Review review = Review.reconstruct(
            reviewId,
            faveId,
            APPROVED_STATUS,
            now,
            now
        );

        // Assert
        assertNotNull(review);
        assertEquals(reviewId, review.getId());
        assertEquals(faveId, review.getFaveId());
        assertEquals(APPROVED_STATUS, review.getStatus());
        assertEquals(now, review.getCreatedAt());
        assertEquals(now, review.getUpdatedAt());
    }

    @Test
    @DisplayName("審査を保留状態にできること")
    void pending_shouldSetStatusToPending() {
        // Arrange
        ReviewId reviewId = new ReviewId(UUID.randomUUID());
        FaveId faveId = new FaveId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();
        Review review = Review.reconstruct(reviewId, faveId, APPROVED_STATUS, now, now);
        LocalDateTime beforeUpdate = review.getUpdatedAt();

        // Act
        review.pending();

        // Assert
        assertTrue(review.isPending());
        assertFalse(review.isApproved());
        assertFalse(review.isRejected());
        assertTrue(review.getUpdatedAt().isAfter(beforeUpdate));
    }

    @Test
    @DisplayName("審査を承認できること")
    void approve_shouldSetStatusToApproved() {
        // Arrange
        FaveId faveId = new FaveId(UUID.randomUUID());
        Review review = Review.create(faveId);
        LocalDateTime beforeUpdate = review.getUpdatedAt();

        // Act
        review.approve();

        // Assert
        assertFalse(review.isPending());
        assertTrue(review.isApproved());
        assertFalse(review.isRejected());
        assertTrue(review.getUpdatedAt().isAfter(beforeUpdate));
    }

    @Test
    @DisplayName("審査を却下できること")
    void reject_shouldSetStatusToRejected() {
        // Arrange
        FaveId faveId = new FaveId(UUID.randomUUID());
        Review review = Review.create(faveId);
        LocalDateTime beforeUpdate = review.getUpdatedAt();

        // Act
        review.reject();

        // Assert
        assertFalse(review.isPending());
        assertFalse(review.isApproved());
        assertTrue(review.isRejected());
        assertTrue(review.getUpdatedAt().isAfter(beforeUpdate));
    }

    @Test
    @DisplayName("再構築時にnullのIDを指定するとValueObjectExceptionがスローされること")
    void reconstruct_withNullId_shouldThrowException() {
        // Arrange
        FaveId faveId = new FaveId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();

        // Act & Assert
        assertThrows(ValueObjectException.class, () ->
            Review.reconstruct(null, faveId, PENDING_STATUS, now, now)
        );
    }

    @Test
    @DisplayName("再構築時にnullのFaveIdを指定するとValueObjectExceptionがスローされること")
    void reconstruct_withNullFaveId_shouldThrowException() {
        // Arrange
        ReviewId reviewId = new ReviewId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();

        // Act & Assert
        assertThrows(ValueObjectException.class, () ->
            Review.reconstruct(reviewId, null, PENDING_STATUS, now, now)
        );
    }

    @Test
    @DisplayName("再構築時にnullのステータスを指定するとValueObjectExceptionがスローされること")
    void reconstruct_withNullStatus_shouldThrowException() {
        // Arrange
        ReviewId reviewId = new ReviewId(UUID.randomUUID());
        FaveId faveId = new FaveId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();

        // Act & Assert
        assertThrows(ValueObjectException.class, () ->
            Review.reconstruct(reviewId, faveId, null, now, now)
        );
    }

    @Test
    @DisplayName("再構築時にnullの作成日時を指定するとValueObjectExceptionがスローされること")
    void reconstruct_withNullCreatedAt_shouldThrowException() {
        // Arrange
        ReviewId reviewId = new ReviewId(UUID.randomUUID());
        FaveId faveId = new FaveId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();

        // Act & Assert
        assertThrows(ValueObjectException.class, () ->
            Review.reconstruct(reviewId, faveId, PENDING_STATUS, null, now)
        );
    }

    @Test
    @DisplayName("再構築時にnullの更新日時を指定するとValueObjectExceptionがスローされること")
    void reconstruct_withNullUpdatedAt_shouldThrowException() {
        // Arrange
        ReviewId reviewId = new ReviewId(UUID.randomUUID());
        FaveId faveId = new FaveId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();

        // Act & Assert
        assertThrows(ValueObjectException.class, () ->
            Review.reconstruct(reviewId, faveId, PENDING_STATUS, now, null)
        );
    }

    @Test
    @DisplayName("同じIDを持つ審査は等しいと判定されること")
    void reviewsWithSameId_shouldBeEqual() {
        // Arrange
        ReviewId reviewId = new ReviewId(UUID.randomUUID());
        FaveId faveId = new FaveId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();
        Review review1 = Review.reconstruct(reviewId, faveId, PENDING_STATUS, now, now);
        Review review2 = Review.reconstruct(reviewId, new FaveId(UUID.randomUUID()), APPROVED_STATUS, now, now);

        // Assert
        assertEquals(review1, review2);
        assertEquals(review1.hashCode(), review2.hashCode());
    }

    @Test
    @DisplayName("異なるIDを持つ審査は等しくないこと")
    void reviewsWithDifferentId_shouldNotBeEqual() {
        // Arrange
        ReviewId reviewId = new ReviewId(UUID.randomUUID());
        ReviewId anotherId = new ReviewId(UUID.randomUUID());
        FaveId faveId = new FaveId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();
        Review review1 = Review.reconstruct(reviewId, faveId, PENDING_STATUS, now, now);
        Review review2 = Review.reconstruct(anotherId, faveId, PENDING_STATUS, now, now);

        // Assert
        assertNotEquals(review1, review2);
    }

    @Test
    @DisplayName("toString()で適切な文字列表現が返されること")
    void toString_shouldReturnAppropriateStringRepresentation() {
        // Arrange
        ReviewId reviewId = new ReviewId(UUID.randomUUID());
        FaveId faveId = new FaveId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();
        Review review = Review.reconstruct(reviewId, faveId, PENDING_STATUS, now, now);

        // Act
        String result = review.toString();

        // Assert
        assertTrue(result.contains("Review{"));
        assertTrue(result.contains("id=" + reviewId.value()));
        assertTrue(result.contains("faveId=" + faveId.value()));
        assertTrue(result.contains("status=" + PENDING_STATUS.value()));
    }
}