package com.favick.domain.feature.review.model;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("ReviewStatusValue のテスト")
class ReviewStatusValueTest {
    @Test
    @DisplayName("PENDING（審査待ち）が正しく定義されていること")
    void pending_shouldBeDefined() {
        // Act & Assert
        assertEquals("PENDING", ReviewStatusValue.PENDING.name());
    }

    @Test
    @DisplayName("APPROVED（承認済み）が正しく定義されていること")
    void approved_shouldBeDefined() {
        // Act & Assert
        assertEquals("APPROVED", ReviewStatusValue.APPROVED.name());
    }

    @Test
    @DisplayName("REJECTED（却下）が正しく定義されていること")
    void rejected_shouldBeDefined() {
        // Act & Assert
        assertEquals("REJECTED", ReviewStatusValue.REJECTED.name());
    }

    @Test
    @DisplayName("valueOf()で正しい値を取得できること")
    void valueOf_shouldReturnCorrectValue() {
        // Act & Assert
        assertEquals(ReviewStatusValue.PENDING, ReviewStatusValue.valueOf("PENDING"));
        assertEquals(ReviewStatusValue.APPROVED, ReviewStatusValue.valueOf("APPROVED"));
        assertEquals(ReviewStatusValue.REJECTED, ReviewStatusValue.valueOf("REJECTED"));
    }

    @Test
    @DisplayName("無効な値でvalueOf()を呼ぶとIllegalArgumentExceptionがスローされること")
    void valueOf_withInvalidValue_shouldThrowException() {
        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> ReviewStatusValue.valueOf("INVALID"));
    }

    @Test
    @DisplayName("全ての審査ステータスが3つ定義されていること")
    void values_shouldContainThreeStatuses() {
        // Act
        ReviewStatusValue[] values = ReviewStatusValue.values();

        // Assert
        assertEquals(3, values.length);
        assertTrue(java.util.Arrays.asList(values).contains(ReviewStatusValue.PENDING));
        assertTrue(java.util.Arrays.asList(values).contains(ReviewStatusValue.APPROVED));
        assertTrue(java.util.Arrays.asList(values).contains(ReviewStatusValue.REJECTED));
    }
}