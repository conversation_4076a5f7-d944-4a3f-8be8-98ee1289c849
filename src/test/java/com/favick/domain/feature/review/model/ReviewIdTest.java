package com.favick.domain.feature.review.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("ReviewId のテスト")
class ReviewIdTest {
    @Test
    @DisplayName("ReviewIdの生成が成功すること")
    void generate_shouldCreateValidReviewId() {
        // Act
        ReviewId reviewId = ReviewId.generate();

        // Assert
        assertNotNull(reviewId);
        assertNotNull(reviewId.value());
        assertInstanceOf(UUID.class, reviewId.value());
    }

    @Test
    @DisplayName("nullを指定してReviewIdを生成するとValueObjectExceptionがスローされること")
    void createWithNull_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new ReviewId(null));
    }

    @Test
    @DisplayName("同じUUIDから生成されたReviewIdは等しいと判定されること")
    void reviewIdsWithSameValue_shouldBeEqual() {
        // Arrange
        UUID uuid = UUID.randomUUID();

        // Act
        ReviewId id1 = new ReviewId(uuid);
        ReviewId id2 = new ReviewId(uuid);

        // Assert
        assertEquals(id1, id2);
        assertEquals(id1.hashCode(), id2.hashCode());
    }

    @Test
    @DisplayName("異なるUUIDのReviewIdは等しくないこと")
    void reviewIdsWithDifferentValue_shouldNotBeEqual() {
        // Act
        ReviewId id1 = ReviewId.generate();
        ReviewId id2 = ReviewId.generate();

        // Assert
        assertNotEquals(id1, id2);
    }
}