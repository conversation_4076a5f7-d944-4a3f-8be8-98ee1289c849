package com.favick.domain.feature.review.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("ReviewStatus のテスト")
class ReviewStatusTest {
    @Test
    @DisplayName("有効なReviewStatusValueでReviewStatusが作成できること")
    void createWithValidValue_shouldCreateReviewStatus() {
        // Act
        ReviewStatus status = new ReviewStatus(ReviewStatusValue.PENDING);

        // Assert
        assertNotNull(status);
        assertEquals(ReviewStatusValue.PENDING, status.value());
    }

    @Test
    @DisplayName("nullを指定してReviewStatusを生成するとValueObjectExceptionがスローされること")
    void createWithNull_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new ReviewStatus(null));
    }

    @Test
    @DisplayName("fromString()で有効な文字列からReviewStatusを作成できること")
    void fromString_withValidString_shouldCreateReviewStatus() {
        // Act
        ReviewStatus pendingStatus = ReviewStatus.fromString("PENDING");
        ReviewStatus approvedStatus = ReviewStatus.fromString("APPROVED");
        ReviewStatus rejectedStatus = ReviewStatus.fromString("REJECTED");

        // Assert
        assertEquals(ReviewStatusValue.PENDING, pendingStatus.value());
        assertEquals(ReviewStatusValue.APPROVED, approvedStatus.value());
        assertEquals(ReviewStatusValue.REJECTED, rejectedStatus.value());
    }

    @Test
    @DisplayName("fromString()でnullを指定するとValueObjectExceptionがスローされること")
    void fromString_withNull_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> ReviewStatus.fromString(null));
    }

    @Test
    @DisplayName("fromString()で無効な文字列を指定するとValueObjectExceptionがスローされること")
    void fromString_withInvalidString_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> ReviewStatus.fromString("INVALID"));
    }

    @Test
    @DisplayName("isPending()で審査中かどうかを正しく判定できること")
    void isPending_shouldReturnCorrectResult() {
        // Arrange
        ReviewStatus pendingStatus = new ReviewStatus(ReviewStatusValue.PENDING);
        ReviewStatus approvedStatus = new ReviewStatus(ReviewStatusValue.APPROVED);
        ReviewStatus rejectedStatus = new ReviewStatus(ReviewStatusValue.REJECTED);

        // Act & Assert
        assertTrue(pendingStatus.isPending());
        assertFalse(approvedStatus.isPending());
        assertFalse(rejectedStatus.isPending());
    }

    @Test
    @DisplayName("isApproved()で承認済みかどうかを正しく判定できること")
    void isApproved_shouldReturnCorrectResult() {
        // Arrange
        ReviewStatus pendingStatus = new ReviewStatus(ReviewStatusValue.PENDING);
        ReviewStatus approvedStatus = new ReviewStatus(ReviewStatusValue.APPROVED);
        ReviewStatus rejectedStatus = new ReviewStatus(ReviewStatusValue.REJECTED);

        // Act & Assert
        assertFalse(pendingStatus.isApproved());
        assertTrue(approvedStatus.isApproved());
        assertFalse(rejectedStatus.isApproved());
    }

    @Test
    @DisplayName("isRejected()で却下済みかどうかを正しく判定できること")
    void isRejected_shouldReturnCorrectResult() {
        // Arrange
        ReviewStatus pendingStatus = new ReviewStatus(ReviewStatusValue.PENDING);
        ReviewStatus approvedStatus = new ReviewStatus(ReviewStatusValue.APPROVED);
        ReviewStatus rejectedStatus = new ReviewStatus(ReviewStatusValue.REJECTED);

        // Act & Assert
        assertFalse(pendingStatus.isRejected());
        assertFalse(approvedStatus.isRejected());
        assertTrue(rejectedStatus.isRejected());
    }

    @Test
    @DisplayName("同じReviewStatusValueを持つReviewStatusは等しいと判定されること")
    void reviewStatusesWithSameValue_shouldBeEqual() {
        // Arrange
        ReviewStatus status1 = new ReviewStatus(ReviewStatusValue.PENDING);
        ReviewStatus status2 = new ReviewStatus(ReviewStatusValue.PENDING);

        // Assert
        assertEquals(status1, status2);
        assertEquals(status1.hashCode(), status2.hashCode());
    }

    @Test
    @DisplayName("異なるReviewStatusValueを持つReviewStatusは等しくないこと")
    void reviewStatusesWithDifferentValue_shouldNotBeEqual() {
        // Arrange
        ReviewStatus pendingStatus = new ReviewStatus(ReviewStatusValue.PENDING);
        ReviewStatus approvedStatus = new ReviewStatus(ReviewStatusValue.APPROVED);

        // Assert
        assertNotEquals(pendingStatus, approvedStatus);
    }
}