package com.favick.domain.feature.role.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

@DisplayName("RoleName のテスト")
class RoleNameTest {
    @Test
    @DisplayName("正常系：有効なRoleNameValueを指定した場合、インスタンスが生成される")
    void validRoleNameValue_shouldCreateObject() {
        // Arrange
        RoleNameValue value = RoleNameValue.ROLE_ADMIN;

        // Act
        RoleName roleName = new RoleName(value);

        // Assert
        assertEquals(value, roleName.value());
    }

    @Test
    @DisplayName("異常系：nullを指定した場合、例外が発生する")
    void nullValue_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new RoleName(null));
    }

    @Test
    @DisplayName("正常系：fromStringで有効な文字列を指定した場合、対応するRoleNameを取得できる")
    void validString_shouldReturnRoleName() {
        // Act
        RoleName roleName = RoleName.fromString("ROLE_ADMIN");

        // Assert
        assertEquals(RoleNameValue.ROLE_ADMIN, roleName.value());
    }

    @Test
    @DisplayName("異常系：fromStringでnullを指定した場合、例外が発生する")
    void nullString_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> RoleName.fromString(null));
    }

    @Test
    @DisplayName("異常系：fromStringで無効な文字列を指定した場合、例外が発生する")
    void invalidString_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> RoleName.fromString("INVALID_ROLE"));
    }
}
