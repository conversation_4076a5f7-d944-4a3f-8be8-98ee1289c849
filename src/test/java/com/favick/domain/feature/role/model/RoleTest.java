package com.favick.domain.feature.role.model;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("Role のテスト")
class RoleTest {
    @Test
    @DisplayName("正常系：コンストラクタで全ての値を設定できる")
    void constructor_shouldSetAllValues() {
        // Arrange
        RoleId id = RoleId.generate();
        RoleName name = new RoleName(RoleNameValue.ROLE_ADMIN);
        LocalDateTime createdAt = LocalDateTime.now().minusDays(1);
        LocalDateTime updatedAt = LocalDateTime.now();

        // Act
        Role role = Role.reconstruct(
            id,
            name,
            createdAt,
            updatedAt
        );

        // Assert
        assertEquals(id, role.getId());
        assertEquals(name, role.getName());
        assertEquals(createdAt, role.getCreatedAt());
        assertEquals(updatedAt, role.getUpdatedAt());
    }

    @Test
    @DisplayName("正常系：名前のみ指定のコンストラクタでIDと時間が自動生成される")
    void constructorWithNameOnly_shouldGenerateIdAndTimes() {
        // Arrange
        RoleName name = new RoleName(RoleNameValue.ROLE_ADMIN);

        // Act
        Role role = Role.create(name);

        // Assert
        assertNotNull(role.getId());
        assertNotNull(role.getId().value());
        assertEquals(name, role.getName());
        assertNotNull(role.getCreatedAt());
        assertNotNull(role.getUpdatedAt());
        // 作成日時と更新日時は初期状態では同じ
        assertEquals(role.getCreatedAt(), role.getUpdatedAt());
    }

    @Test
    @DisplayName("正常系：updateメソッドで名前と更新日時が更新される")
    void update_shouldUpdateNameAndUpdateTime() {
        // Arrange
        Role role = Role.create(new RoleName(RoleNameValue.ROLE_ADMIN));
        LocalDateTime beforeUpdate = role.getUpdatedAt();
        RoleName newName = new RoleName(RoleNameValue.ROLE_ADMIN); // 同じ値でも更新日時は変わる

        // 少し時間をおいて更新
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            fail("Thread sleep interrupted");
        }

        // Act
        role.update(newName);

        // Assert
        assertEquals(newName, role.getName());
        assertTrue(role.getUpdatedAt().isAfter(beforeUpdate), "更新日時が更新されていること");
    }

    @Test
    @DisplayName("等価性テスト：同じIDで生成したRoleは等しい")
    void sameId_shouldBeEqual() {
        // Arrange
        RoleId id = RoleId.generate();
        RoleName name1 = new RoleName(RoleNameValue.ROLE_ADMIN);
        RoleName name2 = new RoleName(RoleNameValue.ROLE_ADMIN);
        LocalDateTime now = LocalDateTime.now();

        // Act
        Role role1 = Role.reconstruct(
            id,
            name1,
            now,
            now
        );
        Role role2 = Role.reconstruct(
            id,
            name2,
            now.plusHours(1),
            now.plusHours(2)
        );

        // Assert
        assertEquals(role1, role2);
        assertEquals(role1.hashCode(), role2.hashCode());
    }

    @Test
    @DisplayName("非等価性テスト：異なるIDで生成したRoleは等しくない")
    void differentId_shouldNotBeEqual() {
        // Arrange
        RoleName name = new RoleName(RoleNameValue.ROLE_ADMIN);
        LocalDateTime now = LocalDateTime.now();

        // Act
        Role role1 = Role.reconstruct(
            RoleId.generate(),
            name,
            now,
            now
        );
        Role role2 = Role.reconstruct(
            RoleId.generate(),
            name,
            now,
            now
        );

        // Assert
        assertNotEquals(role1, role2);
    }

    @Test
    @DisplayName("toString()テスト：IDと名前を含む文字列を返す")
    void toString_shouldContainIdAndName() {
        // Arrange
        RoleId id = RoleId.generate();
        RoleName name = new RoleName(RoleNameValue.ROLE_ADMIN);
        LocalDateTime now = LocalDateTime.now();
        Role role = Role.reconstruct(
            id,
            name,
            now,
            now
        );

        // Act
        String result = role.toString();

        // Assert
        assertTrue(result.contains("Role{"));
        assertTrue(result.contains("id=" + id.value()));
        assertTrue(result.contains("name=" + name.value()));
    }
}
