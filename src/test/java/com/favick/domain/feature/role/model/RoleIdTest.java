package com.favick.domain.feature.role.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("RoleId のテスト")
class RoleIdTest {
    @Test
    @DisplayName("正常系：generate()で有効なRoleIdを生成できる")
    void generate_shouldCreateValidRoleId() {
        // Act
        RoleId roleId = RoleId.generate();

        // Assert
        assertNotNull(roleId);
        assertNotNull(roleId.value());
        assertInstanceOf(UUID.class, roleId.value());
    }

    @Test
    @DisplayName("異常系：nullを指定した場合、例外が発生する")
    void nullValue_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new RoleId(null));
    }

    @Test
    @DisplayName("等価性テスト：同じUUIDで生成したRoleIdは等しい")
    void sameUuid_shouldBeEqual() {
        // Arrange
        UUID uuid = UUID.randomUUID();

        // Act
        RoleId id1 = new RoleId(uuid);
        RoleId id2 = new RoleId(uuid);

        // Assert
        assertEquals(id1, id2);
        assertEquals(id1.hashCode(), id2.hashCode());
    }

    @Test
    @DisplayName("非等価性テスト：異なるUUIDで生成したRoleIdは等しくない")
    void differentUuid_shouldNotBeEqual() {
        // Act
        RoleId id1 = RoleId.generate();
        RoleId id2 = RoleId.generate();

        // Assert
        assertNotEquals(id1, id2);
    }
}
