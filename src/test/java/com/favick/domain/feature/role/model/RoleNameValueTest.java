package com.favick.domain.feature.role.model;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("RoleNameValue のテスト")
class RoleNameValueTest {
    @Test
    @DisplayName("ROLE_ADMIN（管理者権限）が正しく定義されていること")
    void roleAdmin_shouldBeDefined() {
        // Act & Assert
        assertEquals("ROLE_ADMIN", RoleNameValue.ROLE_ADMIN.name());
    }

    @Test
    @DisplayName("valueOf()で正しい値を取得できること")
    void valueOf_shouldReturnCorrectValue() {
        // Act & Assert
        assertEquals(RoleNameValue.ROLE_ADMIN, RoleNameValue.valueOf("ROLE_ADMIN"));
    }

    @Test
    @DisplayName("無効な値でvalueOf()を呼ぶとIllegalArgumentExceptionがスローされること")
    void valueOf_withInvalidValue_shouldThrowException() {
        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> RoleNameValue.valueOf("INVALID"));
    }

    @Test
    @DisplayName("全ての権限名が1つ定義されていること")
    void values_shouldContainOneRole() {
        // Act
        RoleNameValue[] values = RoleNameValue.values();

        // Assert
        assertEquals(1, values.length);
        assertTrue(java.util.Arrays.asList(values).contains(RoleNameValue.ROLE_ADMIN));
    }
}