package com.favick.domain.feature.language.model;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("LanguageCodeValue のテスト")
class LanguageCodeValueTest {
    @Test
    @DisplayName("JA（日本語）が正しく定義されていること")
    void ja_shouldBeDefined() {
        // Act & Assert
        assertEquals("JA", LanguageCodeValue.JA.name());
    }

    @Test
    @DisplayName("EN（英語）が正しく定義されていること")
    void en_shouldBeDefined() {
        // Act & Assert
        assertEquals("EN", LanguageCodeValue.EN.name());
    }

    @Test
    @DisplayName("valueOf()で正しい値を取得できること")
    void valueOf_shouldReturnCorrectValue() {
        // Act & Assert
        assertEquals(LanguageCodeValue.JA, LanguageCodeValue.valueOf("JA"));
        assertEquals(LanguageCodeValue.EN, LanguageCodeValue.valueOf("EN"));
    }

    @Test
    @DisplayName("無効な値でvalueOf()を呼ぶとIllegalArgumentExceptionがスローされること")
    void valueOf_withInvalidValue_shouldThrowException() {
        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> LanguageCodeValue.valueOf("INVALID"));
    }

    @Test
    @DisplayName("全ての言語コードが2つ定義されていること")
    void values_shouldContainTwoLanguages() {
        // Act
        LanguageCodeValue[] values = LanguageCodeValue.values();

        // Assert
        assertEquals(2, values.length);
        assertTrue(java.util.Arrays.asList(values).contains(LanguageCodeValue.JA));
        assertTrue(java.util.Arrays.asList(values).contains(LanguageCodeValue.EN));
    }
}