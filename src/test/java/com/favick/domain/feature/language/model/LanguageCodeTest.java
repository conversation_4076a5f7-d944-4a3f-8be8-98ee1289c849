package com.favick.domain.feature.language.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("LanguageCode のテスト")
class LanguageCodeTest {
    @Test
    @DisplayName("有効なLanguageCodeValueでLanguageCodeが作成できること")
    void createWithValidValue_shouldCreateLanguageCode() {
        // Act
        LanguageCode code = new LanguageCode(LanguageCodeValue.JA);

        // Assert
        assertNotNull(code);
        assertEquals(LanguageCodeValue.JA, code.value());
    }

    @Test
    @DisplayName("nullを指定してLanguageCodeを生成するとValueObjectExceptionがスローされること")
    void createWithNull_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new LanguageCode(null));
    }

    @Test
    @DisplayName("fromString()で有効な文字列からLanguageCodeを作成できること")
    void fromString_withValidString_shouldCreateLanguageCode() {
        // Act
        LanguageCode jaCode = LanguageCode.fromString("JA");
        LanguageCode enCode = LanguageCode.fromString("EN");

        // Assert
        assertEquals(LanguageCodeValue.JA, jaCode.value());
        assertEquals(LanguageCodeValue.EN, enCode.value());
    }

    @Test
    @DisplayName("fromString()でnullを指定するとValueObjectExceptionがスローされること")
    void fromString_withNull_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> LanguageCode.fromString(null));
    }

    @Test
    @DisplayName("fromString()で無効な文字列を指定するとValueObjectExceptionがスローされること")
    void fromString_withInvalidString_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> LanguageCode.fromString("INVALID"));
    }

    @Test
    @DisplayName("getFieldSuffix()で正しいサフィックスが取得できること")
    void getFieldSuffix_shouldReturnCorrectSuffix() {
        // Arrange
        LanguageCode jaCode = new LanguageCode(LanguageCodeValue.JA);
        LanguageCode enCode = new LanguageCode(LanguageCodeValue.EN);

        // Act & Assert
        assertEquals("Ja", jaCode.getFieldSuffix());
        assertEquals("En", enCode.getFieldSuffix());
    }

    @Test
    @DisplayName("同じLanguageCodeValueを持つLanguageCodeは等しいと判定されること")
    void languageCodesWithSameValue_shouldBeEqual() {
        // Arrange
        LanguageCode code1 = new LanguageCode(LanguageCodeValue.JA);
        LanguageCode code2 = new LanguageCode(LanguageCodeValue.JA);

        // Assert
        assertEquals(code1, code2);
        assertEquals(code1.hashCode(), code2.hashCode());
    }

    @Test
    @DisplayName("異なるLanguageCodeValueを持つLanguageCodeは等しくないこと")
    void languageCodesWithDifferentValue_shouldNotBeEqual() {
        // Arrange
        LanguageCode jaCode = new LanguageCode(LanguageCodeValue.JA);
        LanguageCode enCode = new LanguageCode(LanguageCodeValue.EN);

        // Assert
        assertNotEquals(jaCode, enCode);
    }
}