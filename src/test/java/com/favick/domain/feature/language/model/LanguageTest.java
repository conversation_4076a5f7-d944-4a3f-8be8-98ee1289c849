package com.favick.domain.feature.language.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("Language のテスト")
class LanguageTest {
    private static final LanguageCode JA_CODE = new LanguageCode(LanguageCodeValue.JA);
    private static final LanguageCode EN_CODE = new LanguageCode(LanguageCodeValue.EN);
    private static final LanguageDisplayName JA_DISPLAY_NAME = new LanguageDisplayName("日本語");
    private static final LanguageDisplayName EN_DISPLAY_NAME = new LanguageDisplayName("English");

    @Test
    @DisplayName("新しい言語を作成できること")
    void create_shouldCreateNewLanguage() {
        // Act
        Language language = Language.create(JA_CODE, JA_DISPLAY_NAME);

        // Assert
        assertNotNull(language);
        assertNotNull(language.getId());
        assertEquals(JA_CODE, language.getCode());
        assertEquals(JA_DISPLAY_NAME, language.getDisplayName());
        assertNotNull(language.getCreatedAt());
        assertNotNull(language.getUpdatedAt());
        assertEquals(language.getCreatedAt(), language.getUpdatedAt());
    }

    @Test
    @DisplayName("DB から言語を再構築できること")
    void reconstruct_shouldReconstructLanguage() {
        // Arrange
        LanguageId languageId = new LanguageId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();

        // Act
        Language language = Language.reconstruct(
            languageId,
            JA_CODE,
            JA_DISPLAY_NAME,
            now,
            now
        );

        // Assert
        assertNotNull(language);
        assertEquals(languageId, language.getId());
        assertEquals(JA_CODE, language.getCode());
        assertEquals(JA_DISPLAY_NAME, language.getDisplayName());
        assertEquals(now, language.getCreatedAt());
        assertEquals(now, language.getUpdatedAt());
    }

    @Test
    @DisplayName("言語情報を更新できること")
    void update_shouldUpdateLanguageInfo() {
        // Arrange
        LanguageId languageId = new LanguageId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();
        Language language = Language.reconstruct(languageId, JA_CODE, JA_DISPLAY_NAME, now, now);
        LocalDateTime beforeUpdate = language.getUpdatedAt();

        // Act
        language.update(EN_CODE, EN_DISPLAY_NAME);

        // Assert
        assertEquals(EN_CODE, language.getCode());
        assertEquals(EN_DISPLAY_NAME, language.getDisplayName());
        assertTrue(language.getUpdatedAt().isAfter(beforeUpdate));
    }

    @Test
    @DisplayName("再構築時にnullのIDを指定するとValueObjectExceptionがスローされること")
    void reconstruct_withNullId_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () ->
            Language.reconstruct(null, JA_CODE, JA_DISPLAY_NAME, LocalDateTime.now(), LocalDateTime.now())
        );
    }

    @Test
    @DisplayName("再構築時にnullのコードを指定するとValueObjectExceptionがスローされること")
    void reconstruct_withNullCode_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () ->
            Language.reconstruct(new LanguageId(UUID.randomUUID()), null, JA_DISPLAY_NAME, LocalDateTime.now(), LocalDateTime.now())
        );
    }

    @Test
    @DisplayName("再構築時にnullの表示名を指定するとValueObjectExceptionがスローされること")
    void reconstruct_withNullDisplayName_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () ->
            Language.reconstruct(new LanguageId(UUID.randomUUID()), JA_CODE, null, LocalDateTime.now(), LocalDateTime.now())
        );
    }

    @Test
    @DisplayName("再構築時にnullの作成日時を指定するとValueObjectExceptionがスローされること")
    void reconstruct_withNullCreatedAt_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () ->
            Language.reconstruct(new LanguageId(UUID.randomUUID()), JA_CODE, JA_DISPLAY_NAME, null, LocalDateTime.now())
        );
    }

    @Test
    @DisplayName("再構築時にnullの更新日時を指定するとValueObjectExceptionがスローされること")
    void reconstruct_withNullUpdatedAt_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () ->
            Language.reconstruct(new LanguageId(UUID.randomUUID()), JA_CODE, JA_DISPLAY_NAME, LocalDateTime.now(), null)
        );
    }

    @Test
    @DisplayName("更新時にnullのコードを指定するとValueObjectExceptionがスローされること")
    void update_withNullCode_shouldThrowException() {
        // Arrange
        Language language = Language.create(JA_CODE, JA_DISPLAY_NAME);

        // Act & Assert
        assertThrows(ValueObjectException.class, () ->
            language.update(null, EN_DISPLAY_NAME)
        );
    }

    @Test
    @DisplayName("更新時にnullの表示名を指定するとValueObjectExceptionがスローされること")
    void update_withNullDisplayName_shouldThrowException() {
        // Arrange
        Language language = Language.create(JA_CODE, JA_DISPLAY_NAME);

        // Act & Assert
        assertThrows(ValueObjectException.class, () ->
            language.update(EN_CODE, null)
        );
    }

    @Test
    @DisplayName("同じIDを持つ言語は等しいと判定されること")
    void languagesWithSameId_shouldBeEqual() {
        // Arrange
        LanguageId languageId = new LanguageId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();
        Language language1 = Language.reconstruct(languageId, JA_CODE, JA_DISPLAY_NAME, now, now);
        Language language2 = Language.reconstruct(languageId, EN_CODE, EN_DISPLAY_NAME, now, now);

        // Assert
        assertEquals(language1, language2);
        assertEquals(language1.hashCode(), language2.hashCode());
    }

    @Test
    @DisplayName("異なるIDを持つ言語は等しくないこと")
    void languagesWithDifferentId_shouldNotBeEqual() {
        // Arrange
        LanguageId languageId = new LanguageId(UUID.randomUUID());
        LanguageId anotherId = new LanguageId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();
        Language language1 = Language.reconstruct(languageId, JA_CODE, JA_DISPLAY_NAME, now, now);
        Language language2 = Language.reconstruct(anotherId, JA_CODE, JA_DISPLAY_NAME, now, now);

        // Assert
        assertNotEquals(language1, language2);
    }

    @Test
    @DisplayName("toString()で適切な文字列表現が返されること")
    void toString_shouldReturnAppropriateStringRepresentation() {
        // Arrange
        LanguageId languageId = new LanguageId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();
        Language language = Language.reconstruct(languageId, JA_CODE, JA_DISPLAY_NAME, now, now);

        // Act
        String result = language.toString();

        // Assert
        assertTrue(result.contains("Language{"));
        assertTrue(result.contains("id='" + languageId.value() + "'"));
        assertTrue(result.contains("code=" + JA_CODE.value()));
        assertTrue(result.contains("displayName=" + JA_DISPLAY_NAME.value()));
    }
}