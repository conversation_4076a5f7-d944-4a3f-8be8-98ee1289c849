package com.favick.domain.feature.language.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("LanguageDisplayName のテスト")
class LanguageDisplayNameTest {
    @Test
    @DisplayName("有効な表示名でLanguageDisplayNameが作成できること")
    void createWithValidDisplayName_shouldCreateLanguageDisplayName() {
        // Act
        LanguageDisplayName displayName = new LanguageDisplayName("日本語");

        // Assert
        assertNotNull(displayName);
        assertEquals("日本語", displayName.value());
    }

    @Test
    @DisplayName("最大長の表示名でLanguageDisplayNameが作成できること")
    void createWithMaxLengthDisplayName_shouldCreateLanguageDisplayName() {
        // Arrange
        String maxLengthName = "a".repeat(50);

        // Act
        LanguageDisplayName displayName = new LanguageDisplayName(maxLengthName);

        // Assert
        assertNotNull(displayName);
        assertEquals(maxLengthName, displayName.value());
    }

    @Test
    @DisplayName("nullを指定してLanguageDisplayNameを生成するとValueObjectExceptionがスローされること")
    void createWithNull_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new LanguageDisplayName(null));
    }

    @Test
    @DisplayName("空文字を指定してLanguageDisplayNameを生成するとValueObjectExceptionがスローされること")
    void createWithEmptyString_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new LanguageDisplayName(""));
    }

    @Test
    @DisplayName("空白文字のみを指定してLanguageDisplayNameを生成するとValueObjectExceptionがスローされること")
    void createWithWhitespaceOnly_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new LanguageDisplayName("   "));
    }

    @Test
    @DisplayName("最大長を超える表示名でLanguageDisplayNameを生成するとValueObjectExceptionがスローされること")
    void createWithTooLongDisplayName_shouldThrowException() {
        // Arrange
        String tooLongName = "a".repeat(51);

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new LanguageDisplayName(tooLongName));
    }

    @Test
    @DisplayName("同じ表示名を持つLanguageDisplayNameは等しいと判定されること")
    void languageDisplayNamesWithSameValue_shouldBeEqual() {
        // Arrange
        LanguageDisplayName name1 = new LanguageDisplayName("日本語");
        LanguageDisplayName name2 = new LanguageDisplayName("日本語");

        // Assert
        assertEquals(name1, name2);
        assertEquals(name1.hashCode(), name2.hashCode());
    }

    @Test
    @DisplayName("異なる表示名を持つLanguageDisplayNameは等しくないこと")
    void languageDisplayNamesWithDifferentValue_shouldNotBeEqual() {
        // Arrange
        LanguageDisplayName jaName = new LanguageDisplayName("日本語");
        LanguageDisplayName enName = new LanguageDisplayName("English");

        // Assert
        assertNotEquals(jaName, enName);
    }
}