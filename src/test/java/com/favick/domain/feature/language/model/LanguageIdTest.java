package com.favick.domain.feature.language.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("LanguageId のテスト")
class LanguageIdTest {
    @Test
    @DisplayName("LanguageIdの生成が成功すること")
    void generate_shouldCreateValidLanguageId() {
        // Act
        LanguageId languageId = LanguageId.generate();

        // Assert
        assertNotNull(languageId);
        assertNotNull(languageId.value());
        assertInstanceOf(UUID.class, languageId.value());
    }

    @Test
    @DisplayName("nullを指定してLanguageIdを生成するとValueObjectExceptionがスローされること")
    void createWithNull_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new LanguageId(null));
    }

    @Test
    @DisplayName("同じUUIDから生成されたLanguageIdは等しいと判定されること")
    void languageIdsWithSameValue_shouldBeEqual() {
        // Arrange
        UUID uuid = UUID.randomUUID();

        // Act
        LanguageId id1 = new LanguageId(uuid);
        LanguageId id2 = new LanguageId(uuid);

        // Assert
        assertEquals(id1, id2);
        assertEquals(id1.hashCode(), id2.hashCode());
    }

    @Test
    @DisplayName("異なるUUIDのLanguageIdは等しくないこと")
    void languageIdsWithDifferentValue_shouldNotBeEqual() {
        // Act
        LanguageId id1 = LanguageId.generate();
        LanguageId id2 = LanguageId.generate();

        // Assert
        assertNotEquals(id1, id2);
    }
}