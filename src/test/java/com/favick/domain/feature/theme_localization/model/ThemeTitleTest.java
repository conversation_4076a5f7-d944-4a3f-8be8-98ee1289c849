package com.favick.domain.feature.theme_localization.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("ThemeTitle のテスト")
class ThemeTitleTest {
    @Test
    @DisplayName("正常系：50文字以内のタイトルを指定した場合、インスタンスが生成される")
    void validTitle_shouldCreateObject() {
        // Arrange
        String value = "春のテーマ";

        // Act
        ThemeTitle title = new ThemeTitle(value);

        // Assert
        assertEquals(value, title.value());
    }

    @Test
    @DisplayName("異常系：nullを指定した場合、例外が発生する")
    void nullTitle_shouldThrowException() {
        assertThrows(ValueObjectException.class, () -> new ThemeTitle(null));
    }

    @Test
    @DisplayName("異常系：空文字を指定した場合、例外が発生する")
    void emptyTitle_shouldThrowException() {
        // Arrange
        String value = "";

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new ThemeTitle(value));
    }

    @Test
    @DisplayName("異常系：51文字以上のタイトルを指定した場合、例外が発生する")
    void tooLongTitle_shouldThrowException() {
        // Arrange
        String tooLong = "あ".repeat(51);

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new ThemeTitle(tooLong));
    }

    @Test
    @DisplayName("正常系：50文字のタイトルは許可される")
    void maxLengthTitle_shouldBeAccepted() {
        // Arrange
        String maxLength = "あ".repeat(50);

        // Act & Assert
        assertDoesNotThrow(() -> new ThemeTitle(maxLength));
    }

    @Test
    @DisplayName("正常系：1文字のタイトルは許可される")
    void minLengthTitle_shouldBeAccepted() {
        // Arrange
        String minLength = "あ";

        // Act & Assert
        assertDoesNotThrow(() -> new ThemeTitle(minLength));
    }

    @Test
    @DisplayName("異常系：空白のみのタイトルは例外が発生する")
    void whitespaceOnlyTitle_shouldThrowException() {
        // Arrange
        String whitespace = "   ";

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new ThemeTitle(whitespace));
    }
}
