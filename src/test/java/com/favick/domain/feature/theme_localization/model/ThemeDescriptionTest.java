package com.favick.domain.feature.theme_localization.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("ThemeDescription のテスト")
class ThemeDescriptionTest {
    @Test
    @DisplayName("正常系：説明が空でない場合、インスタンスが生成される")
    void validDescription_shouldCreateObject() {
        // Arrange
        String value = "春のテーマについての説明";

        // Act
        ThemeDescription description = new ThemeDescription(value);

        // Assert
        assertEquals(value, description.value());
    }

    @Test
    @DisplayName("正常系：255文字の説明は許可される")
    void maxLengthDescription_shouldBeAccepted() {
        String valid = "あ".repeat(255);
        assertDoesNotThrow(() -> new ThemeDescription(valid));
    }

    @Test
    @DisplayName("異常系：説明がnullの場合、例外が発生する")
    void nullDescription_shouldThrowException() {
        // Arrange
        String value = null;

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new ThemeDescription(value));
    }

    @Test
    @DisplayName("異常系：説明が空文字の場合、例外が発生する")
    void emptyDescription_shouldThrowException() {
        // Arrange
        String empty = "";

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new ThemeDescription(empty));
    }

    @Test
    @DisplayName("異常系：256文字の説明は例外が発生する")
    void tooLongDescription_shouldThrowException() {
        String tooLong = "あ".repeat(256);
        assertThrows(ValueObjectException.class, () -> new ThemeDescription(tooLong));
    }

    @Test
    @DisplayName("正常系：1文字の説明は許可される")
    void minLengthDescription_shouldBeAccepted() {
        // Arrange
        String minLength = "あ";

        // Act & Assert
        assertDoesNotThrow(() -> new ThemeDescription(minLength));
    }

    @Test
    @DisplayName("異常系：空白のみの説明は例外が発生する")
    void whitespaceOnlyDescription_shouldThrowException() {
        // Arrange
        String whitespace = "   ";

        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new ThemeDescription(whitespace));
    }
}
