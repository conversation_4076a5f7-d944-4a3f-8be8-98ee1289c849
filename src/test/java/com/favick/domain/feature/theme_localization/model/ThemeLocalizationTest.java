package com.favick.domain.feature.theme_localization.model;

import com.favick.domain.exception.ValueObjectException;
import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import com.favick.domain.feature.theme.model.ThemeId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("ThemeLocalization のテスト")
class ThemeLocalizationTest {
    private static final LanguageCode JA_LANGUAGE_CODE = new LanguageCode(LanguageCodeValue.JA);
    private static final ThemeTitle JA_TITLE = new ThemeTitle("日本語タイトル");
    private static final ThemeTitle EN_TITLE = new ThemeTitle("English Title");
    private static final ThemeDescription JA_DESCRIPTION = new ThemeDescription("日本語の説明");
    private static final ThemeDescription EN_DESCRIPTION = new ThemeDescription("English description");

    @Test
    @DisplayName("新しいテーマローカライゼーションを作成できること")
    void create_shouldCreateNewThemeLocalization() {
        // Arrange
        ThemeId themeId = new ThemeId(UUID.randomUUID());

        // Act
        ThemeLocalization localization = ThemeLocalization.create(
            themeId,
            JA_LANGUAGE_CODE,
            JA_TITLE,
            JA_DESCRIPTION
        );

        // Assert
        assertNotNull(localization);
        assertNotNull(localization.getId());
        assertEquals(themeId, localization.getThemeId());
        assertEquals(JA_LANGUAGE_CODE, localization.getLanguageCode());
        assertEquals(JA_TITLE, localization.getTitle());
        assertEquals(JA_DESCRIPTION, localization.getDescription());
        assertNotNull(localization.getCreatedAt());
        assertNotNull(localization.getUpdatedAt());
        assertEquals(localization.getCreatedAt(), localization.getUpdatedAt());
    }

    @Test
    @DisplayName("DB からテーマローカライゼーションを再構築できること")
    void reconstruct_shouldReconstructThemeLocalization() {
        // Arrange
        ThemeLocalizationId localizationId = new ThemeLocalizationId(UUID.randomUUID());
        ThemeId themeId = new ThemeId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();

        // Act
        ThemeLocalization localization = ThemeLocalization.reconstruct(
            localizationId,
            themeId,
            JA_LANGUAGE_CODE,
            JA_TITLE,
            JA_DESCRIPTION,
            now,
            now
        );

        // Assert
        assertNotNull(localization);
        assertEquals(localizationId, localization.getId());
        assertEquals(themeId, localization.getThemeId());
        assertEquals(JA_LANGUAGE_CODE, localization.getLanguageCode());
        assertEquals(JA_TITLE, localization.getTitle());
        assertEquals(JA_DESCRIPTION, localization.getDescription());
        assertEquals(now, localization.getCreatedAt());
        assertEquals(now, localization.getUpdatedAt());
    }

    @Test
    @DisplayName("テーマローカライゼーション情報を更新できること")
    void update_shouldUpdateThemeLocalizationInfo() {
        // Arrange
        ThemeLocalizationId localizationId = new ThemeLocalizationId(UUID.randomUUID());
        ThemeId themeId = new ThemeId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();
        ThemeLocalization localization = ThemeLocalization.reconstruct(
            localizationId, themeId, JA_LANGUAGE_CODE, JA_TITLE, JA_DESCRIPTION, now, now
        );
        LocalDateTime beforeUpdate = localization.getUpdatedAt();

        // Act
        localization.update(EN_TITLE, EN_DESCRIPTION);

        // Assert
        assertEquals(EN_TITLE, localization.getTitle());
        assertEquals(EN_DESCRIPTION, localization.getDescription());
        assertTrue(localization.getUpdatedAt().isAfter(beforeUpdate));
    }

    @Test
    @DisplayName("再構築時にnullのIDを指定するとValueObjectExceptionがスローされること")
    void reconstruct_withNullId_shouldThrowException() {
        // Arrange
        ThemeId themeId = new ThemeId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();

        // Act & Assert
        assertThrows(ValueObjectException.class, () ->
            ThemeLocalization.reconstruct(null, themeId, JA_LANGUAGE_CODE, JA_TITLE, JA_DESCRIPTION, now, now)
        );
    }

    @Test
    @DisplayName("再構築時にnullのテーマIDを指定するとValueObjectExceptionがスローされること")
    void reconstruct_withNullThemeId_shouldThrowException() {
        // Arrange
        ThemeLocalizationId localizationId = new ThemeLocalizationId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();

        // Act & Assert
        assertThrows(ValueObjectException.class, () ->
            ThemeLocalization.reconstruct(localizationId, null, JA_LANGUAGE_CODE, JA_TITLE, JA_DESCRIPTION, now, now)
        );
    }

    @Test
    @DisplayName("再構築時にnullの言語コードを指定するとValueObjectExceptionがスローされること")
    void reconstruct_withNullLanguageCode_shouldThrowException() {
        // Arrange
        ThemeLocalizationId localizationId = new ThemeLocalizationId(UUID.randomUUID());
        ThemeId themeId = new ThemeId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();

        // Act & Assert
        assertThrows(ValueObjectException.class, () ->
            ThemeLocalization.reconstruct(localizationId, themeId, null, JA_TITLE, JA_DESCRIPTION, now, now)
        );
    }

    @Test
    @DisplayName("再構築時にnullのタイトルを指定するとValueObjectExceptionがスローされること")
    void reconstruct_withNullTitle_shouldThrowException() {
        // Arrange
        ThemeLocalizationId localizationId = new ThemeLocalizationId(UUID.randomUUID());
        ThemeId themeId = new ThemeId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();

        // Act & Assert
        assertThrows(ValueObjectException.class, () ->
            ThemeLocalization.reconstruct(localizationId, themeId, JA_LANGUAGE_CODE, null, JA_DESCRIPTION, now, now)
        );
    }

    @Test
    @DisplayName("再構築時にnullの説明を指定するとValueObjectExceptionがスローされること")
    void reconstruct_withNullDescription_shouldThrowException() {
        // Arrange
        ThemeLocalizationId localizationId = new ThemeLocalizationId(UUID.randomUUID());
        ThemeId themeId = new ThemeId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();

        // Act & Assert
        assertThrows(ValueObjectException.class, () ->
            ThemeLocalization.reconstruct(localizationId, themeId, JA_LANGUAGE_CODE, JA_TITLE, null, now, now)
        );
    }

    @Test
    @DisplayName("再構築時にnullの作成日時を指定するとValueObjectExceptionがスローされること")
    void reconstruct_withNullCreatedAt_shouldThrowException() {
        // Arrange
        ThemeLocalizationId localizationId = new ThemeLocalizationId(UUID.randomUUID());
        ThemeId themeId = new ThemeId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();

        // Act & Assert
        assertThrows(ValueObjectException.class, () ->
            ThemeLocalization.reconstruct(localizationId, themeId, JA_LANGUAGE_CODE, JA_TITLE, JA_DESCRIPTION, null, now)
        );
    }

    @Test
    @DisplayName("再構築時にnullの更新日時を指定するとValueObjectExceptionがスローされること")
    void reconstruct_withNullUpdatedAt_shouldThrowException() {
        // Arrange
        ThemeLocalizationId localizationId = new ThemeLocalizationId(UUID.randomUUID());
        ThemeId themeId = new ThemeId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();

        // Act & Assert
        assertThrows(ValueObjectException.class, () ->
            ThemeLocalization.reconstruct(localizationId, themeId, JA_LANGUAGE_CODE, JA_TITLE, JA_DESCRIPTION, now, null)
        );
    }

    @Test
    @DisplayName("更新時にnullのタイトルを指定するとValueObjectExceptionがスローされること")
    void update_withNullTitle_shouldThrowException() {
        // Arrange
        ThemeLocalization localization = ThemeLocalization.create(new ThemeId(UUID.randomUUID()), JA_LANGUAGE_CODE, JA_TITLE, JA_DESCRIPTION);

        // Act & Assert
        assertThrows(ValueObjectException.class, () ->
            localization.update(null, EN_DESCRIPTION)
        );
    }

    @Test
    @DisplayName("更新時にnullの説明を指定するとValueObjectExceptionがスローされること")
    void update_withNullDescription_shouldThrowException() {
        // Arrange
        ThemeLocalization localization = ThemeLocalization.create(new ThemeId(UUID.randomUUID()), JA_LANGUAGE_CODE, JA_TITLE, JA_DESCRIPTION);

        // Act & Assert
        assertThrows(ValueObjectException.class, () ->
            localization.update(EN_TITLE, null)
        );
    }

    @Test
    @DisplayName("同じID、テーマID、言語コードを持つテーマローカライゼーションは等しいと判定されること")
    void themeLocalizationsWithSameIdAndThemeIdAndLanguageCode_shouldBeEqual() {
        // Arrange
        ThemeLocalizationId localizationId = new ThemeLocalizationId(UUID.randomUUID());
        ThemeId themeId = new ThemeId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();
        ThemeLocalization localization1 = ThemeLocalization.reconstruct(
            localizationId, themeId, JA_LANGUAGE_CODE, JA_TITLE, JA_DESCRIPTION, now, now
        );
        ThemeLocalization localization2 = ThemeLocalization.reconstruct(
            localizationId, themeId, JA_LANGUAGE_CODE, EN_TITLE, EN_DESCRIPTION, now, now
        );

        // Assert
        assertEquals(localization1, localization2);
        assertEquals(localization1.hashCode(), localization2.hashCode());
    }

    @Test
    @DisplayName("異なるIDを持つテーマローカライゼーションは等しくないこと")
    void themeLocalizationsWithDifferentId_shouldNotBeEqual() {
        // Arrange
        ThemeLocalizationId localizationId = new ThemeLocalizationId(UUID.randomUUID());
        ThemeLocalizationId anotherId = new ThemeLocalizationId(UUID.randomUUID());
        ThemeId themeId = new ThemeId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();
        ThemeLocalization localization1 = ThemeLocalization.reconstruct(
            localizationId, themeId, JA_LANGUAGE_CODE, JA_TITLE, JA_DESCRIPTION, now, now
        );
        ThemeLocalization localization2 = ThemeLocalization.reconstruct(
            anotherId, themeId, JA_LANGUAGE_CODE, JA_TITLE, JA_DESCRIPTION, now, now
        );

        // Assert
        assertNotEquals(localization1, localization2);
    }

    @Test
    @DisplayName("toString()で適切な文字列表現が返されること")
    void toString_shouldReturnAppropriateStringRepresentation() {
        // Arrange
        ThemeLocalizationId localizationId = new ThemeLocalizationId(UUID.randomUUID());
        ThemeId themeId = new ThemeId(UUID.randomUUID());
        LocalDateTime now = LocalDateTime.now();
        ThemeLocalization localization = ThemeLocalization.reconstruct(
            localizationId, themeId, JA_LANGUAGE_CODE, JA_TITLE, JA_DESCRIPTION, now, now
        );

        // Act
        String result = localization.toString();

        // Assert
        assertTrue(result.contains("ThemeLocalization{"));
        assertTrue(result.contains("id='" + localizationId.value() + "'"));
        assertTrue(result.contains("themeId=" + themeId.value()));
        assertTrue(result.contains("languageCode=" + JA_LANGUAGE_CODE.value()));
        assertTrue(result.contains("title=" + JA_TITLE.value()));
        assertTrue(result.contains("description=" + JA_DESCRIPTION.value()));
    }
}
