package com.favick.domain.feature.theme_localization.model;

import com.favick.domain.exception.ValueObjectException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("ThemeLocalizationId のテスト")
class ThemeLocalizationIdTest {
    @Test
    @DisplayName("ThemeLocalizationIdの生成が成功すること")
    void generate_shouldCreateValidThemeLocalizationId() {
        // Act
        ThemeLocalizationId themeLocalizationId = ThemeLocalizationId.generate();

        // Assert
        assertNotNull(themeLocalizationId);
        assertNotNull(themeLocalizationId.value());
        assertInstanceOf(UUID.class, themeLocalizationId.value());
    }

    @Test
    @DisplayName("nullを指定してThemeLocalizationIdを生成するとValueObjectExceptionがスローされること")
    void createWithNull_shouldThrowException() {
        // Act & Assert
        assertThrows(ValueObjectException.class, () -> new ThemeLocalizationId(null));
    }

    @Test
    @DisplayName("同じUUIDから生成されたThemeLocalizationIdは等しいと判定されること")
    void themeLocalizationIdsWithSameValue_shouldBeEqual() {
        // Arrange
        UUID uuid = UUID.randomUUID();

        // Act
        ThemeLocalizationId id1 = new ThemeLocalizationId(uuid);
        ThemeLocalizationId id2 = new ThemeLocalizationId(uuid);

        // Assert
        assertEquals(id1, id2);
        assertEquals(id1.hashCode(), id2.hashCode());
    }

    @Test
    @DisplayName("異なるUUIDのThemeLocalizationIdは等しくないこと")
    void themeLocalizationIdsWithDifferentValue_shouldNotBeEqual() {
        // Act
        ThemeLocalizationId id1 = ThemeLocalizationId.generate();
        ThemeLocalizationId id2 = ThemeLocalizationId.generate();

        // Assert
        assertNotEquals(id1, id2);
    }
}
