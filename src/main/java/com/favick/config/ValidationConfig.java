package com.favick.config;

import com.favick.common.validation.CustomMessageInterpolator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

@Configuration
public class ValidationConfig {
    /**
     * Spring統合バリデーターを提供するビーン定義
     * 依存注入をサポートするLocalValidatorFactoryBeanを使用
     *
     * @param customMessageInterpolator カスタムメッセージインタポレーター
     * @return バリデーター
     */
    @Bean
    public LocalValidatorFactoryBean validator(CustomMessageInterpolator customMessageInterpolator) {
        LocalValidatorFactoryBean factory = new LocalValidatorFactoryBean();
        factory.setMessageInterpolator(customMessageInterpolator);
        return factory;
    }
}
