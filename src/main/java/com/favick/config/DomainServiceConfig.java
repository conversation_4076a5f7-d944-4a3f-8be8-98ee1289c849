package com.favick.config;

import com.favick.domain.feature.theme.service.ThemeDomainService;
import com.favick.domain.feature.user.service.UserDomainService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * ドメインサービスの設定クラス
 * ドメインレイヤーをSpring依存から分離するために、
 * ドメインサービスのビーンをここで定義する
 */
@Configuration
public class DomainServiceConfig {
    /**
     * テーマドメインサービスのビーン定義
     *
     * @return テーマドメインサービス
     */
    @Bean
    public ThemeDomainService themeDomainService() {
        return new ThemeDomainService();
    }

    /**
     * ユーザードメインサービスのビーン定義
     *
     * @return ユーザードメインサービス
     */
    @Bean
    public UserDomainService userDomainService() {
        return new UserDomainService();
    }
}
