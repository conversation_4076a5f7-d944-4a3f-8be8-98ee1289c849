package com.favick.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.i18n.CookieLocaleResolver;
import org.springframework.web.servlet.i18n.LocaleChangeInterceptor;

import java.time.Duration;
import java.util.Locale;

/**
 * Web MVCの設定
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    /**
     * ロケールリゾルバを設定するビーン定義
     *
     * @return ロケールリゾルバ
     */
    @Bean
    public LocaleResolver localeResolver(Environment environment) {
        CookieLocaleResolver resolver = new CookieLocaleResolver("localeCookie");
        resolver.setDefaultLocale(Locale.JAPAN);
        resolver.setCookieMaxAge(Duration.ofDays(365));
        resolver.setCookiePath("/");

        if (environment.acceptsProfiles(Profiles.of("production"))) {
            resolver.setCookieSecure(true);
        }

        return resolver;
    }

    /**
     * ロケール変更インターセプターを設定するビーン定義
     *
     * @return ロケール変更インターセプター
     */
    @Bean
    public LocaleChangeInterceptor localeChangeInterceptor() {
        LocaleChangeInterceptor interceptor = new LocaleChangeInterceptor();
        interceptor.setParamName("lang");
        return interceptor;
    }

    @Override
    public void addInterceptors(@NonNull InterceptorRegistry registry) {
        registry.addInterceptor(localeChangeInterceptor());
    }
}
