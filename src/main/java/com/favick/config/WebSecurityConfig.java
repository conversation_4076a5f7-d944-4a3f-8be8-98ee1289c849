package com.favick.config;

import com.favick.adapter.in.security.entrypoint.HtmxAuthenticationEntryPoint;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.servlet.util.matcher.PathPatternRequestMatcher;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity
@RequiredArgsConstructor
public class WebSecurityConfig {
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * 管理者用のセキュリティフィルタチェーンを構築
     *
     * @param http                    HttpSecurityオブジェクト
     * @param adminDetailsServiceImpl 管理者用のUserDetailsService
     * @return SecurityFilterChainオブジェクト
     * @throws Exception 例外
     */
    @Bean
    @Order(1)
    public SecurityFilterChain adminFilterChain(
        HttpSecurity http,
        @Qualifier("adminDetailsServiceImpl") UserDetailsService adminDetailsServiceImpl
    ) throws Exception {
        http
            .securityMatcher("/console/**", "/auth/admin/**", "/admin/monitor/**")
            .authorizeHttpRequests((requests) -> requests
                .requestMatchers("/auth/admin/login").permitAll()
                .anyRequest().hasRole("ADMIN")
            )
            .formLogin((form) -> form
                .loginPage("/auth/admin/login")
                .loginProcessingUrl("/auth/admin/login")
                .defaultSuccessUrl("/console?login", true)
                .failureUrl("/auth/admin/login?error")
                .permitAll()
            )
            .logout((logout) -> logout
                .logoutRequestMatcher(PathPatternRequestMatcher.withDefaults().matcher("/auth/admin/logout"))
                .logoutSuccessUrl("/auth/admin/login?logout")
                .deleteCookies("JSESSIONID")
                .invalidateHttpSession(true)
                .clearAuthentication(true)
                .permitAll()
            )
            .userDetailsService(adminDetailsServiceImpl);

        return http.build();
    }

    /**
     * ユーザー用のセキュリティフィルタチェーンを構築
     *
     * @param http                   HttpSecurityオブジェクト
     * @param userDetailsServiceImpl ユーザー用のUserDetailsService
     * @return SecurityFilterChainオブジェクト
     * @throws Exception 例外
     */
    @Bean
    @Order(2)
    public SecurityFilterChain userFilterChain(
        HttpSecurity http,
        @Qualifier("userDetailsServiceImpl") UserDetailsService userDetailsServiceImpl,
        HtmxAuthenticationEntryPoint htmxAuthenticationEntryPoint
    ) throws Exception {
        http
            .authorizeHttpRequests((requests) -> requests
                // 静的リソースは全員アクセス可能
                .requestMatchers("/css/**", "/js/**", "/img/**", "/storage/**", "/animation/**", "/favicon.ico").permitAll()
                // 認証関連ページは全員アクセス可能
                .requestMatchers("/auth/**").permitAll()
                // メインページは全員アクセス可能（閲覧のみ）
                .requestMatchers("/", "/themes", "/themes/**", "/auth/**").permitAll()
                // 管理者ページへのアクセスを拒否
                .requestMatchers("/console/**").denyAll()
                // ログインが必要なページ
                .requestMatchers("/account/**", "/pick", "/like").authenticated()
                // その他のリクエストは認証が必要
                .anyRequest().authenticated()
            )
            .formLogin((form) -> form
                .loginPage("/auth/login")
                .loginProcessingUrl("/auth/login")
                .defaultSuccessUrl("/account?login", true)
                .failureUrl("/auth/login?error")
                .permitAll()
            )
            .logout((logout) -> logout
                .logoutRequestMatcher(PathPatternRequestMatcher.withDefaults().matcher("/auth/logout"))
                .logoutSuccessUrl("/?logout")
                .deleteCookies("JSESSIONID")
                .invalidateHttpSession(true)
                .clearAuthentication(true)
                .permitAll()
            )
            .exceptionHandling(exceptions -> exceptions
                .authenticationEntryPoint(htmxAuthenticationEntryPoint)
            )
            .headers(headers -> headers
                .contentSecurityPolicy(csp -> csp.policyDirectives("default-src 'self'; script-src 'self' 'wasm-unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self' https://unpkg.com; frame-src 'self'; frame-ancestors 'none'; object-src 'none'; form-action 'self'; base-uri 'none';"))
            )
            .userDetailsService(userDetailsServiceImpl);

        return http.build();
    }
}
