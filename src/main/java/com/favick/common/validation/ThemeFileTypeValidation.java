package com.favick.common.validation;

import com.favick.application.port.out.feature.theme.repository.ThemeRepository;
import com.favick.domain.feature.theme.model.Theme;
import com.favick.domain.feature.theme.model.ThemeTypeValue;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;
import java.util.Set;

/**
 * テーマ種別を検証するバリデータ
 */
@RequiredArgsConstructor
@Slf4j
public class ThemeFileTypeValidation implements ConstraintValidator<ThemeFileType, MultipartFile> {
    /**
     * テーマタイプごとの許可されたMIMEタイプ
     */
    private static final Map<ThemeTypeValue, Set<String>> ALLOWED_MIME_TYPES = Map.of(
        ThemeTypeValue.PHOTO, Set.of(
            "image/jpeg",
            "image/jpg",
            "image/png",
            "image/gif",
            "image/webp"
        ),
        ThemeTypeValue.MOVIE, Set.of(
            "video/mp4",
            "video/quicktime",
            "video/x-msvideo",
            "video/webm"
        ),
        ThemeTypeValue.AUDIO, Set.of(
            "audio/mpeg",
            "audio/mp3",
            "audio/wav",
            "audio/aac",
            "audio/ogg"
        )
    );
    private final ThemeRepository themeRepository;

    @Override
    public boolean isValid(MultipartFile file, ConstraintValidatorContext context) {
        if (file == null || file.isEmpty()) {
            return true; // @NotEmptyFileで検証されるため、ここでは許可
        }

        try {
            // 現在開催中のテーマを取得
            Theme currentTheme = themeRepository.findCurrentTheme()
                .orElse(null);

            if (currentTheme == null) {
                log.warn("No current theme found. Skipping file type validation.");
                return true; // 安全側: テーマが見つからない場合は許可
            }

            ThemeTypeValue themeType = currentTheme.getType().value();
            String contentType = file.getContentType();

            if (contentType == null || contentType.trim().isEmpty()) {
                log.warn("Content type is null or empty for file: {}", file.getOriginalFilename());
                return false; // MIME typeが不明な場合は拒否
            }

            // MIME typeの正規化（小文字化）
            String normalizedContentType = contentType.toLowerCase().trim();

            // テーマタイプに対応するMIMEタイプかチェック
            Set<String> allowedTypes = ALLOWED_MIME_TYPES.get(themeType);
            boolean isValid = allowedTypes != null && allowedTypes.contains(normalizedContentType);

            if (!isValid) {
                log.info("Invalid file type. Theme type: {}, File content type: {}, File name: {}",
                    themeType, normalizedContentType, file.getOriginalFilename());
            }

            return isValid;

        } catch (Exception e) {
            log.error("Failed to validate file type for file: {}", file.getOriginalFilename(), e);
            return true; // エラー時は安全側: 許可
        }
    }
}
