package com.favick.common.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * テーマ種別を検証するアノテーション
 */
@Documented
@Constraint(validatedBy = ThemeFileTypeValidation.class)
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ThemeFileType {
    String message() default "{validation.invalid.file_type}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
