package com.favick.common.validation;

import com.favick.application.port.out.feature.theme.repository.ThemeRepository;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.theme.model.ThemeStartDate;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.LocalDate;
import java.util.Objects;
import java.util.UUID;

/**
 * 日付が重複していないことを検証するバリデータ
 */
@RequiredArgsConstructor
@Slf4j
public class UniqueDateValidator implements ConstraintValidator<UniqueDate, LocalDate> {
    private final ThemeRepository themeRepository;

    @Override
    public boolean isValid(LocalDate startDate, ConstraintValidatorContext context) {
        if (startDate == null) {
            return true;
        }

        try {
            ThemeStartDate themeStartDate = new ThemeStartDate(startDate);

            // リクエストからIDを取得
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = Objects.requireNonNull(attributes).getRequest();
            String themeIdStr = request.getParameter("id");

            // 更新の場合、自分自身を除外
            if (themeIdStr != null && !themeIdStr.isEmpty()) {
                try {
                    ThemeId themeId = new ThemeId(UUID.fromString(themeIdStr));
                    return !themeRepository.existsByStartDateExcludingId(themeStartDate, themeId);
                } catch (Exception e) {
                    log.error("Failed to validate unique date", e);
                    return true;
                }
            }

            // 新規作成の場合
            return !themeRepository.existsByStartDate(themeStartDate);
        } catch (Exception e) {
            log.error("Failed to validate unique date", e);
            return true;
        }
    }
}
