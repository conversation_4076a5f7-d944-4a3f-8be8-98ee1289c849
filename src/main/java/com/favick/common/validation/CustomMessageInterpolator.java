package com.favick.common.validation;

import jakarta.validation.MessageInterpolator;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import java.util.Locale;

@Component
@RequiredArgsConstructor
public class CustomMessageInterpolator implements MessageInterpolator {
    private final MessageSource messageSource;

    @Override
    public String interpolate(String messageTemplate, Context context) {
        return interpolate(messageTemplate, context, LocaleContextHolder.getLocale());
    }

    @Override
    public String interpolate(String messageTemplate, Context context, Locale locale) {
        final String resolvedMessage = resolveMessage(messageTemplate, locale);

        // @Size アノテーションの処理
        if (context.getConstraintDescriptor().getAnnotation() instanceof Size size) {
            if (size.min() > 0) {
                return resolvedMessage.replace("{0}", String.valueOf(size.min()));
            }
            if (size.max() < Integer.MAX_VALUE) {
                return resolvedMessage.replace("{0}", String.valueOf(size.max()));
            }
        }

        return resolvedMessage;
    }

    /**
     * メッセージキーを解決する
     *
     * @param messageTemplate メッセージテンプレート
     * @param locale          ロケール
     * @return 解決されたメッセージ
     */
    private String resolveMessage(String messageTemplate, Locale locale) {
        if (messageTemplate.startsWith("{") && messageTemplate.endsWith("}")) {
            String messageKey = messageTemplate.substring(1, messageTemplate.length() - 1);
            try {
                return messageSource.getMessage(messageKey, null, locale);
            } catch (Exception e) {
                return messageTemplate;
            }
        }

        return messageTemplate;
    }
}
