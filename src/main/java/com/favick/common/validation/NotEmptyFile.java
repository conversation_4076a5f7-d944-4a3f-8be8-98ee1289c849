package com.favick.common.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * ファイルが空でないことを検証するアノテーション
 */
@Documented
@Constraint(validatedBy = NotEmptyFileValidator.class)
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface NotEmptyFile {
    String message() default "{validation.required.file}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
