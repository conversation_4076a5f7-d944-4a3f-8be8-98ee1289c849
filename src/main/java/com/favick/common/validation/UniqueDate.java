package com.favick.common.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * 登録済みの日付と重複しないことを検証するアノテーション
 */
@Documented
@Constraint(validatedBy = UniqueDateValidator.class)
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface UniqueDate {
    String message() default "{validation.must_be.unique.date}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
