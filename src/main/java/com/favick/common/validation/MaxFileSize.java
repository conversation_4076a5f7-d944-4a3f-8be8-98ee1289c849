package com.favick.common.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * ファイルサイズが最大値以下であることを検証するアノテーション
 */
@Documented
@Constraint(validatedBy = MaxFileSizeValidator.class)
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface MaxFileSize {
    String message() default "{validation.too_large.file_size}";

    long value() default 1048576; // 1MB

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
