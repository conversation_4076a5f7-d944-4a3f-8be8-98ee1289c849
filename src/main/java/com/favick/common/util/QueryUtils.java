package com.favick.common.util;

import jakarta.servlet.http.HttpServletRequest;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * クエリ文字列のユーティリティクラス
 */
public class QueryUtils {
    /**
     * クエリ文字列を生成する
     *
     * @param request       HTTPリクエスト
     * @param excludeParams 除外するパラメータ名
     * @return クエリ文字列
     */
    public static String toQueryString(HttpServletRequest request, String... excludeParams) {
        Set<String> excludeSet = new HashSet<>(Arrays.asList(excludeParams));

        String queryString = request.getParameterMap().entrySet().stream()
            .filter(entry -> !excludeSet.contains(entry.getKey()))
            .flatMap(entry -> {
                String key = entry.getKey();
                String[] values = entry.getValue();
                return Arrays.stream(values).map(value -> key + "=" + value);
            })
            .collect(Collectors.joining("&"));

        return queryString.isEmpty() ? "" : queryString;
    }
}
