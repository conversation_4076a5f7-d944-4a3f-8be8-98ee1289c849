package com.favick.common.util;

/**
 * セキュリティログ用のユーティリティクラス
 */
public final class SecurityLogUtil {
    private SecurityLogUtil() {
        // ユーティリティクラスのため、インスタンス化を禁止
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * メールアドレスをマスキングする
     *
     * <p>例: "<EMAIL>" → "u***@example.com"</p>
     *
     * @param email マスキング対象のメールアドレス
     * @return マスキングされたメールアドレス。nullまたは無効な形式の場合は "***"
     */
    public static String maskEmail(String email) {
        if (email == null || email.length() < 3) {
            return "***";
        }

        int atIndex = email.indexOf('@');
        if (atIndex <= 0) {
            return "***";
        }

        return email.charAt(0) + "***" + email.substring(atIndex);
    }

    /**
     * 電話番号をマスキングする
     *
     * <p>例: "090-1234-5678" → "090-****-5678"</p>
     *
     * @param phoneNumber マスキング対象の電話番号
     * @return マスキングされた電話番号
     */
    public static String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() < 6) {
            return "***";
        }

        // 数字のみを抽出
        String numbersOnly = phoneNumber.replaceAll("[^0-9]", "");

        if (numbersOnly.length() < 6) {
            return "***";
        }

        if (numbersOnly.length() >= 10) {
            // 10桁以上の場合
            return numbersOnly.substring(0, 3) + "-****-" + numbersOnly.substring(numbersOnly.length() - 4);
        } else {
            // 10桁未満の場合
            return numbersOnly.substring(0, 2) + "***" + numbersOnly.substring(numbersOnly.length() - 2);
        }
    }

    /**
     * クレジットカード番号をマスキングする
     *
     * <p>例: "1234567890123456" → "1234-****-****-3456"</p>
     *
     * @param cardNumber マスキング対象のクレジットカード番号
     * @return マスキングされたクレジットカード番号
     */
    public static String maskCardNumber(String cardNumber) {
        if (cardNumber == null || cardNumber.length() < 8) {
            return "***";
        }

        // ハイフンやスペースを除去
        String cleanNumber = cardNumber.replaceAll("[\\s-]", "");

        if (cleanNumber.length() < 8) {
            return "***";
        }

        if (cleanNumber.length() >= 16) {
            // 16桁の場合（一般的なクレジットカード）
            return cleanNumber.substring(0, 4) + "-****-****-" + cleanNumber.substring(12);
        } else {
            // その他の場合
            return cleanNumber.substring(0, 4) + "***" + cleanNumber.substring(cleanNumber.length() - 4);
        }
    }

    /**
     * トークンをマスキングする
     *
     * <p>例: "abcdef123456" → "abcd...3456"</p>
     *
     * @param token マスキング対象のトークン
     * @return マスキングされたトークン。nullまたは8文字未満の場合は "***"
     */
    public static String maskToken(String token) {
        if (token == null || token.length() < 8) {
            return "***";
        }

        return token.substring(0, 4) + "..." + token.substring(token.length() - 4);
    }

    /**
     * ユーザーIDをマスキングする
     *
     * <p>例: "12345678-1234-1234-1234-123456789012" → "12345678-****-****-****-123456789012"</p>
     *
     * @param userId マスキング対象のユーザーID（UUID形式）
     * @return マスキングされたユーザーID。nullまたは無効な形式の場合は "***"
     */
    public static String maskUserId(String userId) {
        if (userId == null || userId.length() < 8) {
            return "***";
        }

        // UUID形式の場合（xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx）
        if (userId.length() == 36 && userId.charAt(8) == '-' && userId.charAt(13) == '-') {
            return userId.substring(0, 8) + "-****-****-****-" + userId.substring(24);
        }

        // その他の形式の場合
        if (userId.length() >= 12) {
            return userId.substring(0, 4) + "***" + userId.substring(userId.length() - 4);
        }

        // 8文字以上12文字未満の場合
        return userId.substring(0, 2) + "***" + userId.substring(userId.length() - 2);
    }

    /**
     * IPアドレスをマスキングする
     *
     * <p>例: "*************" → "192.168.***.***"</p>
     *
     * @param ipAddress マスキング対象のIPアドレス
     * @return マスキングされたIPアドレス。nullまたは無効な形式の場合は "***"
     */
    public static String maskIpAddress(String ipAddress) {
        if (ipAddress == null || ipAddress.isEmpty()) {
            return "***";
        }

        // IPv4の場合
        if (ipAddress.contains(".")) {
            String[] parts = ipAddress.split("\\.");
            if (parts.length == 4) {
                return parts[0] + "." + parts[1] + ".***";
            }
        }

        // IPv6の場合（簡易実装）
        if (ipAddress.contains(":")) {
            String[] parts = ipAddress.split(":");
            if (parts.length >= 4) {
                return parts[0] + ":" + parts[1] + ":***:***";
            }
        }

        return "***";
    }
}
