package com.favick.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.UUID;

/**
 * ファイル操作のユーティリティクラス
 */
@Slf4j
public class FileUtils {

    /**
     * ファイル名をUUID付きで生成する
     *
     * @param fileName 元のファイル名
     * @return 生成された新しいファイル名
     */
    private static String generateNewFileName(String fileName) {
        String uuid = UUID.randomUUID().toString();

        if (fileName == null || fileName.isBlank()) {
            return uuid + ".jpg"; // デフォルトの拡張子をつける
        }

        int dotIndex = fileName.lastIndexOf(".");

        return (dotIndex == -1) ? uuid + ".jpg" : uuid + fileName.substring(dotIndex);
    }

    /**
     * 画像ファイルの保存処理
     *
     * @param imageFile アップロードされた画像ファイル
     * @param directory 保存先ディレクトリ名
     * @return 保存されたファイル名（nullの場合、画像は保存されなかった）
     */
    public static String saveImage(MultipartFile imageFile, String directory) {
        if (imageFile == null || imageFile.isEmpty()) {
            return null; // 空のファイルなら null を返す
        }

        try {
            String newFileName = generateNewFileName(imageFile.getOriginalFilename());
            Path filePath = Path.of("src/main/resources/static/storage", directory, newFileName);
            Files.createDirectories(filePath.getParent());
            Files.copy(imageFile.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);

            return newFileName;
        } catch (IOException e) {
            log.error("Failed to save image file", e);
            throw new RuntimeException("画像の保存に失敗しました", e);
        }
    }
}
