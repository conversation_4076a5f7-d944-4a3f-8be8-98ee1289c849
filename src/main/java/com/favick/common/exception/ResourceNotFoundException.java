package com.favick.common.exception;

import lombok.Getter;

/**
 * リソースが見つからない場合の例外
 */
@Getter
public class ResourceNotFoundException extends RuntimeException {
    private final String messageKey;
    private final Object context;

    public ResourceNotFoundException(String messageKey, Object context) {
        super("ResourceNotFound");
        this.messageKey = messageKey;
        this.context = context;
    }
}
