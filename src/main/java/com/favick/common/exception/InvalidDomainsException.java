package com.favick.common.exception;

import com.favick.domain.exception.DomainException;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 複数のドメインオブジェクトの不正の例外
 */
@Getter
public class InvalidDomainsException extends RuntimeException {
    private final List<DomainException> exceptions;

    public InvalidDomainsException(List<DomainException> exceptions) {
        super(exceptions.size() + " Domain Errors");
        this.exceptions = new ArrayList<>(exceptions);
    }
}
