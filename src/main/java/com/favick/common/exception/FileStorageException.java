package com.favick.common.exception;

import lombok.Getter;

/**
 * ファイルストレージ操作に失敗した場合の例外
 */
@Getter
public class FileStorageException extends RuntimeException {
    private final String messageKey;
    private final Object context;

    public FileStorageException(String messageKey, Object context, Exception cause) {
        super("FileStorageError", cause);
        this.messageKey = messageKey;
        this.context = context;
    }
}
