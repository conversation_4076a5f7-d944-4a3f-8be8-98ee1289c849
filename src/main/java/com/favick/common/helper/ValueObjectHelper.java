package com.favick.common.helper;

import com.favick.domain.exception.DomainException;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;

/**
 * 値オブジェクトを支援するユーティリティクラス
 */
@Slf4j
public class ValueObjectHelper {
    /**
     * 値オブジェクトを検証し、結果をOptionalで返す
     *
     * @param valueObject 検証対象の値オブジェクト
     * @return 検証結果を含むOptional
     */
    public static <T> Optional<T> validate(Supplier<T> valueObject) {
        try {
            return Optional.ofNullable(valueObject.get());
        } catch (DomainException e) {
            log.error("Value object validation failed: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    /**
     * 値オブジェクトを検証し、結果をOptionalで返す
     * 例外が発生した場合は、エラーリストに追加する
     *
     * @param valueObject 検証対象の値オブジェクト
     * @param errors      エラーリスト
     * @return 検証結果を含むOptional
     */
    public static <T> Optional<T> validate(
        Supplier<T> valueObject,
        List<DomainException> errors
    ) {
        try {
            return Optional.ofNullable(valueObject.get());
        } catch (DomainException e) {
            log.error("Value object validation failed: {}", e.getMessage(), e);
            errors.add(e);
            return Optional.empty();
        }
    }

    /**
     * ドメインサービスを実行し、例外が発生した場合はエラーリストに追加する
     *
     * @param domainService 実行するドメインサービス
     * @param errors        エラーリスト
     */
    public static void validate(
        Runnable domainService,
        List<DomainException> errors
    ) {
        try {
            domainService.run();
        } catch (DomainException e) {
            log.error("Domain service failed: {}", e.getMessage(), e);
            errors.add(e);
        }
    }
}
