package com.favick.common.helper;

import com.favick.common.type.MessageType;
import lombok.RequiredArgsConstructor;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

/**
 * メッセージヘルパー
 */
@Component
@RequiredArgsConstructor
public class MessageHelper {
    private final MessageSource messageSource;
    private final LocaleHelper localeHelper;

    /**
     * メッセージを取得する
     *
     * @param key メッセージキー
     * @return メッセージ
     */
    public String getMessage(String key) {
        return messageSource.getMessage(key, null, localeHelper.getCurrentLocale());
    }

    /**
     * メッセージを取得する
     *
     * @param key  メッセージキー
     * @param args メッセージ引数
     * @return メッセージ
     */
    public String getMessage(String key, Object[] args) {
        return messageSource.getMessage(key, args, localeHelper.getCurrentLocale());
    }

    /**
     * メッセージを設定する
     *
     * @param redirectAttributes リダイレクト属性
     * @param type               メッセージタイプ
     * @param key                メッセージキー
     * @param args               メッセージ引数
     */
    public void setMessage(RedirectAttributes redirectAttributes, MessageType type, String key, Object... args) {
        redirectAttributes.addFlashAttribute(type.getAttributeName(), getMessage(key, args));
    }
}
