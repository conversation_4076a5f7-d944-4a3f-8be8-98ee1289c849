package com.favick.common.helper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import java.util.Locale;

@Component
@Slf4j
public class LocaleHelper {
    /**
     * 現在のロケールを取得する
     * Webリクエストコンテキストから現在のロケールを取得し、
     * 取得できない場合はデフォルトロケールを返す
     *
     * @return 現在のロケール
     */
    public Locale getCurrentLocale() {
        try {
            return LocaleContextHolder.getLocale();
        } catch (Exception e) {
            log.error("Failed to get current locale", e);
            return getDefaultLocale();
        }
    }

    /**
     * デフォルトロケールを取得する
     *
     * @return デフォルトロケール
     */
    public Locale getDefaultLocale() {
        return Locale.JAPAN;
    }

    /**
     * 現在のロケールが日本語かどうかを判定する
     *
     * @return 日本語の場合true
     */
    public boolean isJapanese() {
        return isJapanese(getCurrentLocale());
    }

    /**
     * 指定されたロケールが日本語かどうかを判定する
     *
     * @param locale 判定対象のロケール
     * @return 日本語の場合true
     */
    public boolean isJapanese(Locale locale) {
        return locale != null && Locale.JAPAN.getLanguage().equals(locale.getLanguage());
    }

    /**
     * 現在のロケールが英語かどうかを判定する
     *
     * @return 英語の場合true
     */
    public boolean isEnglish() {
        return isEnglish(getCurrentLocale());
    }

    /**
     * 指定されたロケールが英語かどうかを判定する
     *
     * @param locale 判定対象のロケール
     * @return 英語の場合true
     */
    public boolean isEnglish(Locale locale) {
        return locale != null && Locale.ENGLISH.getLanguage().equals(locale.getLanguage());
    }

    /**
     * ロケールの表示名を取得する
     *
     * @param locale 対象のロケール
     * @return ロケールの表示名
     */
    public String getDisplayName(Locale locale) {
        if (locale == null) {
            return "Unknown";
        }
        return locale.getDisplayName(locale);
    }
}
