package com.favick.common.helper;

import com.favick.domain.exception.DomainException;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;

/**
 * エンティティを支援するユーティリティクラス
 */
@Slf4j
public class EntityHelper {
    /**
     * エンティティを検証し、結果をOptionalで返す
     * 例外が発生した場合は、エラーリストに追加する
     *
     * @param entity 検証対象のエンティティ
     * @param errors エラーリスト
     * @return 検証結果を含むOptional
     */
    public static <T> Optional<T> validate(
        Supplier<T> entity,
        List<DomainException> errors
    ) {
        try {
            return Optional.ofNullable(entity.get());
        } catch (DomainException e) {
            log.error("Entity validation error: messageKey={}", e.getMessageKey(), e);
            errors.add(e);
            return Optional.empty();
        }
    }

    /**
     * エンティティの操作を実行し、例外が発生した場合はエラーリストに追加する
     *
     * @param operation 実行する操作
     * @param errors    エラーリスト
     */
    public static void validate(
        Runnable operation,
        List<DomainException> errors
    ) {
        try {
            operation.run();
        } catch (DomainException e) {
            log.error("Entity operation error: messageKey={}", e.getMessageKey(), e);
            errors.add(e);
        }
    }
}
