package com.favick.application.port.in;

import com.favick.application.dto.query.GetCurrentFaveQueryDto;
import com.favick.application.dto.result.CurrentThemePickedFaveResultDto;

/**
 * 開催中のテーマとお気に入りを取得するユースケースのポート
 */
public interface GetCurrentThemePickedFaveUseCase {
    /**
     * 開催中のテーマとお気に入りを取得する
     *
     * @param query お気に入り取得クエリDTO
     * @return お気に入り詳細リザルトDTO
     */
    CurrentThemePickedFaveResultDto handle(GetCurrentFaveQueryDto query);
}
