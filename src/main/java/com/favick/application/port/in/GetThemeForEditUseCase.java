package com.favick.application.port.in;

import com.favick.application.dto.query.GetThemeForEditQueryDto;
import com.favick.application.dto.result.ThemeEditResultDto;

/**
 * テーマを編集用に取得するユースケースのポート
 */
public interface GetThemeForEditUseCase {
    /**
     * テーマを編集用に取得する
     *
     * @param query テーマ取得クエリDTO
     * @return テーマ編集用詳細リザルトDTO
     */
    ThemeEditResultDto handle(GetThemeForEditQueryDto query);
}
