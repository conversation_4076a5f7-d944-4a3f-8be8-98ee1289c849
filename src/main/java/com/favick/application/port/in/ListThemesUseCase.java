package com.favick.application.port.in;

import com.favick.application.dto.query.ListThemesQueryDto;
import com.favick.application.dto.result.ThemeSummaryResultDto;

import java.util.List;

/**
 * テーマ一覧を取得するユースケースのポート
 */
public interface ListThemesUseCase {
    /**
     * テーマ一覧を取得する
     *
     * @param query テーマ取得クエリDTO
     * @return テーマ一覧サマリーリザルトDTO
     */
    List<ThemeSummaryResultDto> handle(ListThemesQueryDto query);
}
