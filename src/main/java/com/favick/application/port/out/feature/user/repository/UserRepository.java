package com.favick.application.port.out.feature.user.repository;

import com.favick.domain.feature.user.model.User;
import com.favick.domain.feature.user.model.UserEmail;
import com.favick.domain.feature.user.model.UserId;

import java.util.Optional;

/**
 * ユーザーリポジトリのポート
 * ユーザーの永続化を担当する
 */
public interface UserRepository {
    /**
     * ユーザーを保存する
     *
     * @param user 保存するユーザー
     */
    void save(User user);

    /**
     * ユーザーIDが一致するユーザーを取得する
     *
     * @param id 検索対象のユーザーID
     * @return ユーザーIDが一致するユーザー
     */
    Optional<User> findById(UserId id);

    /**
     * メールアドレスが一致するユーザーを取得する
     *
     * @param email 検索対象のメールアドレス
     * @return メールアドレスが一致するユーザー
     */
    Optional<User> findByEmail(UserEmail email);

    /**
     * メールアドレスが一致するユーザーが存在するかを判定する
     *
     * @param email 検索対象のメールアドレス
     * @return メールアドレスが一致するユーザーが存在する場合true、存在しない場合false
     */
    boolean existsByEmail(UserEmail email);
}
