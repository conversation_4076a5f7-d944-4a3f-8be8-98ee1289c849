package com.favick.application.port.out.feature.theme_localization.repository;

import com.favick.domain.feature.theme_localization.model.ThemeLocalization;

/**
 * テーマローカライゼーションリポジトリのポート
 * テーマローカライゼーションの永続化を担当する
 */
public interface ThemeLocalizationRepository {
    /**
     * テーマローカライゼーションを保存する
     *
     * @param themeLocalization 保存するテーマローカライゼーション
     */
    void save(ThemeLocalization themeLocalization);
}
