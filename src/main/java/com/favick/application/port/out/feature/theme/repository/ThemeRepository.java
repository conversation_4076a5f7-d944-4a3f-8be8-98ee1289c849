package com.favick.application.port.out.feature.theme.repository;

import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.theme.model.Theme;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.theme.model.ThemeStartDate;
import com.favick.domain.feature.theme_localization.model.ThemeLocalization;
import org.springframework.data.util.Pair;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * テーマリポジトリのポート
 * テーマの永続化を担当する
 */
public interface ThemeRepository {
    /**
     * 言語コードで統合済みのテーマ一覧を取得する
     *
     * @param languageCode 言語コード
     * @return 統合済みのテーマ一覧
     */
    List<Pair<Theme, ThemeLocalization>> findAllIntegratedByLanguageCode(LanguageCode languageCode);

    /**
     * 言語コードで現在開催中の統合済みのテーマを取得する
     *
     * @param languageCode 言語コード
     * @return 現在開催中の統合済みのテーマ
     */
    Optional<Pair<Theme, ThemeLocalization>> findCurrentIntegratedByLanguageCode(LanguageCode languageCode);

    /**
     * テーマIDと言語コードで統合済みのテーマを取得する
     *
     * @param themeId      テーマID
     * @param languageCode 言語コード
     * @return 統合済みのテーマ
     */
    Optional<Pair<Theme, ThemeLocalization>> findIntegratedByIdAndLanguageCode(ThemeId themeId, LanguageCode languageCode);

    /**
     * テーマIDで全言語の統合済みのテーマを取得する
     *
     * @param themeId テーマID
     * @return 全言語統合済みのテーマ
     */
    Optional<Pair<Theme, Map<LanguageCode, ThemeLocalization>>> findIntegratedMultiLangById(ThemeId themeId);

    /**
     * 現在開催中のテーマを取得する
     * 開始日が今日以降で、最も今日に近い（最も早い）テーマを返す
     *
     * @return 現在開催中のテーマ
     */
    Optional<Theme> findCurrentTheme();

    /**
     * テーマを取得する
     *
     * @param id 検索対象のテーマID
     * @return テーマIDが一致するテーマ
     */
    Optional<Theme> findById(ThemeId id);

    /**
     * テーマを保存する
     *
     * @param theme 保存するテーマ
     */
    void save(Theme theme);

    /**
     * テーマを削除する
     *
     * @param id 削除対象のテーマID
     */
    void delete(ThemeId id);

    /**
     * 開始日が指定された日付のテーマが存在するかを判定する
     *
     * @param startDate 検索対象の開始日
     * @return 存在する場合true、存在しない場合false
     */
    boolean existsByStartDate(ThemeStartDate startDate);

    /**
     * 開始日が指定された日付のテーマが存在するかを判定する
     * 指定されたIDを除く
     *
     * @param startDate 検索対象の開始日
     * @param excludeId 除外するテーマID
     * @return 存在する場合true、存在しない場合false
     */
    boolean existsByStartDateExcludingId(ThemeStartDate startDate, ThemeId excludeId);
}
