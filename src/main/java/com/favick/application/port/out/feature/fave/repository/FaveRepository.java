package com.favick.application.port.out.feature.fave.repository;

import com.favick.domain.feature.fave.model.Fave;
import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.review.model.Review;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.user.model.UserId;
import org.springframework.data.util.Pair;

import java.util.List;
import java.util.Optional;

/**
 * お気に入りリポジトリのポート
 * お気に入りの永続化を担当する
 */
public interface FaveRepository {
    /**
     * テーマIDでお気に入り一覧を取得する
     *
     * @param themeId テーマID
     * @return お気に入り一覧（作成日時の新しい順）
     */
    List<Fave> findAllApprovedByThemeId(ThemeId themeId);

    /**
     * お気に入りを取得する
     *
     * @param id 検索対象のお気に入りID
     * @return お気に入りIDが一致するお気に入り
     */
    Optional<Fave> findById(FaveId id);

    /**
     * ユーザーIDとテーマIDが一致する統合済みのお気に入りを取得する
     *
     * @param userId  検索対象のユーザーID
     * @param themeId 検索対象のテーマID
     * @return ユーザーIDとテーマIDが一致する統合済みのお気に入り
     */
    Optional<Pair<Fave, Review>> findIntegratedByUserIdAndThemeId(UserId userId, ThemeId themeId);

    /**
     * お気に入りを保存する
     *
     * @param fave 保存するお気に入り
     */
    void save(Fave fave);
}
