package com.favick.application.port.out.feature.like.repository;

import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.like.model.Like;
import com.favick.domain.feature.like.model.LikeId;
import com.favick.domain.feature.user.model.UserId;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * いいねリポジトリのポート
 * いいねの永続化を担当する
 */
public interface LikeRepository {
    /**
     * ユーザーIDとFaveIDでいいねを取得する
     *
     * @param userId ユーザーID
     * @param faveId FaveID
     * @return いいね
     */
    Optional<Like> findByUserIdAndFaveId(UserId userId, FaveId faveId);

    /**
     * ユーザーIDと複数のFaveIDでいいね一覧を取得する
     *
     * @param userId  ユーザーID
     * @param faveIds FaveIDのセット
     * @return いいね一覧
     */
    List<Like> findByUserIdAndFaveIds(UserId userId, Set<FaveId> faveIds);

    /**
     * いいねを保存する
     *
     * @param like 保存するいいね
     */
    void save(Like like);

    /**
     * いいねを削除する
     *
     * @param id 検索対象のいいねID
     */
    void delete(LikeId id);
}
