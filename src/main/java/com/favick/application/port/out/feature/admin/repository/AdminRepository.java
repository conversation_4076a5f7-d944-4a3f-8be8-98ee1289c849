package com.favick.application.port.out.feature.admin.repository;

import com.favick.domain.feature.admin.model.Admin;
import com.favick.domain.feature.admin.model.AdminEmail;

import java.util.Optional;

/**
 * 管理者リポジトリのポート
 * 管理者の永続化を担当する
 */
public interface AdminRepository {
    /**
     * メールアドレスが一致する管理者を取得する
     *
     * @param email 検索対象のメールアドレス
     * @return メールアドレスが一致する管理者
     */
    Optional<Admin> findByEmail(AdminEmail email);
}
