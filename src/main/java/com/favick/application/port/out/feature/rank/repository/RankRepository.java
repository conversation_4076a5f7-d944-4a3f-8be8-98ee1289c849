package com.favick.application.port.out.feature.rank.repository;

import com.favick.domain.feature.rank.model.Rank;
import com.favick.domain.feature.rank.model.RankId;
import com.favick.domain.feature.rank.model.RankName;

import java.util.Optional;

/**
 * ランクリポジトリのポート
 * ランクの永続化を担当する
 */
public interface RankRepository {
    /**
     * ランクIDが一致するランクを取得する
     *
     * @param id 検索対象のランクID
     * @return ランクIDが一致するランク
     */
    Optional<Rank> findById(RankId id);

    /**
     * ランク名が一致するランクを取得する
     *
     * @param name 検索対象のランク名
     * @return ランク名が一致するランク
     */
    Optional<Rank> findByName(RankName name);
}
