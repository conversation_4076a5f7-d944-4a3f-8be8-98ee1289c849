package com.favick.application.service.theme_localization.command;

import com.favick.application.dto.command.LocalizedContent;
import com.favick.common.helper.ValueObjectHelper;
import com.favick.domain.exception.DomainException;
import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import com.favick.domain.feature.theme_localization.model.ThemeDescription;
import com.favick.domain.feature.theme_localization.model.ThemeTitle;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ThemeLocalizationValidator {
    /**
     * ローカライゼーション情報をバリデーションして変換する
     *
     * @param localizations バリデーション前のローカライゼーション情報
     * @param errors        エラーリスト
     * @return バリデーション済みのローカライゼーション情報
     */
    public Map<LanguageCode, ThemeContent> validateLocalizations(
        Map<LanguageCodeValue, LocalizedContent> localizations,
        List<DomainException> errors
    ) {
        Map<LanguageCode, ThemeContent> validatedLocalizations = new HashMap<>();

        localizations.forEach((languageCodeValue, localizedContent) ->
            ValueObjectHelper.validate(() -> new LanguageCode(languageCodeValue), errors)
                .ifPresent(languageCode -> {
                    String titleFieldName = "title" + languageCode.getFieldSuffix();
                    String descriptionFieldName = "description" + languageCode.getFieldSuffix();

                    ValueObjectHelper.validate(() -> new ThemeTitle(localizedContent.title(), titleFieldName), errors)
                        .flatMap(themeTitle ->
                            ValueObjectHelper.validate(() -> new ThemeDescription(localizedContent.description(), descriptionFieldName), errors)
                                .map(themeDescription -> new ThemeContent(themeTitle, themeDescription))
                        )
                        .ifPresent(param -> validatedLocalizations.put(languageCode, param));
                })
        );

        return validatedLocalizations;
    }
}
