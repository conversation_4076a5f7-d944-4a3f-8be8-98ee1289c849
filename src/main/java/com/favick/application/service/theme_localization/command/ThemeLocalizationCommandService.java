package com.favick.application.service.theme_localization.command;

import com.favick.application.port.out.feature.theme_localization.repository.ThemeLocalizationRepository;
import com.favick.domain.feature.theme_localization.model.ThemeLocalization;
import com.favick.domain.feature.theme_localization.model.ThemeLocalizationId;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * テーマローカライゼーションに関するコマンド操作を提供するサービス
 * テーマローカライゼーションの作成と更新を担当する
 */
@Service
@RequiredArgsConstructor
public class ThemeLocalizationCommandService {
    private final ThemeLocalizationRepository themeLocalizationRepository;

    /**
     * 新しいテーマローカライゼーションを作成する
     *
     * @param themeLocalization テーマローカライゼーション
     * @return 作成したテーマローカライゼーションのID
     */
    public ThemeLocalizationId create(ThemeLocalization themeLocalization) {
        themeLocalizationRepository.save(themeLocalization);
        return themeLocalization.getId();
    }

    /**
     * 既存のテーマローカライゼーションを更新する
     *
     * @param themeLocalization テーマローカライゼーション
     * @return 更新したテーマのID
     */
    public ThemeLocalizationId update(ThemeLocalization themeLocalization) {
        themeLocalizationRepository.save(themeLocalization);
        return themeLocalization.getId();
    }
}
