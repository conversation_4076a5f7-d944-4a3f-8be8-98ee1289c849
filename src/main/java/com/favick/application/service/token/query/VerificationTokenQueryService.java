package com.favick.application.service.token.query;

import com.favick.application.port.out.feature.token.repository.VerificationTokenRepository;
import com.favick.domain.feature.token.model.TokenValue;
import com.favick.domain.feature.token.model.VerificationToken;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 認証トークンに関するクエリ操作を提供するサービス
 * 認証トークンの取得を担当する
 */
@Service
@RequiredArgsConstructor
public class VerificationTokenQueryService {
    private final VerificationTokenRepository verificationTokenRepository;

    /**
     * トークン値が一致する認証トークンを取得する
     *
     * @param token トークン値
     * @return 認証トークン
     */
    public Optional<VerificationToken> getByToken(TokenValue token) {
        return verificationTokenRepository.findByToken(token);
    }
}
