package com.favick.application.service.token.command;

import com.favick.application.port.out.feature.token.repository.VerificationTokenRepository;
import com.favick.domain.feature.token.model.VerificationToken;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 認証トークンに関するコマンド操作を提供するサービス
 * 認証トークンの削除を担当する
 */
@Service
@RequiredArgsConstructor
public class VerificationTokenCommandService {
    private final VerificationTokenRepository verificationTokenRepository;

    /**
     * 認証トークンを削除する
     *
     * @param verificationToken 認証トークン
     */
    public void delete(VerificationToken verificationToken) {
        verificationTokenRepository.delete(verificationToken.getId());
    }
}
