package com.favick.application.service.token.query;

import com.favick.application.port.out.feature.token.repository.PasswordResetTokenRepository;
import com.favick.domain.feature.token.model.PasswordResetToken;
import com.favick.domain.feature.token.model.TokenValue;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * パスワードリセットトークンに関するクエリ操作を提供するサービス
 * パスワードリセットトークンの取得を担当する
 */
@Service
@RequiredArgsConstructor
public class PasswordResetTokenQueryService {
    private final PasswordResetTokenRepository passwordResetTokenRepository;

    /**
     * トークン値が一致するパスワードリセットトークンを取得する
     *
     * @param token トークン値
     * @return パスワードリセットトークン
     */
    public Optional<PasswordResetToken> getByToken(TokenValue token) {
        return passwordResetTokenRepository.findByToken(token);
    }
}
