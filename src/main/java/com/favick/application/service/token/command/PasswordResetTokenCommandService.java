package com.favick.application.service.token.command;

import com.favick.application.port.out.feature.token.repository.PasswordResetTokenRepository;
import com.favick.domain.feature.token.model.PasswordResetToken;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * パスワードリセットトークンに関するコマンド操作を提供するサービス
 * パスワードリセットトークンの削除を担当する
 */
@Service
@RequiredArgsConstructor
public class PasswordResetTokenCommandService {
    private final PasswordResetTokenRepository passwordResetTokenRepository;

    /**
     * パスワードリセットトークンを削除する
     *
     * @param passwordResetToken パスワードリセットトークン
     */
    public void delete(PasswordResetToken passwordResetToken) {
        passwordResetTokenRepository.delete(passwordResetToken.getId());
    }
}
