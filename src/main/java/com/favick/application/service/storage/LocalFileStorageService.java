package com.favick.application.service.storage;

import com.favick.common.exception.FileStorageException;
import com.favick.domain.feature.theme.model.ThemeType;
import com.favick.domain.feature.theme.model.ThemeTypeValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Map;
import java.util.UUID;

/**
 * ローカルファイルシステムを使用したファイルストレージサービス実装
 */
@Service
@Slf4j
public class LocalFileStorageService implements FileStorageService {
    /**
     * テーマタイプ別のディレクトリ名
     */
    private static final Map<ThemeTypeValue, String> DIRECTORY_MAP = Map.of(
        ThemeTypeValue.PHOTO, "photos",
        ThemeTypeValue.MOVIE, "movies",
        ThemeTypeValue.AUDIO, "audios"
    );

    @Value("${app.file.upload-dir:src/main/resources/static/storage}")
    private String uploadDir;

    @Override
    public String store(MultipartFile file, ThemeType themeType) throws IOException {
        // ファイル名を生成
        String newFileName = generateNewFileName(file.getOriginalFilename(), themeType);

        // 保存先パスを構築
        String subdirectory = DIRECTORY_MAP.get(themeType.value());
        Path targetPath = Paths.get(uploadDir, subdirectory, newFileName);

        // ディレクトリを作成
        createDirectoriesIfNotExists(targetPath.getParent());

        // ファイルを保存
        Files.copy(file.getInputStream(), targetPath, StandardCopyOption.REPLACE_EXISTING);

        // 相対パスを返す
        return Paths.get(subdirectory, newFileName).toString().replace("\\", "/");
    }

    @Override
    public void delete(String filePath) throws IOException {
        Path targetPath = Paths.get(uploadDir, filePath);

        if (Files.exists(targetPath)) {
            Files.delete(targetPath);
        }
    }

    /**
     * ユニークなファイル名を生成する
     *
     * @param originalFileName 元のファイル名
     * @param themeType        テーマタイプ
     * @return 生成された新しいファイル名
     */
    private String generateNewFileName(
        String originalFileName,
        ThemeType themeType
    ) {
        String uuid = UUID.randomUUID().toString();
        String sanitizedName = sanitizeFileName(originalFileName);
        int dotIndex = sanitizedName.lastIndexOf(".");
        String extension = sanitizedName.substring(dotIndex).toLowerCase();
        return uuid + extension;
    }

    /**
     * ファイル名をサニタイズする
     *
     * @param fileName ファイル名
     * @return サニタイズされたファイル名
     */
    private String sanitizeFileName(String fileName) {
        return fileName.replaceAll("[^a-zA-Z0-9.\\-_]", "").trim();
    }

    /**
     * ディレクトリが存在しない場合は作成する
     *
     * @param directoryPath ディレクトリパス
     * @throws FileStorageException ディレクトリ作成に失敗した場合
     */
    private void createDirectoriesIfNotExists(Path directoryPath) throws IOException {
        if (!Files.exists(directoryPath)) {
            Files.createDirectories(directoryPath);
        }
    }
}
