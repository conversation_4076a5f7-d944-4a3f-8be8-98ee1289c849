package com.favick.application.service.storage;

import com.favick.domain.feature.theme.model.ThemeType;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * ファイルストレージサービスのインターフェース
 * ファイルの保存、削除、URL取得を担当する
 */
public interface FileStorageService {
    /**
     * ファイルを保存する
     *
     * @param file      保存するファイル
     * @param themeType テーマタイプ（保存先ディレクトリの決定に使用）
     * @return 保存されたファイルの相対パス
     */
    String store(MultipartFile file, ThemeType themeType) throws IOException;

    /**
     * ファイルを削除する
     *
     * @param filePath 削除するファイルの相対パス
     */
    void delete(String filePath) throws IOException;
}
