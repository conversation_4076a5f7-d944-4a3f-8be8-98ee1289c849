package com.favick.application.service.admin.query;

import com.favick.application.port.out.feature.admin.repository.AdminRepository;
import com.favick.domain.feature.admin.model.Admin;
import com.favick.domain.feature.admin.model.AdminEmail;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 管理者に関するクエリ操作を提供するサービス
 * 管理者の取得を担当する
 */
@Service
@RequiredArgsConstructor
public class AdminQueryService {
    private final AdminRepository adminRepository;

    /**
     * 管理者を取得する
     *
     * @param email 管理者のメールアドレス
     * @return 管理者
     */
    public Optional<Admin> getByEmail(AdminEmail email) {
        return adminRepository.findByEmail(email);
    }
}
