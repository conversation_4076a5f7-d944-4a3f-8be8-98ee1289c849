package com.favick.application.service.theme.query;

import com.favick.application.port.out.feature.theme.repository.ThemeRepository;
import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.theme.model.Theme;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.theme.model.ThemeStartDate;
import com.favick.domain.feature.theme_localization.model.ThemeLocalization;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * テーマに関するクエリ操作を提供するサービス
 * テーマの取得を担当する
 */
@Service
@RequiredArgsConstructor
public class ThemeQueryService {
    private final ThemeRepository themeRepository;

    /**
     * 言語コードで統合済みのテーマ一覧を取得する
     *
     * @param languageCode 言語コード
     * @return 統合済みのテーマ一覧
     */
    @Cacheable(value = "themes:listIntegrated", key = "#languageCode.value()")
    public List<Pair<Theme, ThemeLocalization>> listIntegratedByLanguageCode(LanguageCode languageCode) {
        return themeRepository.findAllIntegratedByLanguageCode(languageCode);
    }

    /**
     * 言語コードで現在開催中の統合済みのテーマを取得する
     *
     * @param languageCode 言語コード
     * @return 現在開催中の統合済みのテーマ
     */
    @Cacheable(value = "themes:getCurrentIntegrated", key = "#languageCode.value()")
    public Optional<Pair<Theme, ThemeLocalization>> getCurrentIntegratedByLanguageCode(LanguageCode languageCode) {
        return themeRepository.findCurrentIntegratedByLanguageCode(languageCode);
    }

    /**
     * テーマIDと言語コードで統合済みのテーマを取得する
     *
     * @param themeId      テーマID
     * @param languageCode 言語コード
     * @return 統合済みのテーマ
     */
    @Cacheable(value = "themes:getIntegrated", key = "#themeId.value() + ':' + #languageCode.value()")
    public Optional<Pair<Theme, ThemeLocalization>> getIntegratedByIdAndLanguageCode(ThemeId themeId, LanguageCode languageCode) {
        return themeRepository.findIntegratedByIdAndLanguageCode(themeId, languageCode);
    }

    /**
     * テーマIDで全言語の統合済みのテーマを取得する
     *
     * @param themeId テーマID
     * @return 全言語統合済みのテーマ
     */
    @Cacheable(value = "themes:getMultiLang", key = "#themeId.value()")
    public Optional<Pair<Theme, Map<LanguageCode, ThemeLocalization>>> getIntegratedMultiLangById(ThemeId themeId) {
        return themeRepository.findIntegratedMultiLangById(themeId);
    }

    /**
     * テーマを取得する
     *
     * @param id ID
     * @return テーマ
     */
    @Cacheable("themes:getById")
    public Optional<Theme> getById(ThemeId id) {
        return themeRepository.findById(id);
    }

    /**
     * 開始日が指定された日付のテーマが存在するかを判定する
     *
     * @param startDate 検索対象の開始日
     * @return 存在する場合true、存在しない場合false
     */
    public boolean existsByStartDate(ThemeStartDate startDate) {
        return themeRepository.existsByStartDate(startDate);
    }

    /**
     * 開始日が指定された日付のテーマが存在するかを判定する
     * 指定されたIDを除く
     *
     * @param startDate 検索対象の開始日
     * @param excludeId 除外するテーマID
     * @return 存在する場合true、存在しない場合false
     */
    public boolean existsByStartDateExcludingId(ThemeStartDate startDate, ThemeId excludeId) {
        return themeRepository.existsByStartDateExcludingId(startDate, excludeId);
    }
}
