package com.favick.application.service.theme.command;

import com.favick.application.port.out.feature.theme.repository.ThemeRepository;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.domain.feature.theme.model.Theme;
import com.favick.domain.feature.theme.model.ThemeId;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

/**
 * テーマに関するコマンド操作を提供するサービス
 * テーマの作成、更新、削除を担当する
 */
@Service
@RequiredArgsConstructor
public class ThemeCommandService {
    private final ThemeRepository themeRepository;

    /**
     * 新しいテーマを作成する
     *
     * @param theme テーマ
     * @return 作成したテーマのID
     * @throws InvalidDomainsException バリデーションエラーが発生した場合
     */
    @Caching(evict = {
        @CacheEvict(value = "themes:list", allEntries = true),
        @CacheEvict(value = "themes:listIntegrated", allEntries = true),
        @CacheEvict(value = "themes:getCurrentTheme", allEntries = true),
        @CacheEvict(value = "themes:getCurrentIntegrated", allEntries = true)
    })
    public ThemeId create(Theme theme) {
        themeRepository.save(theme);
        return theme.getId();
    }

    /**
     * 既存のテーマを更新する
     *
     * @param theme テーマ
     * @return 更新したテーマのID
     */
    @Caching(evict = {
        @CacheEvict(value = "themes:getById", key = "#theme.id"),
        @CacheEvict(value = "themes:getIntegrated", allEntries = true),
        @CacheEvict(value = "themes:getMultiLang", key = "#theme.id"),
        @CacheEvict(value = "themes:list", allEntries = true),
        @CacheEvict(value = "themes:listIntegrated", allEntries = true),
        @CacheEvict(value = "themes:getCurrentTheme", allEntries = true),
        @CacheEvict(value = "themes:getCurrentIntegrated", allEntries = true)
    })
    public ThemeId update(Theme theme) {
        themeRepository.save(theme);
        return theme.getId();
    }

    /**
     * 既存のテーマを削除する
     *
     * @param theme 削除対象のテーマ
     */
    @Caching(evict = {
        @CacheEvict(value = "themes:getById", key = "#theme.id"),
        @CacheEvict(value = "themes:getIntegrated", allEntries = true),
        @CacheEvict(value = "themes:getMultiLang", key = "#theme.id"),
        @CacheEvict(value = "themes:list", allEntries = true),
        @CacheEvict(value = "themes:listIntegrated", allEntries = true),
        @CacheEvict(value = "themes:getCurrentTheme", allEntries = true),
        @CacheEvict(value = "themes:getCurrentIntegrated", allEntries = true)
    })
    public void delete(Theme theme) {
        themeRepository.delete(theme.getId());
    }
}
