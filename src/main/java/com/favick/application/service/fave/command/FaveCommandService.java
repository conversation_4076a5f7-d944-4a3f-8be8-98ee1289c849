package com.favick.application.service.fave.command;

import com.favick.application.port.out.feature.fave.repository.FaveRepository;
import com.favick.domain.feature.fave.model.Fave;
import com.favick.domain.feature.fave.model.FaveId;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * お気に入りコマンドサービス
 * お気に入りの作成・更新・削除を担当する
 */
@Service
@RequiredArgsConstructor
public class FaveCommandService {
    private final FaveRepository faveRepository;

    /**
     * お気に入りを作成する
     *
     * @param fave お気に入り
     * @return 作成されたお気に入りのID
     */
    public FaveId create(Fave fave) {
        faveRepository.save(fave);
        return fave.getId();
    }

    /**
     * お気に入りを更新する
     *
     * @param fave お気に入り
     * @return 更新されたお気に入りのID
     */
    public FaveId update(Fave fave) {
        faveRepository.save(fave);
        return fave.getId();
    }
}
