package com.favick.application.service.fave.query;

import com.favick.application.port.out.feature.fave.repository.FaveRepository;
import com.favick.domain.feature.fave.model.Fave;
import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.review.model.Review;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.user.model.UserId;
import lombok.RequiredArgsConstructor;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * お気に入りに関するクエリ操作を提供するサービス
 * お気に入りの取得を担当する
 */
@Service
@RequiredArgsConstructor
public class FaveQueryService {
    private final FaveRepository faveRepository;

    /**
     * テーマIDでお気に入り一覧を取得する
     *
     * @param themeId テーマID
     * @return お気に入り一覧
     */
    public List<Fave> listApprovedByThemeId(ThemeId themeId) {
        return faveRepository.findAllApprovedByThemeId(themeId);
    }

    /**
     * お気に入りを取得する
     *
     * @param faveId お気に入りID
     * @return お気に入り
     */
    public Optional<Fave> getById(FaveId faveId) {
        return faveRepository.findById(faveId);
    }

    /**
     * ユーザーIDとテーマIDで統合済みのお気に入りを取得する
     *
     * @param userId  ユーザーID
     * @param themeId テーマID
     * @return 統合済みのお気に入り
     */
    public Optional<Pair<Fave, Review>> getIntegratedByUserIdAndThemeId(UserId userId, ThemeId themeId) {
        return faveRepository.findIntegratedByUserIdAndThemeId(userId, themeId);
    }
}
