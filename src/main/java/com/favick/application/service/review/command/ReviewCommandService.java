package com.favick.application.service.review.command;

import com.favick.application.port.out.feature.review.repository.ReviewRepository;
import com.favick.domain.feature.review.model.Review;
import com.favick.domain.feature.review.model.ReviewId;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 審査コマンドサービス
 * 審査の作成・更新・削除を担当する
 */
@Service
@RequiredArgsConstructor
public class ReviewCommandService {
    private final ReviewRepository reviewRepository;

    /**
     * 審査を作成する
     *
     * @param review 審査
     * @return 作成された審査のID
     */
    public ReviewId create(Review review) {
        reviewRepository.save(review);
        return review.getId();
    }

    /**
     * 審査を更新する
     *
     * @param review 審査
     * @return 更新された審査のID
     */
    public ReviewId update(Review review) {
        reviewRepository.save(review);
        return review.getId();
    }
}
