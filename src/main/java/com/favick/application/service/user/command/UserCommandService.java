package com.favick.application.service.user.command;

import com.favick.application.port.out.feature.user.repository.UserRepository;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.domain.feature.user.model.User;
import com.favick.domain.feature.user.model.UserId;
import com.favick.domain.feature.user.model.UserPassword;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * ユーザーに関するコマンド操作を提供するサービス
 * ユーザーの作成、更新、削除を担当する
 */
@Service
@RequiredArgsConstructor
public class UserCommandService {
    private final UserRepository userRepository;

    /**
     * 新しいユーザーを作成する
     *
     * @param user ユーザー
     * @return 作成したユーザーのID
     * @throws InvalidDomainsException バリデーションエラーが発生した場合
     */
    public UserId create(User user) {
        userRepository.save(user);
        return user.getId();
    }

    /**
     * ユーザーを有効にする
     *
     * @param user ユーザー
     */
    public void enableUser(User user) {
        if (!user.isEnabled()) {
            user.enable();
            userRepository.save(user);
        }
    }

    /**
     * ユーザーのパスワードを更新する
     *
     * @param user     ユーザー
     * @param password パスワード
     */
    public void updatePassword(User user, UserPassword password) {
        user.changePassword(password);
        userRepository.save(user);
    }
}
