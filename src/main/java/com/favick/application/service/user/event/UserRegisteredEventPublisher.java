package com.favick.application.service.user.event;

import com.favick.domain.feature.user.event.UserRegisteredEvent;
import com.favick.domain.feature.user.model.UserEmail;
import com.favick.domain.feature.user.model.UserId;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * ユーザー登録完了イベントの発行者
 * ユーザーが登録されたことを通知する
 */
@Component
@RequiredArgsConstructor
public class UserRegisteredEventPublisher {
    private final ApplicationEventPublisher applicationEventPublisher;

    /**
     * ユーザー登録完了イベントを発行する
     *
     * @param id     登録されたユーザーのID
     * @param email  登録されたユーザーのメールアドレス
     * @param origin イベントの発生元
     */
    public void publish(
        UserId id,
        UserEmail email,
        String origin
    ) {
        applicationEventPublisher.publishEvent(
            new UserRegisteredEvent(
                this,
                id.value(),
                email.value(),
                origin
            )
        );
    }
}
