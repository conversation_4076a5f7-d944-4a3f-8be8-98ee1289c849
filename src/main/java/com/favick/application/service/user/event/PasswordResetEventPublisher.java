package com.favick.application.service.user.event;

import com.favick.domain.feature.user.event.PasswordResetEvent;
import com.favick.domain.feature.user.model.UserEmail;
import com.favick.domain.feature.user.model.UserId;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * パスワードリセットイベントの発行者
 * ユーザーがパスワードリセットを要求したことを通知する
 */
@Component
@RequiredArgsConstructor
public class PasswordResetEventPublisher {
    private final ApplicationEventPublisher applicationEventPublisher;

    /**
     * パスワードリセット要求イベントを発行する
     *
     * @param id     登録されたユーザーのID
     * @param email  登録されたユーザーのメールアドレス
     * @param origin イベントの発生元
     */
    public void publish(
        UserId id,
        UserEmail email,
        String origin
    ) {
        applicationEventPublisher.publishEvent(
            new PasswordResetEvent(
                this,
                id.value(),
                email.value(),
                origin
            )
        );
    }
}
