package com.favick.application.service.user.query;

import com.favick.application.port.out.feature.user.repository.UserRepository;
import com.favick.domain.feature.user.model.User;
import com.favick.domain.feature.user.model.UserEmail;
import com.favick.domain.feature.user.model.UserId;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * ユーザーに関するクエリ操作を提供するサービス
 * ユーザーの取得を担当する
 */
@Service
@RequiredArgsConstructor
public class UserQueryService {
    private final UserRepository userRepository;

    /**
     * ユーザーIDが一致するユーザーを取得する
     *
     * @param id ユーザーID
     * @return ユーザー
     */
    public Optional<User> getById(UserId id) {
        return userRepository.findById(id);
    }

    /**
     * メールアドレスが一致するユーザーを取得する
     *
     * @param email ユーザーのメールアドレス
     * @return ユーザー
     */
    public Optional<User> getByEmail(UserEmail email) {
        return userRepository.findByEmail(email);
    }

    /**
     * メールアドレスが一致するユーザーが存在するかを判定する
     *
     * @param email ユーザーのメールアドレス
     * @return メールアドレスが一致するユーザーが存在する場合true、存在しない場合false
     */
    public boolean existsByEmail(UserEmail email) {
        return userRepository.existsByEmail(email);
    }
}
