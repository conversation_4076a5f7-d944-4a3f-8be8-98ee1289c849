package com.favick.application.service.like.command;

import com.favick.application.port.out.feature.like.repository.LikeRepository;
import com.favick.domain.feature.like.model.Like;
import com.favick.domain.feature.like.model.LikeId;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * いいねコマンドサービス
 * いいねの作成・削除を担当する
 */
@Service
@RequiredArgsConstructor
public class LikeCommandService {
    private final LikeRepository likeRepository;

    /**
     * いいねを作成する
     *
     * @param like いいね
     * @return 作成されたいいねのID
     */
    public LikeId create(Like like) {
        likeRepository.save(like);
        return like.getId();
    }

    /**
     * いいねを削除する
     *
     * @param like いいね
     */
    public void delete(Like like) {
        likeRepository.delete(like.getId());
    }
}
