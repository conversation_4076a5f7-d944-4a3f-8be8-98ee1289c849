package com.favick.application.service.like.query;

import com.favick.application.port.out.feature.like.repository.LikeRepository;
import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.like.model.Like;
import com.favick.domain.feature.user.model.UserId;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * いいねに関するクエリ操作を提供するサービス
 * いいねの取得を担当する
 */
@Service
@RequiredArgsConstructor
public class LikeQueryService {
    private final LikeRepository likeRepository;

    /**
     * ユーザーIDとFaveIDでいいねを取得する
     *
     * @param userId ユーザーID
     * @param faveId FaveID
     * @return いいね
     */
    public Optional<Like> getByUserIdAndFaveId(UserId userId, FaveId faveId) {
        return likeRepository.findByUserIdAndFaveId(userId, faveId);
    }

    /**
     * ユーザーがいいねしたお気に入りIDのセットを取得
     *
     * @param userId  ユーザーID
     * @param faveIds 対象のお気に入りIDセット
     * @return いいねしたお気に入りIDのセット
     */
    public Set<UUID> listLikedFaveIdsByUserIdAndFaveIds(UserId userId, Set<FaveId> faveIds) {
        if (faveIds.isEmpty()) {
            return Set.of();
        }

        return likeRepository.findByUserIdAndFaveIds(userId, faveIds)
            .stream()
            .map(like -> like.getFaveId().value())
            .collect(Collectors.toSet());
    }
}
