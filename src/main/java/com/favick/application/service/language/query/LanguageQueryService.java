package com.favick.application.service.language.query;

import com.favick.application.port.out.feature.language.repository.LanguageRepository;
import com.favick.domain.feature.language.model.Language;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 言語に関するクエリ操作を提供するサービス
 * 言語の取得を担当する
 */
@Service
@RequiredArgsConstructor
public class LanguageQueryService {
    private final LanguageRepository languageRepository;

    /**
     * 言語一覧を取得する
     *
     * @return 言語一覧
     */
    @Cacheable("languages:list")
    public List<Language> list() {
        return languageRepository.findAll();
    }
}
