package com.favick.application.service.rank.query;

import com.favick.application.port.out.feature.rank.repository.RankRepository;
import com.favick.domain.feature.rank.model.Rank;
import com.favick.domain.feature.rank.model.RankId;
import com.favick.domain.feature.rank.model.RankName;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * ランクに関するクエリ操作を提供するサービス
 * ランクの取得を担当する
 */
@Service
@RequiredArgsConstructor
public class RankQueryService {
    private final RankRepository rankRepository;

    /**
     * ランクIDが一致するランクを取得する
     *
     * @param id ランクID
     * @return ランク
     */
    @Cacheable("ranks:getById")
    public Optional<Rank> getById(RankId id) {
        return rankRepository.findById(id);
    }

    /**
     * ランク名が一致するランクを取得する
     *
     * @param name ランク名
     * @return ランク
     */
    @Cacheable("ranks:getByName")
    public Optional<Rank> getByName(RankName name) {
        return rankRepository.findByName(name);
    }
}
