package com.favick.application.service.role.query;

import com.favick.application.port.out.feature.role.repository.RoleRepository;
import com.favick.domain.feature.role.model.Role;
import com.favick.domain.feature.role.model.RoleId;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 権限に関するクエリ操作を提供するサービス
 * 権限の取得を担当する
 */
@Service
@RequiredArgsConstructor
public class RoleQueryService {
    private final RoleRepository roleRepository;

    /**
     * 権限を取得する
     *
     * @param id 権限ID
     * @return 権限
     */
    @Cacheable("roles:getById")
    public Optional<Role> getById(RoleId id) {
        return roleRepository.findById(id);
    }
}
