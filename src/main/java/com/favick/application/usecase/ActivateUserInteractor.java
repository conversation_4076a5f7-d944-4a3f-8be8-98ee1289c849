package com.favick.application.usecase;

import com.favick.application.dto.query.ActivateUserQueryDto;
import com.favick.application.port.in.ActivateUserUseCase;
import com.favick.application.service.token.command.VerificationTokenCommandService;
import com.favick.application.service.token.query.VerificationTokenQueryService;
import com.favick.application.service.user.command.UserCommandService;
import com.favick.application.service.user.query.UserQueryService;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.common.helper.ValueObjectHelper;
import com.favick.domain.exception.DomainException;
import com.favick.domain.feature.token.model.TokenValue;
import com.favick.domain.feature.token.model.VerificationToken;
import com.favick.domain.feature.user.model.User;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * ユーザーを有効化するユースケースのインタラクター
 */
@Service
@RequiredArgsConstructor
@Transactional
public class ActivateUserInteractor implements ActivateUserUseCase {
    private final VerificationTokenQueryService verificationTokenQueryService;
    private final VerificationTokenCommandService verificationTokenCommandService;
    private final UserQueryService userQueryService;
    private final UserCommandService userCommandService;

    @Override
    public void handle(ActivateUserQueryDto query) {
        List<DomainException> errors = new ArrayList<>();

        Optional<TokenValue> tokenValueOpt = ValueObjectHelper.validate(() -> new TokenValue(query.token()), errors);

        if (!errors.isEmpty()) {
            throw new InvalidDomainsException(errors);
        }

        VerificationToken verificationToken = tokenValueOpt
            .flatMap(verificationTokenQueryService::getByToken)
            .orElseThrow(() -> new ResourceNotFoundException("domain.token", "Token: " + tokenValueOpt.orElseThrow().value()));

        if (verificationToken.isExpired()) {
            throw new DomainException("error.expired", "Token");
        }

        User user = Optional.of(verificationToken.getUserId())
            .flatMap(userQueryService::getById)
            .orElseThrow(() -> new ResourceNotFoundException("domain.user", "ID: " + verificationToken.getUserId().value()));

        userCommandService.enableUser(user);

        // トークン削除
        verificationTokenCommandService.delete(verificationToken);
    }
}
