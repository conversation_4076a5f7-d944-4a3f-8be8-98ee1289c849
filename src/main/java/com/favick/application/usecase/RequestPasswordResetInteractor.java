package com.favick.application.usecase;

import com.favick.application.dto.command.RequestPasswordResetCommandDto;
import com.favick.application.port.in.RequestPasswordResetUseCase;
import com.favick.application.service.user.event.PasswordResetEventPublisher;
import com.favick.application.service.user.query.UserQueryService;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.common.helper.ValueObjectHelper;
import com.favick.domain.exception.DomainException;
import com.favick.domain.feature.user.model.User;
import com.favick.domain.feature.user.model.UserEmail;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * パスワードリセット要求ユースケースのインタラクター
 */
@Service
@RequiredArgsConstructor
@Transactional
public class RequestPasswordResetInteractor implements RequestPasswordResetUseCase {
    private final UserQueryService userQueryService;
    private final PasswordResetEventPublisher passwordResetEventPublisher;

    @Override
    public void handle(RequestPasswordResetCommandDto command) {
        List<DomainException> errors = new ArrayList<>();

        Optional<UserEmail> emailOpt = ValueObjectHelper.validate(() -> new UserEmail(command.email()), errors);

        if (!errors.isEmpty()) {
            throw new InvalidDomainsException(errors);
        }

        User user = emailOpt
            .flatMap(userQueryService::getByEmail)
            .orElseThrow(() -> new ResourceNotFoundException("domain.user", "Email: " + emailOpt.orElseThrow().value()));

        emailOpt.ifPresent(email -> passwordResetEventPublisher.publish(
            user.getId(),
            email,
            command.origin()
        ));
    }
}
