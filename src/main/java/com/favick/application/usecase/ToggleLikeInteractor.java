package com.favick.application.usecase;

import com.favick.application.dto.command.ToggleLikeCommandDto;
import com.favick.application.dto.result.FaveSummaryResultDto;
import com.favick.application.port.in.ToggleLikeUseCase;
import com.favick.application.service.fave.query.FaveQueryService;
import com.favick.application.service.like.command.LikeCommandService;
import com.favick.application.service.like.query.LikeQueryService;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.common.helper.ValueObjectHelper;
import com.favick.domain.exception.DomainException;
import com.favick.domain.feature.fave.model.Fave;
import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.like.model.Like;
import com.favick.domain.feature.user.model.UserId;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * いいねを切り替えるユースケースのインタラクター
 */
@Service
@RequiredArgsConstructor
@Transactional
public class ToggleLikeInteractor implements ToggleLikeUseCase {
    private final LikeQueryService likeQueryService;
    private final LikeCommandService likeCommandService;
    private final FaveQueryService faveQueryService;

    @Override
    public FaveSummaryResultDto handle(ToggleLikeCommandDto command) {
        List<DomainException> errors = new ArrayList<>();

        Optional<UserId> userIdOpt = ValueObjectHelper.validate(() -> new UserId(command.userId()), errors);
        Optional<FaveId> faveIdOpt = ValueObjectHelper.validate(() -> new FaveId(command.faveId()), errors);

        if (!errors.isEmpty()) {
            throw new InvalidDomainsException(errors);
        }

        UserId userId = userIdOpt.orElseThrow();
        FaveId faveId = faveIdOpt.orElseThrow();

        // お気に入りを取得
        Fave fave = faveQueryService.getById(faveId)
            .orElseThrow(() -> new ResourceNotFoundException("domain.fave", "ID: " + faveId.value()));

        // いいねを切り替える
        Optional<Like> existingLike = likeQueryService.getByUserIdAndFaveId(userId, faveId);
        boolean isLiked;
        if (existingLike.isPresent()) {
            likeCommandService.delete(existingLike.get());
            isLiked = false;
        } else {
            Like newLike = Like.create(userId, faveId);
            likeCommandService.create(newLike);
            isLiked = true;
        }

        return new FaveSummaryResultDto(
            fave.getId().value(),
            fave.getContent().value(),
            isLiked,
            fave.getCreatedAt(),
            fave.getUpdatedAt()
        );
    }
}
