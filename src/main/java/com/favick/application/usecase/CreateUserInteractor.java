package com.favick.application.usecase;

import com.favick.application.dto.command.CreateUserCommandDto;
import com.favick.application.port.in.CreateUserUseCase;
import com.favick.application.service.rank.query.RankQueryService;
import com.favick.application.service.user.command.UserCommandService;
import com.favick.application.service.user.event.UserRegisteredEventPublisher;
import com.favick.application.service.user.query.UserQueryService;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.common.helper.EntityHelper;
import com.favick.common.helper.ValueObjectHelper;
import com.favick.domain.exception.DomainException;
import com.favick.domain.feature.rank.model.Rank;
import com.favick.domain.feature.rank.model.RankName;
import com.favick.domain.feature.rank.model.RankNameValue;
import com.favick.domain.feature.user.model.*;
import com.favick.domain.feature.user.service.UserDomainService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * ユーザーを登録するユースケースのインタラクター
 */
@Service
@RequiredArgsConstructor
@Transactional
public class CreateUserInteractor implements CreateUserUseCase {
    private final UserDomainService userDomainService;
    private final UserQueryService userQueryService;
    private final UserCommandService userCommandService;
    private final RankQueryService rankQueryService;
    private final UserRegisteredEventPublisher userRegisteredEventPublisher;
    private final PasswordEncoder passwordEncoder;

    @Override
    public void handle(CreateUserCommandDto command) {
        List<DomainException> errors = new ArrayList<>();

        Optional<UserName> nameOpt = ValueObjectHelper.validate(() -> new UserName(command.name()), errors);
        Optional<UserEmail> emailOpt = ValueObjectHelper.validate(() -> new UserEmail(command.email()), errors);
        Optional<RawUserPassword> passwordOpt = ValueObjectHelper.validate(() -> new RawUserPassword(command.password()), errors);
        Optional<UserImageUri> imageUriOpt = ValueObjectHelper.validate(() -> new UserImageUri(command.imageUrl()), errors);
        Optional<RankName> rankNameOpt = ValueObjectHelper.validate(() -> RankName.fromString(RankNameValue.RANK_SEED.name()), errors);

        Rank rank = rankNameOpt
            .flatMap(rankQueryService::getByName)
            .orElseThrow(() -> new ResourceNotFoundException("domain.rank", "Name: " + rankNameOpt.orElseThrow().value()));

        emailOpt.ifPresent(email -> ValueObjectHelper.validate(
            () -> userDomainService.validateEmailUniqueness(
                email,
                userQueryService::existsByEmail
            ),
            errors
        ));

        Optional<User> userOpt = nameOpt.flatMap(name ->
            emailOpt.flatMap(email ->
                passwordOpt.flatMap(password ->
                    EntityHelper.validate(
                        () -> User.create(
                            rank.getId(),
                            name,
                            email,
                            new UserPassword(passwordEncoder.encode(password.value())),
                            imageUriOpt.orElse(null)
                        ),
                        errors
                    )
                )
            )
        );

        if (!errors.isEmpty()) {
            throw new InvalidDomainsException(errors);
        }

        User user = userOpt.orElseThrow();

        UserId userId = userCommandService.create(user);

        emailOpt.ifPresent(email -> userRegisteredEventPublisher.publish(
            userId,
            email,
            command.origin()
        ));
    }
}
