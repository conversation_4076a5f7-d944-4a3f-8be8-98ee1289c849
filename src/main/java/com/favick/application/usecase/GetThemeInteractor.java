package com.favick.application.usecase;

import com.favick.application.dto.query.GetThemeQueryDto;
import com.favick.application.dto.result.ThemeDetailResultDto;
import com.favick.application.port.in.GetThemeUseCase;
import com.favick.application.service.theme.query.ThemeQueryService;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.common.helper.ValueObjectHelper;
import com.favick.domain.exception.DomainException;
import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import com.favick.domain.feature.theme.model.Theme;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.theme_localization.model.ThemeLocalization;
import lombok.RequiredArgsConstructor;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * テーマを取得するユースケースのインタラクター
 */
@Service
@RequiredArgsConstructor
public class GetThemeInteractor implements GetThemeUseCase {
    private final ThemeQueryService themeQueryService;

    @Override
    public ThemeDetailResultDto handle(GetThemeQueryDto query) {
        List<DomainException> errors = new ArrayList<>();

        Optional<ThemeId> themeIdOpt = ValueObjectHelper.validate(() -> new ThemeId(query.themeId()), errors);
        LanguageCodeValue languageCodeValue = Optional.ofNullable(query.languageCode())
            .orElse(LanguageCodeValue.JA);
        Optional<LanguageCode> languageCodeOpt = ValueObjectHelper.validate(() -> new LanguageCode(languageCodeValue), errors);

        if (!errors.isEmpty()) {
            throw new InvalidDomainsException(errors);
        }

        ThemeId themeId = themeIdOpt.orElseThrow();
        LanguageCode languageCode = languageCodeOpt.orElseThrow();

        // 統合済みのテーマを取得
        Pair<Theme, ThemeLocalization> integratedTheme = themeQueryService.getIntegratedByIdAndLanguageCode(themeId, languageCode)
            .orElseThrow(() -> new ResourceNotFoundException("domain.theme",
                "ThemeId: " + themeId.value() + ", LanguageCode: " + languageCode.value()));
        Theme theme = integratedTheme.getFirst();
        ThemeLocalization themeLocalization = integratedTheme.getSecond();

        return new ThemeDetailResultDto(
            theme.getId().value(),
            themeLocalization.getTitle().value(),
            themeLocalization.getDescription().value(),
            theme.getType().value(),
            theme.getStartDate().value(),
            theme.getCreatedAt(),
            theme.getUpdatedAt()
        );
    }
}
