package com.favick.application.usecase;

import com.favick.application.dto.query.LoadAdminQueryDto;
import com.favick.application.dto.result.AdminDetailsResultDto;
import com.favick.application.port.in.LoadAdminUseCase;
import com.favick.application.service.admin.query.AdminQueryService;
import com.favick.application.service.role.query.RoleQueryService;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.common.helper.ValueObjectHelper;
import com.favick.domain.exception.DomainException;
import com.favick.domain.feature.admin.model.Admin;
import com.favick.domain.feature.admin.model.AdminEmail;
import com.favick.domain.feature.role.model.Role;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 管理者（認証用）を読み込むユースケースのインタラクター
 */
@Service
@RequiredArgsConstructor
public class LoadAdminInteractor implements LoadAdminUseCase {
    private final AdminQueryService adminQueryService;
    private final RoleQueryService roleQueryService;

    @Override
    public AdminDetailsResultDto handle(LoadAdminQueryDto query) {
        List<DomainException> errors = new ArrayList<>();

        Optional<AdminEmail> adminEmailOpt = ValueObjectHelper.validate(() -> new AdminEmail(query.email()), errors);

        if (!errors.isEmpty()) {
            throw new InvalidDomainsException(errors);
        }

        Admin admin = adminEmailOpt
            .flatMap(adminQueryService::getByEmail)
            .orElseThrow(() -> new ResourceNotFoundException("domain.admin", "Email: " + adminEmailOpt.orElseThrow().value()));

        Role role = Optional.of(admin.getRoleId())
            .flatMap(roleQueryService::getById)
            .orElseThrow(() -> new ResourceNotFoundException("domain.role", "ID: " + admin.getRoleId().value()));

        return new AdminDetailsResultDto(
            admin.getId().value(),
            role.getId().value(),
            admin.getName().value(),
            role.getName().value(),
            admin.getEmail().value(),
            admin.getPassword().value(),
            admin.getCreatedAt(),
            admin.getUpdatedAt()
        );
    }
}
