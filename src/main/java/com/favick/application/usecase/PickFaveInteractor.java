package com.favick.application.usecase;

import com.favick.application.dto.command.PickFaveCommandDto;
import com.favick.application.port.in.PickFaveUseCase;
import com.favick.application.service.fave.command.FaveCommandService;
import com.favick.application.service.fave.query.FaveQueryService;
import com.favick.application.service.review.command.ReviewCommandService;
import com.favick.application.service.storage.FileStorageService;
import com.favick.common.exception.FileStorageException;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.helper.EntityHelper;
import com.favick.common.helper.ValueObjectHelper;
import com.favick.domain.exception.DomainException;
import com.favick.domain.exception.EntityException;
import com.favick.domain.feature.fave.model.Fave;
import com.favick.domain.feature.fave.model.FaveContent;
import com.favick.domain.feature.review.model.Review;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.theme.model.ThemeType;
import com.favick.domain.feature.user.model.UserId;
import lombok.RequiredArgsConstructor;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * お気に入りを作成するユースケースのインタラクター
 */
@Service
@RequiredArgsConstructor
@Transactional
public class PickFaveInteractor implements PickFaveUseCase {
    private final FaveQueryService faveQueryService;
    private final FaveCommandService faveCommandService;
    private final ReviewCommandService reviewCommandService;
    private final FileStorageService fileStorageService;

    @Override
    public void handle(PickFaveCommandDto command) {
        List<DomainException> errors = new ArrayList<>();

        Optional<UserId> userIdOpt = ValueObjectHelper.validate(() -> new UserId(command.userId()), errors);
        Optional<ThemeId> themeIdOpt = ValueObjectHelper.validate(() -> new ThemeId(command.themeId()), errors);
        Optional<ThemeType> themeTypeOpt = ValueObjectHelper.validate(() -> new ThemeType(command.themeType()), errors);

        // 既存の統合済みのお気に入りを取得
        Optional<Pair<Fave, Review>> existingIntegratedFave = userIdOpt.flatMap(userId ->
            themeIdOpt.flatMap(themeId ->
                faveQueryService.getIntegratedByUserIdAndThemeId(userId, themeId)
            )
        );
        if (existingIntegratedFave.isPresent()) {
            Review existingReview = existingIntegratedFave.get().getSecond();
            if (!existingReview.isRejected()) {
                throw new EntityException("fave", "validation.cannot_change");
            }
        }

        String filePath;
        try {
            filePath = fileStorageService.store(command.file(), themeTypeOpt.orElseThrow());
        } catch (Exception e) {
            throw new FileStorageException("error.file_storage.store_failed", command.file().getOriginalFilename(), e);
        }

        Optional<FaveContent> contentOpt = ValueObjectHelper.validate(() -> new FaveContent(filePath), errors);

        Optional<Fave> newFaveOpt = userIdOpt.flatMap(userId ->
            themeIdOpt.flatMap(themeId ->
                contentOpt.flatMap(content ->
                    EntityHelper.validate(
                        () -> Fave.create(userId, themeId, content),
                        errors
                    )
                )
            )
        );

        if (!errors.isEmpty()) {
            throw new InvalidDomainsException(errors);
        }

        Fave newFave = newFaveOpt.orElseThrow();

        if (existingIntegratedFave.isPresent()) {
            Fave fave = existingIntegratedFave.get().getFirst();
            Review review = existingIntegratedFave.get().getSecond();

            try {
                fileStorageService.delete(fave.getContent().value());
            } catch (Exception e) {
                throw new FileStorageException("error.file_storage.delete_failed", fave.getContent().value(), e);
            }

            // お気に入りを更新する
            fave.updateContent(newFave.getContent());
            faveCommandService.update(fave);

            // 審査を保留する
            review.pending();
            reviewCommandService.update(review);
        } else {
            // お気に入りを作成する
            faveCommandService.create(newFave);

            // 審査を作成する
            Review review = Review.create(newFave.getId());
            reviewCommandService.create(review);
        }
    }
}
