package com.favick.application.usecase;

import com.favick.application.dto.command.UpdateThemeCommandDto;
import com.favick.application.port.in.UpdateThemeUseCase;
import com.favick.application.service.theme.command.ThemeCommandService;
import com.favick.application.service.theme.query.ThemeQueryService;
import com.favick.application.service.theme_localization.command.ThemeContent;
import com.favick.application.service.theme_localization.command.ThemeLocalizationCommandService;
import com.favick.application.service.theme_localization.command.ThemeLocalizationValidator;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.common.helper.EntityHelper;
import com.favick.common.helper.ValueObjectHelper;
import com.favick.domain.exception.DomainException;
import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.theme.model.Theme;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.theme.model.ThemeStartDate;
import com.favick.domain.feature.theme.model.ThemeType;
import com.favick.domain.feature.theme.service.ThemeDomainService;
import com.favick.domain.feature.theme_localization.model.ThemeLocalization;
import lombok.RequiredArgsConstructor;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * テーマを更新するユースケースのインタラクター
 */
@Service
@RequiredArgsConstructor
@Transactional
public class UpdateThemeInteractor implements UpdateThemeUseCase {
    private final ThemeDomainService themeDomainService;
    private final ThemeQueryService themeQueryService;
    private final ThemeCommandService themeCommandService;
    private final ThemeLocalizationCommandService themeLocalizationCommandService;
    private final ThemeLocalizationValidator themeLocalizationValidator;

    @Override
    public void handle(UpdateThemeCommandDto command) {
        List<DomainException> errors = new ArrayList<>();

        Optional<ThemeId> themeIdOpt = ValueObjectHelper.validate(() -> new ThemeId(command.id()), errors);
        Map<LanguageCode, ThemeContent> validatedLocalizations = themeLocalizationValidator.validateLocalizations(command.localizations(), errors);
        Optional<ThemeType> themeTypeOpt = ValueObjectHelper.validate(() -> ThemeType.fromString(command.type()), errors);
        Optional<ThemeStartDate> themeStartDateOpt = ValueObjectHelper.validate(() -> new ThemeStartDate(command.startDate()), errors);

        if (!errors.isEmpty()) {
            throw new InvalidDomainsException(errors);
        }

        ThemeId themeId = themeIdOpt.orElseThrow();
        ThemeType themeType = themeTypeOpt.orElseThrow();
        ThemeStartDate themeStartDate = themeStartDateOpt.orElseThrow();

        // 全言語統合済みのテーマを取得
        Pair<Theme, Map<LanguageCode, ThemeLocalization>> integratedTheme = themeQueryService.getIntegratedMultiLangById(themeId)
            .orElseThrow(() -> new ResourceNotFoundException("domain.theme", "ID: " + themeId.value()));
        Theme theme = integratedTheme.getFirst();
        Map<LanguageCode, ThemeLocalization> localizations = integratedTheme.getSecond();

        // ドメインバリデーション
        ValueObjectHelper.validate(
            () -> themeDomainService.validateStartDateUniqueness(themeId, themeStartDate, themeQueryService::existsByStartDateExcludingId),
            errors
        );

        // エンティティ更新
        EntityHelper.validate(() -> theme.update(themeType, themeStartDate), errors);

        List<ThemeLocalization> updatedLocalizations = new ArrayList<>();
        validatedLocalizations.forEach((languageCode, content) -> {
            ThemeLocalization localization = localizations.get(languageCode);
            if (localization == null) {
                throw new ResourceNotFoundException("domain.theme",
                    "ThemeId: " + themeId.value() + ", LanguageCode: " + languageCode.value());
            }
            EntityHelper.validate(() -> localization.update(content.title(), content.description()), errors);
            updatedLocalizations.add(localization);
        });

        if (!errors.isEmpty()) {
            throw new InvalidDomainsException(errors);
        }

        // テーマを更新
        themeCommandService.update(theme);

        // ローカライゼーションを更新
        updatedLocalizations.forEach(themeLocalizationCommandService::update);
    }
}
