package com.favick.application.usecase;

import com.favick.application.dto.query.GetThemeForEditQueryDto;
import com.favick.application.dto.result.ThemeEditResultDto;
import com.favick.application.port.in.GetThemeForEditUseCase;
import com.favick.application.service.theme.query.ThemeQueryService;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.common.helper.ValueObjectHelper;
import com.favick.domain.exception.DomainException;
import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import com.favick.domain.feature.theme.model.Theme;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.theme_localization.model.ThemeLocalization;
import lombok.RequiredArgsConstructor;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 編集用にテーマを取得するユースケースのインタラクター
 */
@Service
@RequiredArgsConstructor
public class GetThemeForEditInteractor implements GetThemeForEditUseCase {
    private final ThemeQueryService themeQueryService;

    @Override
    public ThemeEditResultDto handle(GetThemeForEditQueryDto query) {
        List<DomainException> errors = new ArrayList<>();

        Optional<ThemeId> themeIdOpt = ValueObjectHelper.validate(() -> new ThemeId(query.themeId()), errors);

        if (!errors.isEmpty()) {
            throw new InvalidDomainsException(errors);
        }

        ThemeId themeId = themeIdOpt.orElseThrow();

        // 全言語統合済みのテーマを取得
        Pair<Theme, Map<LanguageCode, ThemeLocalization>> integratedTheme = themeQueryService.getIntegratedMultiLangById(themeId)
            .orElseThrow(() -> new ResourceNotFoundException("domain.theme", "ID: " + themeId.value()));
        Theme theme = integratedTheme.getFirst();
        Map<LanguageCode, ThemeLocalization> localizations = integratedTheme.getSecond();

        // ローカライズ情報を取得
        LanguageCode jaCode = new LanguageCode(LanguageCodeValue.JA);
        LanguageCode enCode = new LanguageCode(LanguageCodeValue.EN);
        ThemeLocalization localizationJa = localizations.get(jaCode);
        ThemeLocalization localizationEn = localizations.get(enCode);

        if (localizationJa == null) {
            throw new ResourceNotFoundException("domain.theme",
                "ThemeId: " + theme.getId().value() + ", LanguageCode: " + jaCode.value());
        }

        if (localizationEn == null) {
            throw new ResourceNotFoundException("domain.theme",
                "ThemeId: " + theme.getId().value() + ", LanguageCode: " + enCode.value());
        }

        return new ThemeEditResultDto(
            theme.getId().value(),
            localizationJa.getTitle().value(),
            localizationJa.getDescription().value(),
            localizationEn.getTitle().value(),
            localizationEn.getDescription().value(),
            theme.getType().value(),
            theme.getStartDate().value(),
            theme.getCreatedAt(),
            theme.getUpdatedAt(),
            theme.isEditable()
        );
    }
}
