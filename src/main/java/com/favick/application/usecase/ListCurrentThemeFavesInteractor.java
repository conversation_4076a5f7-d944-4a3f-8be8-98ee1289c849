package com.favick.application.usecase;

import com.favick.application.dto.query.ListCurrentThemeFavesQueryDto;
import com.favick.application.dto.result.CurrentThemeFavesResultDto;
import com.favick.application.dto.result.FaveSummaryResultDto;
import com.favick.application.dto.result.ThemeSummaryResultDto;
import com.favick.application.port.in.ListCurrentThemeFavesUseCase;
import com.favick.application.service.fave.query.FaveQueryService;
import com.favick.application.service.like.query.LikeQueryService;
import com.favick.application.service.theme.query.ThemeQueryService;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.common.helper.ValueObjectHelper;
import com.favick.domain.exception.DomainException;
import com.favick.domain.feature.fave.model.Fave;
import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import com.favick.domain.feature.theme.model.Theme;
import com.favick.domain.feature.theme_localization.model.ThemeLocalization;
import com.favick.domain.feature.user.model.UserId;
import lombok.RequiredArgsConstructor;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 開催中のテーマとお気に入りを取得するユースケースのインタラクター
 */
@Service
@RequiredArgsConstructor
public class ListCurrentThemeFavesInteractor implements ListCurrentThemeFavesUseCase {
    private final ThemeQueryService themeQueryService;
    private final FaveQueryService faveQueryService;
    private final LikeQueryService likeQueryService;

    @Override
    public CurrentThemeFavesResultDto handle(ListCurrentThemeFavesQueryDto query) {
        List<DomainException> errors = new ArrayList<>();

        LanguageCodeValue languageCodeValue = Optional.ofNullable(query.languageCode())
            .orElse(LanguageCodeValue.JA);
        Optional<LanguageCode> languageCodeOpt = ValueObjectHelper.validate(() -> new LanguageCode(languageCodeValue), errors);

        if (!errors.isEmpty()) {
            throw new InvalidDomainsException(errors);
        }

        LanguageCode languageCode = languageCodeOpt.orElseThrow();

        // 現在の統合済みのテーマを取得
        Pair<Theme, ThemeLocalization> integratedTheme = themeQueryService.getCurrentIntegratedByLanguageCode(languageCode)
            .orElseThrow(() -> new ResourceNotFoundException("domain.theme", "Current theme not found"));
        Theme theme = integratedTheme.getFirst();
        ThemeLocalization themeLocalization = integratedTheme.getSecond();

        // 開催中のテーマのお気に入り一覧を取得
        List<Fave> faves = faveQueryService.listApprovedByThemeId(theme.getId());

        // いいねしたお気に入りIDのセットを取得
        Set<UUID> likedFaveIds;
        if (query.userId().isPresent() && !faves.isEmpty()) {
            Set<FaveId> faveIds = faves.stream()
                .map(Fave::getId)
                .collect(Collectors.toSet());
            likedFaveIds = likeQueryService.listLikedFaveIdsByUserIdAndFaveIds(
                new UserId(query.userId().get()), faveIds);
        } else {
            likedFaveIds = Set.of();
        }

        return new CurrentThemeFavesResultDto(
            new ThemeSummaryResultDto(
                theme.getId().value(),
                themeLocalization.getTitle().value(),
                themeLocalization.getDescription().value(),
                theme.getType().value(),
                theme.getStartDate().value()
            ),
            faves.stream()
                .map(f -> new FaveSummaryResultDto(
                    f.getId().value(),
                    f.getContent().value(),
                    likedFaveIds.contains(f.getId().value()),
                    f.getCreatedAt(),
                    f.getUpdatedAt()
                ))
                .toList()
        );
    }
}
