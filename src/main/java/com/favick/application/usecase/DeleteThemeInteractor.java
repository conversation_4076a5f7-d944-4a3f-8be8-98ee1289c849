package com.favick.application.usecase;

import com.favick.application.dto.command.DeleteThemeCommandDto;
import com.favick.application.port.in.DeleteThemeUseCase;
import com.favick.application.service.theme.command.ThemeCommandService;
import com.favick.application.service.theme.query.ThemeQueryService;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.common.helper.ValueObjectHelper;
import com.favick.domain.exception.DomainException;
import com.favick.domain.feature.theme.model.Theme;
import com.favick.domain.feature.theme.model.ThemeId;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * テーマを削除するユースケースのインタラクター
 */
@Service
@RequiredArgsConstructor
@Transactional
public class DeleteThemeInteractor implements DeleteThemeUseCase {
    private final ThemeQueryService themeQueryService;
    private final ThemeCommandService themeCommandService;

    @Override
    public void handle(DeleteThemeCommandDto command) {
        List<DomainException> errors = new ArrayList<>();

        Optional<ThemeId> themeIdOpt = ValueObjectHelper.validate(() -> new ThemeId(command.id()), errors);

        if (!errors.isEmpty()) {
            throw new InvalidDomainsException(errors);
        }

        Theme theme = themeIdOpt
            .flatMap(themeQueryService::getById)
            .orElseThrow(() -> new ResourceNotFoundException("domain.theme", "ID: " + themeIdOpt.orElseThrow().value()));

        themeCommandService.delete(theme);
    }
}
