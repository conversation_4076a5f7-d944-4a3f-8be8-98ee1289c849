package com.favick.application.usecase;

import com.favick.application.dto.command.CreateThemeCommandDto;
import com.favick.application.port.in.CreateThemeUseCase;
import com.favick.application.service.theme.command.ThemeCommandService;
import com.favick.application.service.theme.query.ThemeQueryService;
import com.favick.application.service.theme_localization.command.ThemeContent;
import com.favick.application.service.theme_localization.command.ThemeLocalizationCommandService;
import com.favick.application.service.theme_localization.command.ThemeLocalizationValidator;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.helper.EntityHelper;
import com.favick.common.helper.ValueObjectHelper;
import com.favick.domain.exception.DomainException;
import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.theme.model.Theme;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.theme.model.ThemeStartDate;
import com.favick.domain.feature.theme.model.ThemeType;
import com.favick.domain.feature.theme.service.ThemeDomainService;
import com.favick.domain.feature.theme_localization.model.ThemeLocalization;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * テーマを作成するユースケースのインタラクター
 */
@Service
@RequiredArgsConstructor
@Transactional
public class CreateThemeInteractor implements CreateThemeUseCase {
    private final ThemeDomainService themeDomainService;
    private final ThemeQueryService themeQueryService;
    private final ThemeCommandService themeCommandService;
    private final ThemeLocalizationCommandService themeLocalizationCommandService;
    private final ThemeLocalizationValidator themeLocalizationValidator;

    @Override
    public void handle(CreateThemeCommandDto command) {
        List<DomainException> errors = new ArrayList<>();

        Map<LanguageCode, ThemeContent> validatedLocalizations = themeLocalizationValidator.validateLocalizations(command.localizations(), errors);
        Optional<ThemeType> themeTypeOpt = ValueObjectHelper.validate(() -> ThemeType.fromString(command.type()), errors);
        Optional<ThemeStartDate> themeStartDateOpt = ValueObjectHelper.validate(() -> new ThemeStartDate(command.startDate()), errors);

        themeStartDateOpt.ifPresent(themeStartDate -> ValueObjectHelper.validate(
            () -> themeDomainService.validateStartDateUniqueness(
                themeStartDate,
                themeQueryService::existsByStartDate
            ),
            errors
        ));

        Optional<Theme> themeOpt = themeTypeOpt.flatMap(themeType ->
            themeStartDateOpt.flatMap(themeStartDate ->
                EntityHelper.validate(
                    () -> Theme.create(themeType, themeStartDate),
                    errors
                )
            )
        );

        if (!errors.isEmpty()) {
            throw new InvalidDomainsException(errors);
        }

        Theme theme = themeOpt.orElseThrow();

        // テーマを作成
        ThemeId themeId = themeCommandService.create(theme);

        // ローカライゼーションを作成
        validatedLocalizations.forEach((languageCode, localizedContent) -> {
            ThemeLocalization themeLocalization = ThemeLocalization.create(
                themeId,
                languageCode,
                localizedContent.title(),
                localizedContent.description()
            );

            themeLocalizationCommandService.create(themeLocalization);
        });
    }
}
