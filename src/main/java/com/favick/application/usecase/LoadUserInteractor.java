package com.favick.application.usecase;

import com.favick.application.dto.query.LoadUserQueryDto;
import com.favick.application.dto.result.UserDetailsResultDto;
import com.favick.application.port.in.LoadUserUseCase;
import com.favick.application.service.rank.query.RankQueryService;
import com.favick.application.service.user.query.UserQueryService;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.common.helper.ValueObjectHelper;
import com.favick.domain.exception.DomainException;
import com.favick.domain.feature.rank.model.Rank;
import com.favick.domain.feature.user.model.User;
import com.favick.domain.feature.user.model.UserEmail;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * ユーザー（認証用）を読み込むユースケースのインタラクター
 */
@Service
@RequiredArgsConstructor
public class LoadUserInteractor implements LoadUserUseCase {
    private final UserQueryService userQueryService;
    private final RankQueryService rankQueryService;

    @Override
    public UserDetailsResultDto handle(LoadUserQueryDto query) {
        List<DomainException> errors = new ArrayList<>();

        Optional<UserEmail> emailOpt = ValueObjectHelper.validate(() -> new UserEmail(query.email()), errors);

        if (!errors.isEmpty()) {
            throw new InvalidDomainsException(errors);
        }

        User user = emailOpt
            .flatMap(userQueryService::getByEmail)
            .orElseThrow(() -> new ResourceNotFoundException("domain.user", "Email: " + emailOpt.orElseThrow().value()));

        Rank rank = Optional.of(user.getRankId())
            .flatMap(rankQueryService::getById)
            .orElseThrow(() -> new ResourceNotFoundException("domain.rank", "ID: " + user.getRankId().value()));

        return new UserDetailsResultDto(
            user.getId().value(),
            rank.getId().value(),
            user.getName().value(),
            rank.getName().value(),
            user.getEmail().value(),
            user.getPassword().value(),
            user.getEnabled().value(),
            user.getImageUrl().value(),
            user.getCreatedAt(),
            user.getUpdatedAt()
        );
    }
}
