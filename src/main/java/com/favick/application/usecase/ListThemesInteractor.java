package com.favick.application.usecase;

import com.favick.application.dto.query.ListThemesQueryDto;
import com.favick.application.dto.result.ThemeSummaryResultDto;
import com.favick.application.port.in.ListThemesUseCase;
import com.favick.application.service.theme.query.ThemeQueryService;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.helper.ValueObjectHelper;
import com.favick.domain.exception.DomainException;
import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import com.favick.domain.feature.theme.model.Theme;
import com.favick.domain.feature.theme_localization.model.ThemeLocalization;
import lombok.RequiredArgsConstructor;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * テーマ一覧を取得するユースケースのインタラクター
 */
@Service
@RequiredArgsConstructor
public class ListThemesInteractor implements ListThemesUseCase {
    private final ThemeQueryService themeQueryService;

    @Override
    public List<ThemeSummaryResultDto> handle(ListThemesQueryDto query) {
        List<DomainException> errors = new ArrayList<>();

        LanguageCodeValue languageCodeValue = Optional.ofNullable(query.languageCode())
            .orElse(LanguageCodeValue.JA);
        Optional<LanguageCode> languageCodeOpt = ValueObjectHelper.validate(() -> new LanguageCode(languageCodeValue), errors);

        if (!errors.isEmpty()) {
            throw new InvalidDomainsException(errors);
        }

        // テーマ一覧を取得
        LanguageCode languageCode = languageCodeOpt.orElseThrow();
        List<Pair<Theme, ThemeLocalization>> integratedThemes = themeQueryService.listIntegratedByLanguageCode(languageCode);

        return integratedThemes.stream()
            .map(pair -> {
                Theme theme = pair.getFirst();
                ThemeLocalization localization = pair.getSecond();

                return new ThemeSummaryResultDto(
                    theme.getId().value(),
                    localization.getTitle().value(),
                    localization.getDescription().value(),
                    theme.getType().value(),
                    theme.getStartDate().value()
                );
            })
            .toList();
    }
}
