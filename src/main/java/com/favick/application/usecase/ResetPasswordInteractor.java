package com.favick.application.usecase;

import com.favick.application.dto.command.ResetPasswordCommandDto;
import com.favick.application.port.in.ResetPasswordUseCase;
import com.favick.application.service.token.command.PasswordResetTokenCommandService;
import com.favick.application.service.token.query.PasswordResetTokenQueryService;
import com.favick.application.service.user.command.UserCommandService;
import com.favick.application.service.user.query.UserQueryService;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.common.helper.ValueObjectHelper;
import com.favick.domain.exception.DomainException;
import com.favick.domain.feature.token.model.PasswordResetToken;
import com.favick.domain.feature.token.model.TokenValue;
import com.favick.domain.feature.user.model.RawUserPassword;
import com.favick.domain.feature.user.model.User;
import com.favick.domain.feature.user.model.UserPassword;
import lombok.RequiredArgsConstructor;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * パスワードリセット実行ユースケースのインタラクター
 */
@Service
@RequiredArgsConstructor
@Transactional
public class ResetPasswordInteractor implements ResetPasswordUseCase {
    private final PasswordResetTokenQueryService passwordResetTokenQueryService;
    private final PasswordResetTokenCommandService passwordResetTokenCommandService;
    private final UserQueryService userQueryService;
    private final UserCommandService userCommandService;
    private final PasswordEncoder passwordEncoder;

    @Override
    public void handle(ResetPasswordCommandDto command) {
        List<DomainException> errors = new ArrayList<>();

        Optional<TokenValue> tokenValueOpt = ValueObjectHelper.validate(() -> new TokenValue(command.token()), errors);
        Optional<RawUserPassword> passwordOpt = ValueObjectHelper.validate(() -> new RawUserPassword(command.password()), errors);

        if (!errors.isEmpty()) {
            throw new InvalidDomainsException(errors);
        }

        PasswordResetToken passwordResetToken = tokenValueOpt
            .flatMap(passwordResetTokenQueryService::getByToken)
            .orElseThrow(() -> new ResourceNotFoundException("domain.token", "Token: " + tokenValueOpt.orElseThrow().value()));

        if (passwordResetToken.isExpired()) {
            throw new DomainException("error.expired", "Token");
        }

        User user = Optional.of(passwordResetToken.getUserId())
            .flatMap(userQueryService::getById)
            .orElseThrow(() -> new ResourceNotFoundException("domain.user", "ID: " + passwordResetToken.getUserId().value()));

        userCommandService.updatePassword(user, new UserPassword(passwordEncoder.encode(passwordOpt.orElseThrow().value())));

        // トークン削除
        passwordResetTokenCommandService.delete(passwordResetToken);
    }
}
