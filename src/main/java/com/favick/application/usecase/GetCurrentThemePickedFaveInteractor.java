package com.favick.application.usecase;

import com.favick.application.dto.query.GetCurrentFaveQueryDto;
import com.favick.application.dto.result.CurrentThemePickedFaveResultDto;
import com.favick.application.dto.result.PickedFaveResultDto;
import com.favick.application.dto.result.ThemeSummaryResultDto;
import com.favick.application.port.in.GetCurrentThemePickedFaveUseCase;
import com.favick.application.service.fave.query.FaveQueryService;
import com.favick.application.service.theme.query.ThemeQueryService;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.common.helper.ValueObjectHelper;
import com.favick.domain.exception.DomainException;
import com.favick.domain.feature.fave.model.Fave;
import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import com.favick.domain.feature.review.model.Review;
import com.favick.domain.feature.theme.model.Theme;
import com.favick.domain.feature.theme_localization.model.ThemeLocalization;
import com.favick.domain.feature.user.model.UserId;
import lombok.RequiredArgsConstructor;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 開催中のテーマと選択済みお気に入りを取得するユースケースのインタラクター
 */
@Service
@RequiredArgsConstructor
public class GetCurrentThemePickedFaveInteractor implements GetCurrentThemePickedFaveUseCase {
    private final ThemeQueryService themeQueryService;
    private final FaveQueryService faveQueryService;

    @Override
    public CurrentThemePickedFaveResultDto handle(GetCurrentFaveQueryDto query) {
        List<DomainException> errors = new ArrayList<>();

        Optional<UserId> userIdOpt = ValueObjectHelper.validate(() -> new UserId(query.userId()), errors);
        LanguageCodeValue languageCodeValue = Optional.ofNullable(query.languageCode())
            .orElse(LanguageCodeValue.JA);
        Optional<LanguageCode> languageCodeOpt = ValueObjectHelper.validate(() -> new LanguageCode(languageCodeValue), errors);

        if (!errors.isEmpty()) {
            throw new InvalidDomainsException(errors);
        }

        UserId userId = userIdOpt.orElseThrow();
        LanguageCode languageCode = languageCodeOpt.orElseThrow();

        // 現在の統合済みのテーマを取得
        Pair<Theme, ThemeLocalization> integratedTheme = themeQueryService.getCurrentIntegratedByLanguageCode(languageCode)
            .orElseThrow(() -> new ResourceNotFoundException("domain.theme", "Current theme not found"));
        Theme theme = integratedTheme.getFirst();
        ThemeLocalization themeLocalization = integratedTheme.getSecond();

        // 統合済みのお気に入りを取得
        Optional<Pair<Fave, Review>> integratedFaveOpt = faveQueryService.getIntegratedByUserIdAndThemeId(userId, theme.getId());

        return new CurrentThemePickedFaveResultDto(
            new ThemeSummaryResultDto(
                theme.getId().value(),
                themeLocalization.getTitle().value(),
                themeLocalization.getDescription().value(),
                theme.getType().value(),
                theme.getStartDate().value()
            ),
            integratedFaveOpt.map(pair -> {
                Fave fave = pair.getFirst();
                Review review = pair.getSecond();

                return new PickedFaveResultDto(
                    fave.getId().value(),
                    fave.getContent().value(),
                    review.getStatus().value(),
                    fave.getCreatedAt(),
                    fave.getUpdatedAt()
                );
            })
        );
    }
}
