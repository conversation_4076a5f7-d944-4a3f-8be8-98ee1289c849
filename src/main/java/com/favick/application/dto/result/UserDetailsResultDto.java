package com.favick.application.dto.result;

import com.favick.domain.feature.rank.model.RankNameValue;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * ユーザー詳細（認証用）リザルト
 */
public record UserDetailsResultDto(
    UUID id,
    UUID rankId,
    String name,
    RankNameValue rankName,
    String email,
    String password,
    boolean enabled,
    String imageUrl,
    LocalDateTime createdAt,
    LocalDateTime updatedAt
) {
}
