package com.favick.adapter.out.mail;

import com.favick.application.port.out.feature.token.repository.VerificationTokenRepository;
import com.favick.common.helper.MessageHelper;
import com.favick.domain.feature.token.model.VerificationToken;
import com.favick.domain.feature.user.event.UserRegisteredEvent;
import com.favick.domain.feature.user.model.UserId;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Component;

/**
 * ユーザー登録完了イベントリスナー
 * ユーザーが登録されたことを通知する
 */
@Component
@RequiredArgsConstructor
public class UserRegisteredEventListener {
    private final VerificationTokenRepository verificationTokenRepository;
    private final JavaMailSender javaMailSender;
    private final MessageHelper messageHelper;

    @Value("${spring.mail.sender}")
    private String senderAddress;

    /**
     * ユーザー登録完了イベントを処理する
     *
     * @param event ユーザー登録完了イベント
     */
    @EventListener
    public void handleUserRegisteredEvent(UserRegisteredEvent event) {
        UserId userId = new UserId(event.getUserId());

        // 既存トークンを削除し、新しい認証トークンを作成・保存
        verificationTokenRepository.deleteByUserId(userId);
        VerificationToken verificationToken = VerificationToken.create(userId);
        verificationTokenRepository.save(verificationToken);

        // メール送信
        String recipientAddress = event.getUserEmail();
        String subject = messageHelper.getMessage("mail.user_registration.subject");
        String confirmationUrl = event.getOrigin() + "/auth/register/verify?token=" + verificationToken.getToken().value();
        String message = messageHelper.getMessage("mail.user_registration.body");

        SimpleMailMessage mailMessage = new SimpleMailMessage();
        mailMessage.setFrom(senderAddress);
        mailMessage.setTo(recipientAddress);
        mailMessage.setSubject(subject);
        mailMessage.setText(message + "\n" + confirmationUrl);

        javaMailSender.send(mailMessage);
    }
}
