package com.favick.adapter.out.mail;

import com.favick.application.port.out.feature.token.repository.PasswordResetTokenRepository;
import com.favick.common.helper.MessageHelper;
import com.favick.domain.feature.token.model.PasswordResetToken;
import com.favick.domain.feature.user.event.PasswordResetEvent;
import com.favick.domain.feature.user.model.UserId;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Component;

/**
 * パスワードリセットイベントリスナー
 * ユーザーがパスワードリセットを要求したことを通知する
 */
@Component
@RequiredArgsConstructor
public class PasswordResetEventListener {
    private final PasswordResetTokenRepository passwordResetTokenRepository;
    private final JavaMailSender javaMailSender;
    private final MessageHelper messageHelper;

    @Value("${spring.mail.sender}")
    private String senderAddress;

    /**
     * パスワードリセットイベントを処理する
     *
     * @param event パスワードリセットイベント
     */
    @EventListener
    public void handlePasswordResetEvent(PasswordResetEvent event) {
        UserId userId = new UserId(event.getUserId());

        // 既存トークンを削除し、新しいパスワードリセットトークンを作成・保存
        passwordResetTokenRepository.deleteByUserId(userId);
        PasswordResetToken passwordResetToken = PasswordResetToken.create(userId);
        passwordResetTokenRepository.save(passwordResetToken);

        // メール送信
        String recipientAddress = event.getUserEmail();
        String subject = messageHelper.getMessage("mail.password_reset.subject");
        String confirmationUrl = event.getOrigin() + "/auth/password-reset/verify?token=" + passwordResetToken.getToken().value();
        String message = messageHelper.getMessage("mail.password_reset.body");

        SimpleMailMessage mailMessage = new SimpleMailMessage();
        mailMessage.setFrom(senderAddress);
        mailMessage.setTo(recipientAddress);
        mailMessage.setSubject(subject);
        mailMessage.setText(message + "\n" + confirmationUrl);

        javaMailSender.send(mailMessage);
    }
}
