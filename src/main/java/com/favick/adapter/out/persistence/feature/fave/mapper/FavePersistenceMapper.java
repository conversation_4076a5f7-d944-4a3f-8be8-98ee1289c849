package com.favick.adapter.out.persistence.feature.fave.mapper;

import com.favick.adapter.out.persistence.feature.fave.entity.FaveEntity;
import com.favick.adapter.out.persistence.feature.fave.projection.IntegratedFave;
import com.favick.domain.feature.fave.model.Fave;
import com.favick.domain.feature.fave.model.FaveContent;
import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.review.model.Review;
import com.favick.domain.feature.review.model.ReviewId;
import com.favick.domain.feature.review.model.ReviewStatus;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.user.model.UserId;
import org.springframework.data.util.Pair;

/**
 * お気に入りドメインモデルとエンティティのマッパー
 */
public class FavePersistenceMapper {
    /**
     * エンティティをドメインモデルに変換する
     *
     * @param entity エンティティ
     * @return ドメインモデル
     */
    public static Fave toDomain(FaveEntity entity) {
        return Fave.reconstruct(
            new FaveId(entity.getId()),
            new UserId(entity.getUserId()),
            new ThemeId(entity.getThemeId()),
            new FaveContent(entity.getContent()),
            entity.getCreatedAt(),
            entity.getUpdatedAt()
        );
    }

    /**
     * ドメインモデルをエンティティに変換する
     *
     * @param domain ドメインモデル
     * @return エンティティ
     */
    public static FaveEntity toEntity(Fave domain) {
        return FaveEntity.builder()
            .id(domain.getId().value())
            .userId(domain.getUserId().value())
            .themeId(domain.getThemeId().value())
            .content(domain.getContent().value())
            .createdAt(domain.getCreatedAt())
            .updatedAt(domain.getUpdatedAt())
            .build();
    }

    /**
     * 統合Projectionをドメインモデルに変換する
     *
     * @param projection 統合Projection
     * @return ドメインモデル
     */
    public static Pair<Fave, Review> toIntegratedDomain(IntegratedFave projection) {
        return Pair.of(
            Fave.reconstruct(
                new FaveId(projection.getFaveId()),
                new UserId(projection.getUserId()),
                new ThemeId(projection.getThemeId()),
                new FaveContent(projection.getContent()),
                projection.getFaveCreatedAt(),
                projection.getFaveUpdatedAt()
            ),
            Review.reconstruct(
                new ReviewId(projection.getReviewId()),
                new FaveId(projection.getFaveId()),
                new ReviewStatus(projection.getReviewStatus()),
                projection.getReviewCreatedAt(),
                projection.getReviewUpdatedAt()
            )
        );
    }
}
