package com.favick.adapter.out.persistence.feature.rank.repository;

import com.favick.adapter.out.persistence.feature.rank.mapper.RankPersistenceMapper;
import com.favick.application.port.out.feature.rank.repository.RankRepository;
import com.favick.domain.feature.rank.model.Rank;
import com.favick.domain.feature.rank.model.RankId;
import com.favick.domain.feature.rank.model.RankName;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * ランクリポジトリ実装
 * ランクの永続化を担当する
 */
@Repository
@RequiredArgsConstructor
public class RankRepositoryImpl implements RankRepository {
    private final RankJpaRepository rankJpaRepository;

    @Override
    public Optional<Rank> findById(RankId id) {
        return rankJpaRepository.findById(id.value())
            .map(RankPersistenceMapper::toDomain);
    }

    @Override
    public Optional<Rank> findByName(RankName name) {
        return rankJpaRepository.findByName(name.value())
            .map(RankPersistenceMapper::toDomain);
    }
}
