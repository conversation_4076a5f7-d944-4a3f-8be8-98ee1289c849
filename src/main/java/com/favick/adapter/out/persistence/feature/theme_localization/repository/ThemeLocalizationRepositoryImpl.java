package com.favick.adapter.out.persistence.feature.theme_localization.repository;

import com.favick.adapter.out.persistence.feature.theme_localization.mapper.ThemeLocalizationPersistenceMapper;
import com.favick.application.port.out.feature.theme_localization.repository.ThemeLocalizationRepository;
import com.favick.domain.feature.theme_localization.model.ThemeLocalization;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * テーマローカライゼーションリポジトリ実装
 * テーマローカライゼーションの永続化を担当する
 */
@Repository
@RequiredArgsConstructor
public class ThemeLocalizationRepositoryImpl implements ThemeLocalizationRepository {
    private final ThemeLocalizationJpaRepository themeLocalizationJpaRepository;

    @Override
    public void save(ThemeLocalization themeLocalization) {
        themeLocalizationJpaRepository.save(
            ThemeLocalizationPersistenceMapper.toEntity(themeLocalization)
        );
    }
}
