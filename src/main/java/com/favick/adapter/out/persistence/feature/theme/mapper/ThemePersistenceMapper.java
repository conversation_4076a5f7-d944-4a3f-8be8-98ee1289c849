package com.favick.adapter.out.persistence.feature.theme.mapper;

import com.favick.adapter.out.persistence.feature.theme.entity.ThemeEntity;
import com.favick.adapter.out.persistence.feature.theme.projection.IntegratedTheme;
import com.favick.adapter.out.persistence.feature.theme.projection.IntegratedThemeMultiLang;
import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import com.favick.domain.feature.theme.model.Theme;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.theme.model.ThemeStartDate;
import com.favick.domain.feature.theme.model.ThemeType;
import com.favick.domain.feature.theme_localization.model.ThemeDescription;
import com.favick.domain.feature.theme_localization.model.ThemeLocalization;
import com.favick.domain.feature.theme_localization.model.ThemeLocalizationId;
import com.favick.domain.feature.theme_localization.model.ThemeTitle;
import org.springframework.data.util.Pair;

import java.util.Map;

/**
 * テーマドメインモデルとエンティティのマッパー
 */
public class ThemePersistenceMapper {
    /**
     * エンティティをドメインモデルに変換する
     *
     * @param entity エンティティ
     * @return ドメインモデル
     */
    public static Theme toDomain(ThemeEntity entity) {
        return Theme.reconstruct(
            new ThemeId(entity.getId()),
            new ThemeType(entity.getType()),
            new ThemeStartDate(entity.getStartDate()),
            entity.getCreatedAt(),
            entity.getUpdatedAt()
        );
    }

    /**
     * ドメインモデルをエンティティに変換する
     *
     * @param domain ドメインモデル
     * @return エンティティ
     */
    public static ThemeEntity toEntity(Theme domain) {
        return ThemeEntity.builder()
            .id(domain.getId().value())
            .type(domain.getType().value())
            .startDate(domain.getStartDate().value())
            .createdAt(domain.getCreatedAt())
            .updatedAt(domain.getUpdatedAt())
            .build();
    }

    /**
     * 統合Projectionをドメインモデルに変換する
     *
     * @param projection 統合Projection
     * @return ドメインモデル
     */
    public static Pair<Theme, ThemeLocalization> toIntegratedDomain(IntegratedTheme projection) {
        return Pair.of(
            Theme.reconstruct(
                new ThemeId(projection.getThemeId()),
                new ThemeType(projection.getType()),
                new ThemeStartDate(projection.getStartDate()),
                projection.getThemeCreatedAt(),
                projection.getThemeUpdatedAt()
            ),
            ThemeLocalization.reconstruct(
                new ThemeLocalizationId(projection.getThemeLocalizationId()),
                new ThemeId(projection.getThemeId()),
                new LanguageCode(projection.getLanguageCode()),
                new ThemeTitle(projection.getTitle()),
                new ThemeDescription(projection.getDescription()),
                projection.getLocalizationCreatedAt(),
                projection.getLocalizationUpdatedAt()
            )
        );
    }

    /**
     * 多言語統合Projectionをドメインモデルに変換する
     *
     * @param projection 多言語統合Projection
     * @return ドメインモデル
     */
    public static Pair<Theme, Map<LanguageCode, ThemeLocalization>> toIntegratedMultiLangDomain(IntegratedThemeMultiLang projection) {
        Theme theme = Theme.reconstruct(
            new ThemeId(projection.getThemeId()),
            new ThemeType(projection.getType()),
            new ThemeStartDate(projection.getStartDate()),
            projection.getThemeCreatedAt(),
            projection.getThemeUpdatedAt()
        );

        ThemeLocalization localizationJa = ThemeLocalization.reconstruct(
            new ThemeLocalizationId(projection.getThemeLocalizationJaId()),
            new ThemeId(projection.getThemeId()),
            new LanguageCode(LanguageCodeValue.JA),
            new ThemeTitle(projection.getTitleJa()),
            new ThemeDescription(projection.getDescriptionJa()),
            projection.getLocalizationJaCreatedAt(),
            projection.getLocalizationJaUpdatedAt()
        );

        ThemeLocalization localizationEn = ThemeLocalization.reconstruct(
            new ThemeLocalizationId(projection.getThemeLocalizationEnId()),
            new ThemeId(projection.getThemeId()),
            new LanguageCode(LanguageCodeValue.EN),
            new ThemeTitle(projection.getTitleEn()),
            new ThemeDescription(projection.getDescriptionEn()),
            projection.getLocalizationEnCreatedAt(),
            projection.getLocalizationEnUpdatedAt()
        );

        Map<LanguageCode, ThemeLocalization> localizations = Map.of(
            new LanguageCode(LanguageCodeValue.JA), localizationJa,
            new LanguageCode(LanguageCodeValue.EN), localizationEn
        );

        return Pair.of(theme, localizations);
    }
}
