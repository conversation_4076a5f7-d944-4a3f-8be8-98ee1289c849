package com.favick.adapter.out.persistence.feature.rank.mapper;

import com.favick.adapter.out.persistence.feature.rank.entity.RankEntity;
import com.favick.domain.feature.rank.model.Rank;
import com.favick.domain.feature.rank.model.RankId;
import com.favick.domain.feature.rank.model.RankName;

/**
 * ランクドメインモデルとエンティティのマッパー
 */
public class RankPersistenceMapper {
    /**
     * エンティティをドメインモデルに変換する
     *
     * @param entity エンティティ
     * @return ドメインモデル
     */
    public static Rank toDomain(RankEntity entity) {
        return Rank.reconstruct(
            new RankId(entity.getId()),
            new RankName(entity.getName()),
            entity.getCreatedAt(),
            entity.getUpdatedAt()
        );
    }

    /**
     * ドメインモデルをエンティティに変換する
     *
     * @param domain ドメインモデル
     * @return エンティティ
     */
    public static RankEntity toEntity(Rank domain) {
        return RankEntity.builder()
            .id(domain.getId().value())
            .name(domain.getName().value())
            .createdAt(domain.getCreatedAt())
            .updatedAt(domain.getUpdatedAt())
            .build();
    }
}
