package com.favick.adapter.out.persistence.feature.like.repository;

import com.favick.adapter.out.persistence.feature.like.entity.LikeEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

/**
 * いいねJPAリポジトリ
 * いいねエンティティの永続化を担当する
 */
public interface LikeJpaRepository extends JpaRepository<LikeEntity, UUID> {
    /**
     * ユーザーIDとFaveIDでいいねを取得する
     *
     * @param userId ユーザーID
     * @param faveId FaveID
     * @return いいね
     */
    Optional<LikeEntity> findByUserIdAndFaveId(UUID userId, UUID faveId);

    /**
     * ユーザーIDと複数のFaveIDでいいね一覧を取得する
     *
     * @param userId  ユーザーID
     * @param faveIds FaveIDのセット
     * @return いいね一覧
     */
    List<LikeEntity> findByUserIdAndFaveIdIn(UUID userId, Set<UUID> faveIds);
}
