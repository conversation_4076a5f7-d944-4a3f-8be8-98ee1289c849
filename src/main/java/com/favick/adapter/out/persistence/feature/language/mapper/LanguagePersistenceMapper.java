package com.favick.adapter.out.persistence.feature.language.mapper;

import com.favick.adapter.out.persistence.feature.language.entity.LanguageEntity;
import com.favick.domain.feature.language.model.Language;
import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.language.model.LanguageDisplayName;
import com.favick.domain.feature.language.model.LanguageId;

/**
 * 言語ドメインモデルとエンティティのマッパー
 */
public class LanguagePersistenceMapper {
    /**
     * エンティティをドメインモデルに変換する
     *
     * @param entity エンティティ
     * @return ドメインモデル
     */
    public static Language toDomain(LanguageEntity entity) {
        return Language.reconstruct(
            new LanguageId(entity.getId()),
            new LanguageCode(entity.getCode()),
            new LanguageDisplayName(entity.getDisplayName()),
            entity.getCreatedAt(),
            entity.getUpdatedAt()
        );
    }

    /**
     * ドメインモデルをエンティティに変換する
     *
     * @param domain ドメインモデル
     * @return エンティティ
     */
    public static LanguageEntity toEntity(Language domain) {
        return LanguageEntity.builder()
            .id(domain.getId().value())
            .code(domain.getCode().value())
            .displayName(domain.getDisplayName().value())
            .createdAt(domain.getCreatedAt())
            .updatedAt(domain.getUpdatedAt())
            .build();
    }
}
