package com.favick.adapter.out.persistence.feature.fave.projection;

import com.favick.domain.feature.review.model.ReviewStatusValue;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * お気に入りの結合Projection
 */
public interface IntegratedFave {
    // Fave
    UUID getFaveId();

    UUID getUserId();

    UUID getThemeId();

    String getContent();

    LocalDateTime getFaveCreatedAt();

    LocalDateTime getFaveUpdatedAt();

    // Review
    UUID getReviewId();

    ReviewStatusValue getReviewStatus();

    LocalDateTime getReviewCreatedAt();

    LocalDateTime getReviewUpdatedAt();
}
