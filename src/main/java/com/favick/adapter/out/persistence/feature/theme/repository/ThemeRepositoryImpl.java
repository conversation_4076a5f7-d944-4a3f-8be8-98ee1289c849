package com.favick.adapter.out.persistence.feature.theme.repository;

import com.favick.adapter.out.persistence.feature.theme.entity.ThemeEntity;
import com.favick.adapter.out.persistence.feature.theme.mapper.ThemePersistenceMapper;
import com.favick.application.port.out.feature.theme.repository.ThemeRepository;
import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.theme.model.Theme;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.theme.model.ThemeStartDate;
import com.favick.domain.feature.theme_localization.model.ThemeLocalization;
import lombok.RequiredArgsConstructor;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * テーマリポジトリ実装
 * テーマの永続化を担当する
 */
@Repository
@RequiredArgsConstructor
public class ThemeRepositoryImpl implements ThemeRepository {
    private final ThemeJpaRepository themeJpaRepository;

    @Override
    public List<Pair<Theme, ThemeLocalization>> findAllIntegratedByLanguageCode(LanguageCode languageCode) {
        return themeJpaRepository.findAllIntegratedByLanguageCode(languageCode.value())
            .stream()
            .map(ThemePersistenceMapper::toIntegratedDomain)
            .toList();
    }

    @Override
    public Optional<Pair<Theme, ThemeLocalization>> findCurrentIntegratedByLanguageCode(LanguageCode languageCode) {
        return themeJpaRepository.findCurrentIntegratedByLanguageCode(LocalDate.now(), languageCode.value())
            .map(ThemePersistenceMapper::toIntegratedDomain);
    }

    @Override
    public Optional<Pair<Theme, ThemeLocalization>> findIntegratedByIdAndLanguageCode(ThemeId themeId, LanguageCode languageCode) {
        return themeJpaRepository.findIntegratedByIdAndLanguageCode(themeId.value(), languageCode.value())
            .map(ThemePersistenceMapper::toIntegratedDomain);
    }

    @Override
    public Optional<Pair<Theme, Map<LanguageCode, ThemeLocalization>>> findIntegratedMultiLangById(ThemeId themeId) {
        return themeJpaRepository.findIntegratedMultiLangById(themeId.value())
            .map(ThemePersistenceMapper::toIntegratedMultiLangDomain);
    }

    @Override
    public Optional<Theme> findCurrentTheme() {
        return themeJpaRepository.findCurrentTheme(LocalDate.now())
            .map(ThemePersistenceMapper::toDomain);
    }

    @Override
    public Optional<Theme> findById(ThemeId id) {
        return themeJpaRepository.findById(id.value())
            .map(ThemePersistenceMapper::toDomain);
    }
    
    @Override
    public void save(Theme theme) {
        ThemeEntity entity = ThemePersistenceMapper.toEntity(theme);
        themeJpaRepository.save(entity);
    }

    @Override
    public void delete(ThemeId id) {
        themeJpaRepository.deleteById(id.value());
    }

    @Override
    public boolean existsByStartDate(ThemeStartDate startDate) {
        return themeJpaRepository.existsByStartDate(startDate.value());
    }

    @Override
    public boolean existsByStartDateExcludingId(ThemeStartDate startDate, ThemeId excludeId) {
        return themeJpaRepository.existsByStartDateAndIdNot(startDate.value(), excludeId.value());
    }
}
