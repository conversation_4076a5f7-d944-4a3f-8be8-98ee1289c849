package com.favick.adapter.out.persistence.feature.admin.repository;

import com.favick.adapter.out.persistence.feature.admin.entity.AdminEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;
import java.util.UUID;

/**
 * 管理者JPAリポジトリ
 * 管理者エンティティの永続化を担当する
 */
public interface AdminJpaRepository extends JpaRepository<AdminEntity, UUID> {
    /**
     * メールアドレスが一致する管理者を取得する
     *
     * @param email 検索対象のメールアドレス
     * @return メールアドレスが一致する管理者
     */
    Optional<AdminEntity> findByEmail(String email);
}
