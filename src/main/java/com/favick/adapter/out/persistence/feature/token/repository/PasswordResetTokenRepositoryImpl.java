package com.favick.adapter.out.persistence.feature.token.repository;

import com.favick.adapter.out.persistence.feature.token.entity.PasswordResetTokenEntity;
import com.favick.adapter.out.persistence.feature.token.mapper.PasswordResetTokenPersistenceMapper;
import com.favick.application.port.out.feature.token.repository.PasswordResetTokenRepository;
import com.favick.domain.feature.token.model.PasswordResetToken;
import com.favick.domain.feature.token.model.PasswordResetTokenId;
import com.favick.domain.feature.token.model.TokenValue;
import com.favick.domain.feature.user.model.UserId;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * パスワードリセットトークンリポジトリ実装
 * パスワードリセットトークンの永続化を担当する
 */
@Repository
@RequiredArgsConstructor
public class PasswordResetTokenRepositoryImpl implements PasswordResetTokenRepository {
    private final PasswordResetTokenJpaRepository passwordResetTokenJpaRepository;

    @Override
    public void save(PasswordResetToken passwordResetToken) {
        PasswordResetTokenEntity entity = PasswordResetTokenPersistenceMapper.toEntity(passwordResetToken);
        passwordResetTokenJpaRepository.save(entity);
    }

    @Override
    public Optional<PasswordResetToken> findById(PasswordResetTokenId id) {
        return passwordResetTokenJpaRepository.findById(id.value())
            .map(PasswordResetTokenPersistenceMapper::toDomain);
    }

    @Override
    public Optional<PasswordResetToken> findByToken(TokenValue token) {
        return passwordResetTokenJpaRepository.findByToken(token.value())
            .map(PasswordResetTokenPersistenceMapper::toDomain);
    }

    @Override
    public Optional<PasswordResetToken> findByUserId(UserId userId) {
        return passwordResetTokenJpaRepository.findByUserId(userId.value())
            .map(PasswordResetTokenPersistenceMapper::toDomain);
    }

    @Override
    public void delete(PasswordResetTokenId id) {
        passwordResetTokenJpaRepository.deleteById(id.value());
    }

    @Override
    public void deleteByUserId(UserId userId) {
        passwordResetTokenJpaRepository.deleteByUserId(userId.value());
    }
}
