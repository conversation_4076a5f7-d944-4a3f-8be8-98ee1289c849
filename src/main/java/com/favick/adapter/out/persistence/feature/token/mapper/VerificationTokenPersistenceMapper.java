package com.favick.adapter.out.persistence.feature.token.mapper;

import com.favick.adapter.out.persistence.feature.token.entity.VerificationTokenEntity;
import com.favick.domain.feature.token.model.TokenExpireAt;
import com.favick.domain.feature.token.model.TokenValue;
import com.favick.domain.feature.token.model.VerificationToken;
import com.favick.domain.feature.token.model.VerificationTokenId;
import com.favick.domain.feature.user.model.UserId;

/**
 * 認証トークンドメインモデルとエンティティのマッパー
 */
public class VerificationTokenPersistenceMapper {
    /**
     * エンティティをドメインモデルに変換する
     *
     * @param entity エンティティ
     * @return ドメインモデル
     */
    public static VerificationToken toDomain(VerificationTokenEntity entity) {
        return VerificationToken.reconstruct(
            new VerificationTokenId(entity.getId()),
            new UserId(entity.getUserId()),
            new TokenValue(entity.getToken()),
            new TokenExpireAt(entity.getExpireAt()),
            entity.getCreatedAt()
        );
    }

    /**
     * ドメインモデルをエンティティに変換する
     *
     * @param domain ドメインモデル
     * @return エンティティ
     */
    public static VerificationTokenEntity toEntity(VerificationToken domain) {
        return VerificationTokenEntity.builder()
            .id(domain.getId().value())
            .userId(domain.getUserId().value())
            .token(domain.getToken().value())
            .expireAt(domain.getExpireAt().value())
            .createdAt(domain.getCreatedAt())
            .build();
    }
}
