package com.favick.adapter.out.persistence.feature.review.repository;

import com.favick.adapter.out.persistence.feature.review.entity.ReviewEntity;
import com.favick.domain.feature.review.model.ReviewStatusValue;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 審査JPAリポジトリ
 * 審査エンティティの永続化を担当する
 */
public interface ReviewJpaRepository extends JpaRepository<ReviewEntity, UUID> {
    /**
     * お気に入りIDで審査を取得する
     *
     * @param faveId お気に入りID
     * @return お気に入りIDが一致する審査
     */
    Optional<ReviewEntity> findByFaveId(UUID faveId);

    /**
     * ステータスで審査一覧を取得する（更新日時の新しい順）
     *
     * @param status ステータス
     * @return ステータスが一致する審査一覧
     */
    List<ReviewEntity> findByStatusOrderByUpdatedAtDesc(ReviewStatusValue status);

    /**
     * 管理者IDで審査一覧を取得する（更新日時の新しい順）
     *
     * @param adminId 管理者ID
     * @return 管理者IDが一致する審査一覧
     */
    List<ReviewEntity> findByAdminIdOrderByUpdatedAtDesc(UUID adminId);

    /**
     * ステータスと管理者IDで審査一覧を取得する（更新日時の新しい順）
     *
     * @param status  ステータス
     * @param adminId 管理者ID
     * @return ステータスと管理者IDが一致する審査一覧
     */
    List<ReviewEntity> findByStatusAndAdminIdOrderByUpdatedAtDesc(ReviewStatusValue status, UUID adminId);
}
