package com.favick.adapter.out.persistence.feature.theme.projection;

import com.favick.domain.feature.language.model.LanguageCodeValue;
import com.favick.domain.feature.theme.model.ThemeTypeValue;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * テーマの結合Projection
 */
public interface IntegratedTheme {
    // Theme
    UUID getThemeId();

    ThemeTypeValue getType();

    LocalDate getStartDate();

    LocalDateTime getThemeCreatedAt();

    LocalDateTime getThemeUpdatedAt();

    // ThemeLocalization
    UUID getThemeLocalizationId();

    String getTitle();

    String getDescription();

    LanguageCodeValue getLanguageCode();

    LocalDateTime getLocalizationCreatedAt();

    LocalDateTime getLocalizationUpdatedAt();
}
