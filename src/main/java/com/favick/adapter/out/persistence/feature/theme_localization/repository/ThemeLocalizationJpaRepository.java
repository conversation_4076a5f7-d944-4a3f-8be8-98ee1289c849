package com.favick.adapter.out.persistence.feature.theme_localization.repository;

import com.favick.adapter.out.persistence.feature.theme_localization.entity.ThemeLocalizationEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.UUID;

/**
 * テーマローカライゼーションJPAリポジトリ
 * テーマローカライゼーションエンティティの永続化を担当する
 */
public interface ThemeLocalizationJpaRepository extends JpaRepository<ThemeLocalizationEntity, UUID> {
}
