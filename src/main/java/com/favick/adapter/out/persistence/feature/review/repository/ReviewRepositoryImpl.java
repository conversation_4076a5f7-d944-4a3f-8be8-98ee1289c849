package com.favick.adapter.out.persistence.feature.review.repository;

import com.favick.adapter.out.persistence.feature.review.entity.ReviewEntity;
import com.favick.adapter.out.persistence.feature.review.mapper.ReviewPersistenceMapper;
import com.favick.application.port.out.feature.review.repository.ReviewRepository;
import com.favick.domain.feature.review.model.Review;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * 審査リポジトリ実装
 * 審査の永続化を担当する
 */
@Repository
@RequiredArgsConstructor
public class ReviewRepositoryImpl implements ReviewRepository {
    private final ReviewJpaRepository reviewJpaRepository;

    @Override
    public void save(Review review) {
        ReviewEntity entity = ReviewPersistenceMapper.toEntity(review);
        reviewJpaRepository.save(entity);
    }
}
