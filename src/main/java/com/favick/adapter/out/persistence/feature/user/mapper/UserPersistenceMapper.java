package com.favick.adapter.out.persistence.feature.user.mapper;

import com.favick.adapter.out.persistence.feature.user.entity.UserEntity;
import com.favick.domain.feature.rank.model.RankId;
import com.favick.domain.feature.user.model.*;

/**
 * ユーザードメインモデルとエンティティのマッパー
 */
public class UserPersistenceMapper {
    /**
     * エンティティをドメインモデルに変換する
     *
     * @param entity エンティティ
     * @return ドメインモデル
     */
    public static User toDomain(UserEntity entity) {
        return User.reconstruct(
            new UserId(entity.getId()),
            new RankId(entity.getRankId()),
            new UserName(entity.getName()),
            new UserEmail(entity.getEmail()),
            new UserPassword(entity.getPassword()),
            new UserEnabled(entity.getEnabled()),
            new UserImageUri(entity.getImageUrl()),
            entity.getCreatedAt(),
            entity.getUpdatedAt()
        );
    }

    /**
     * ドメインモデルをエンティティに変換する
     *
     * @param domain ドメインモデル
     * @return エンティティ
     */
    public static UserEntity toEntity(User domain) {
        return UserEntity.builder()
            .id(domain.getId().value())
            .rankId(domain.getRankId().value())
            .name(domain.getName().value())
            .email(domain.getEmail().value())
            .password(domain.getPassword().value())
            .enabled(domain.getEnabled().value())
            .imageUrl(domain.getImageUrl().value())
            .createdAt(domain.getCreatedAt())
            .updatedAt(domain.getUpdatedAt())
            .build();
    }
}
