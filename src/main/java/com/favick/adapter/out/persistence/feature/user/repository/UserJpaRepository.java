package com.favick.adapter.out.persistence.feature.user.repository;

import com.favick.adapter.out.persistence.feature.user.entity.UserEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;
import java.util.UUID;

/**
 * ユーザーJPAリポジトリ
 * ユーザーエンティティの永続化を担当する
 */
public interface UserJpaRepository extends JpaRepository<UserEntity, UUID> {
    /**
     * メールアドレスが一致するユーザーを取得する
     *
     * @param email 検索対象のメールアドレス
     * @return メールアドレスが一致するユーザー
     */
    Optional<UserEntity> findByEmail(String email);

    /**
     * メールアドレスが一致するユーザーが存在するかを判定する
     *
     * @param email 検索対象のメールアドレス
     * @return メールアドレスが一致するユーザーが存在する場合true、存在しない場合false
     */
    boolean existsByEmail(String email);
}
