package com.favick.adapter.out.persistence.feature.like.repository;

import com.favick.adapter.out.persistence.feature.like.entity.LikeEntity;
import com.favick.adapter.out.persistence.feature.like.mapper.LikePersistenceMapper;
import com.favick.application.port.out.feature.like.repository.LikeRepository;
import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.like.model.Like;
import com.favick.domain.feature.like.model.LikeId;
import com.favick.domain.feature.user.model.UserId;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * いいねリポジトリ実装
 * いいねの永続化を担当する
 */
@Repository
@RequiredArgsConstructor
public class LikeRepositoryImpl implements LikeRepository {
    private final LikeJpaRepository likeJpaRepository;

    @Override
    public Optional<Like> findByUserIdAndFaveId(UserId userId, FaveId faveId) {
        return likeJpaRepository.findByUserIdAndFaveId(userId.value(), faveId.value())
            .map(LikePersistenceMapper::toDomain);
    }

    @Override
    public List<Like> findByUserIdAndFaveIds(UserId userId, Set<FaveId> faveIds) {
        if (faveIds.isEmpty()) {
            return List.of();
        }

        Set<UUID> faveIdValues = faveIds.stream()
            .map(FaveId::value)
            .collect(Collectors.toSet());

        return likeJpaRepository.findByUserIdAndFaveIdIn(userId.value(), faveIdValues)
            .stream()
            .map(LikePersistenceMapper::toDomain)
            .toList();
    }

    @Override
    public void save(Like like) {
        LikeEntity entity = LikePersistenceMapper.toEntity(like);
        likeJpaRepository.save(entity);
    }

    @Override
    public void delete(LikeId id) {
        likeJpaRepository.deleteById(id.value());
    }
}
