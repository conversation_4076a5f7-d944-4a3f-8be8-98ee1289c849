package com.favick.adapter.out.persistence.feature.role.repository;

import com.favick.adapter.out.persistence.feature.role.mapper.RolePersistenceMapper;
import com.favick.application.port.out.feature.role.repository.RoleRepository;
import com.favick.domain.feature.role.model.Role;
import com.favick.domain.feature.role.model.RoleId;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 権限リポジトリ実装
 * 権限の永続化を担当する
 */
@Repository
@RequiredArgsConstructor
public class RoleRepositoryImpl implements RoleRepository {
    private final RoleJpaRepository roleJpaRepository;

    @Override
    public Optional<Role> findById(RoleId id) {
        return roleJpaRepository.findById(id.value())
            .map(RolePersistenceMapper::toDomain);
    }
}
