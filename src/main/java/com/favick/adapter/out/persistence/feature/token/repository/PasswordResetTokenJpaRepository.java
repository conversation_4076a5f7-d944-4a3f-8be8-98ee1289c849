package com.favick.adapter.out.persistence.feature.token.repository;

import com.favick.adapter.out.persistence.feature.token.entity.PasswordResetTokenEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;
import java.util.UUID;

/**
 * パスワードリセットトークンJPAリポジトリ
 * パスワードリセットトークンエンティティの永続化を担当する
 */
public interface PasswordResetTokenJpaRepository extends JpaRepository<PasswordResetTokenEntity, UUID> {
    /**
     * トークン値が一致するパスワードリセットトークンを取得する
     *
     * @param token 検索対象のトークン値
     * @return トークン値が一致するパスワードリセットトークン
     */
    Optional<PasswordResetTokenEntity> findByToken(String token);

    /**
     * ユーザーIDが一致するパスワードリセットトークンを取得する
     *
     * @param userId 検索対象のユーザーID
     * @return ユーザーIDが一致するパスワードリセットトークン
     */
    Optional<PasswordResetTokenEntity> findByUserId(UUID userId);

    /**
     * ユーザーIDが一致するパスワードリセットトークンを削除する
     *
     * @param userId 削除対象のユーザーID
     */
    void deleteByUserId(UUID userId);
}
