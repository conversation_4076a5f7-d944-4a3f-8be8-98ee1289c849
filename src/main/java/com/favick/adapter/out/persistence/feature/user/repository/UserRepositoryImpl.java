package com.favick.adapter.out.persistence.feature.user.repository;

import com.favick.adapter.out.persistence.feature.user.entity.UserEntity;
import com.favick.adapter.out.persistence.feature.user.mapper.UserPersistenceMapper;
import com.favick.application.port.out.feature.user.repository.UserRepository;
import com.favick.domain.feature.user.model.User;
import com.favick.domain.feature.user.model.UserEmail;
import com.favick.domain.feature.user.model.UserId;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * ユーザーリポジトリ実装
 * ユーザーの永続化を担当する
 */
@Repository
@RequiredArgsConstructor
public class UserRepositoryImpl implements UserRepository {
    private final UserJpaRepository userJpaRepository;

    @Override
    public void save(User user) {
        UserEntity entity = UserPersistenceMapper.toEntity(user);
        userJpaRepository.save(entity);
    }

    @Override
    public Optional<User> findById(UserId id) {
        return userJpaRepository.findById(id.value())
            .map(UserPersistenceMapper::toDomain);
    }

    @Override
    public Optional<User> findByEmail(UserEmail email) {
        return userJpaRepository.findByEmail(email.value())
            .map(UserPersistenceMapper::toDomain);
    }

    @Override
    public boolean existsByEmail(UserEmail email) {
        return userJpaRepository.existsByEmail(email.value());
    }
}
