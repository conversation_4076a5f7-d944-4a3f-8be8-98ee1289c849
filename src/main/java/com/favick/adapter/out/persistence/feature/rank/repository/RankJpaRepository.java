package com.favick.adapter.out.persistence.feature.rank.repository;

import com.favick.adapter.out.persistence.feature.rank.entity.RankEntity;
import com.favick.domain.feature.rank.model.RankNameValue;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;
import java.util.UUID;

/**
 * ランクJPAリポジトリ
 * ランクエンティティの永続化を担当する
 */
public interface RankJpaRepository extends JpaRepository<RankEntity, UUID> {
    /**
     * ランク名が一致するランクを取得する
     *
     * @param name 検索対象のランク名
     * @return 名前が一致するランク
     */
    Optional<RankEntity> findByName(RankNameValue name);
}
