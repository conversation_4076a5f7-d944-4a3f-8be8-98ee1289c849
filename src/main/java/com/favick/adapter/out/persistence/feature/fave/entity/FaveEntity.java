package com.favick.adapter.out.persistence.feature.fave.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * お気に入りエンティティ
 * お気に入り情報の永続化を担当する
 */
@Entity
@Table(name = "faves")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FaveEntity {
    @Id
    @Column(name = "id", nullable = false, updatable = false)
    private UUID id;

    @Column(name = "user_id", nullable = false)
    private UUID userId;

    @Column(name = "theme_id", nullable = false)
    private UUID themeId;

    @Column(name = "content", nullable = false)
    @Size(max = 255)
    @NotNull
    private String content;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
}
