package com.favick.adapter.out.persistence.feature.admin.mapper;

import com.favick.adapter.out.persistence.feature.admin.entity.AdminEntity;
import com.favick.domain.feature.admin.model.*;
import com.favick.domain.feature.role.model.RoleId;

/**
 * 管理者ドメインモデルとエンティティのマッパー
 */
public class AdminPersistenceMapper {
    /**
     * エンティティをドメインモデルに変換する
     *
     * @param entity エンティティ
     * @return ドメインモデル
     */
    public static Admin toDomain(AdminEntity entity) {
        return Admin.reconstruct(
            new AdminId(entity.getId()),
            new RoleId(entity.getRoleId()),
            new AdminName(entity.getName()),
            new AdminEmail(entity.getEmail()),
            new AdminPassword(entity.getPassword()),
            entity.getCreatedAt(),
            entity.getUpdatedAt()
        );
    }

    /**
     * ドメインモデルをエンティティに変換する
     *
     * @param domain ドメインモデル
     * @return エンティティ
     */
    public static AdminEntity toEntity(Admin domain) {
        return AdminEntity.builder()
            .id(domain.getId().value())
            .roleId(domain.getRoleId().value())
            .name(domain.getName().value())
            .email(domain.getEmail().value())
            .password(domain.getPassword().value())
            .createdAt(domain.getCreatedAt())
            .updatedAt(domain.getUpdatedAt())
            .build();
    }
}
