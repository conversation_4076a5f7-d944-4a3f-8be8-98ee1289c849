package com.favick.adapter.out.persistence.feature.token.mapper;

import com.favick.adapter.out.persistence.feature.token.entity.PasswordResetTokenEntity;
import com.favick.domain.feature.token.model.PasswordResetToken;
import com.favick.domain.feature.token.model.PasswordResetTokenId;
import com.favick.domain.feature.token.model.TokenExpireAt;
import com.favick.domain.feature.token.model.TokenValue;
import com.favick.domain.feature.user.model.UserId;

/**
 * パスワードリセットトークンドメインモデルとエンティティのマッパー
 */
public class PasswordResetTokenPersistenceMapper {
    /**
     * エンティティをドメインモデルに変換する
     *
     * @param entity エンティティ
     * @return ドメインモデル
     */
    public static PasswordResetToken toDomain(PasswordResetTokenEntity entity) {
        return PasswordResetToken.reconstruct(
            new PasswordResetTokenId(entity.getId()),
            new UserId(entity.getUserId()),
            new TokenValue(entity.getToken()),
            new TokenExpireAt(entity.getExpireAt()),
            entity.getCreatedAt()
        );
    }

    /**
     * ドメインモデルをエンティティに変換する
     *
     * @param domain ドメインモデル
     * @return エンティティ
     */
    public static PasswordResetTokenEntity toEntity(PasswordResetToken domain) {
        return PasswordResetTokenEntity.builder()
            .id(domain.getId().value())
            .userId(domain.getUserId().value())
            .token(domain.getToken().value())
            .expireAt(domain.getExpireAt().value())
            .createdAt(domain.getCreatedAt())
            .build();
    }
}
