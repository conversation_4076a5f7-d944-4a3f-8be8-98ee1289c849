package com.favick.adapter.out.persistence.feature.fave.repository;

import com.favick.adapter.out.persistence.feature.fave.entity.FaveEntity;
import com.favick.adapter.out.persistence.feature.fave.projection.IntegratedFave;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * お気に入りJPAリポジトリ
 * お気に入りエンティティの永続化を担当する
 */
public interface FaveJpaRepository extends JpaRepository<FaveEntity, UUID> {
    /**
     * テーマIDでお気に入り一覧を取得する（作成日時の新しい順）
     *
     * @param themeId テーマID
     * @return お気に入り一覧
     */
    @Query("SELECT f FROM FaveEntity f JOIN ReviewEntity r ON f.id = r.faveId WHERE f.themeId = :themeId AND r.status = 'APPROVED' ORDER BY f.createdAt DESC")
    List<FaveEntity> findAllApprovedByThemeId(@Param("themeId") UUID themeId);

    /**
     * ユーザーIDとテーマIDで統合済みのお気に入りを取得する
     *
     * @param userId  ユーザーID
     * @param themeId テーマID
     * @return 統合済みのお気に入り
     */
    @Query("""
            SELECT f.id as faveId,
                   f.userId as userId,
                   f.themeId as themeId,
                   f.content as content,
                   f.createdAt as faveCreatedAt,
                   f.updatedAt as faveUpdatedAt,
                   COALESCE(r.id, NULL) as reviewId,
                   COALESCE(r.status, 'PENDING') as reviewStatus,
                   COALESCE(r.createdAt, f.createdAt) as reviewCreatedAt,
                   COALESCE(r.updatedAt, f.updatedAt) as reviewUpdatedAt
            FROM FaveEntity f
            LEFT JOIN ReviewEntity r ON f.id = r.faveId
            WHERE f.userId = :userId AND f.themeId = :themeId
        """)
    Optional<IntegratedFave> findIntegratedByUserIdAndThemeId(@Param("userId") UUID userId, @Param("themeId") UUID themeId);
}
