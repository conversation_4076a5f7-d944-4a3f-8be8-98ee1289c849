package com.favick.adapter.out.persistence.feature.language.repository;

import com.favick.adapter.out.persistence.feature.language.mapper.LanguagePersistenceMapper;
import com.favick.application.port.out.feature.language.repository.LanguageRepository;
import com.favick.domain.feature.language.model.Language;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 言語リポジトリ実装
 * 言語の永続化を担当する
 */
@Repository
@RequiredArgsConstructor
public class LanguageRepositoryImpl implements LanguageRepository {
    private final LanguageJpaRepository languageJpaRepository;

    @Override
    public List<Language> findAll() {
        return languageJpaRepository.findAll(Sort.by(Sort.Direction.ASC, "code"))
            .stream()
            .map(LanguagePersistenceMapper::toDomain)
            .toList();
    }
}
