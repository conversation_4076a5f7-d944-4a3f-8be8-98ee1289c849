package com.favick.adapter.out.persistence.feature.like.mapper;

import com.favick.adapter.out.persistence.feature.like.entity.LikeEntity;
import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.like.model.Like;
import com.favick.domain.feature.like.model.LikeId;
import com.favick.domain.feature.user.model.UserId;

/**
 * いいねドメインモデルとエンティティのマッパー
 */
public class LikePersistenceMapper {
    /**
     * エンティティをドメインモデルに変換する
     *
     * @param entity エンティティ
     * @return ドメインモデル
     */
    public static Like toDomain(LikeEntity entity) {
        return Like.reconstruct(
            new LikeId(entity.getId()),
            new UserId(entity.getUserId()),
            new FaveId(entity.getFaveId()),
            entity.getCreatedAt()
        );
    }

    /**
     * ドメインモデルをエンティティに変換する
     *
     * @param domain ドメインモデル
     * @return エンティティ
     */
    public static LikeEntity toEntity(Like domain) {
        return LikeEntity.builder()
            .id(domain.getId().value())
            .userId(domain.getUserId().value())
            .faveId(domain.getFaveId().value())
            .createdAt(domain.getCreatedAt())
            .build();
    }
}
