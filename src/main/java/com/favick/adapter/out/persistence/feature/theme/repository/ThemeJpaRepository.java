package com.favick.adapter.out.persistence.feature.theme.repository;

import com.favick.adapter.out.persistence.feature.theme.entity.ThemeEntity;
import com.favick.adapter.out.persistence.feature.theme.projection.IntegratedTheme;
import com.favick.adapter.out.persistence.feature.theme.projection.IntegratedThemeMultiLang;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * テーマJPAリポジトリ
 * テーマエンティティの永続化を担当する
 */
public interface ThemeJpaRepository extends JpaRepository<ThemeEntity, UUID> {
    /**
     * 指定言語で統合済みのテーマ一覧を取得する
     *
     * @param languageCode 言語コード
     * @return 統合済みのテーマ一覧
     */
    @Query("""
            SELECT t.id as themeId,
                   t.type as type,
                   t.startDate as startDate,
                   t.createdAt as themeCreatedAt,
                   t.updatedAt as themeUpdatedAt,
                   tl.id as themeLocalizationId,
                   tl.title as title,
                   tl.description as description,
                   tl.languageCode as languageCode,
                   tl.createdAt as localizationCreatedAt,
                   tl.updatedAt as localizationUpdatedAt
            FROM ThemeEntity t
            INNER JOIN ThemeLocalizationEntity tl ON t.id = tl.themeId
            WHERE tl.languageCode = :languageCode
            ORDER BY t.startDate DESC
        """)
    List<IntegratedTheme> findAllIntegratedByLanguageCode(@Param("languageCode") LanguageCodeValue languageCode);

    /**
     * 指定言語で現在開催中の統合済みのテーマを取得する
     * 開始日が今日以降で、最も今日に近い（最も早い）テーマを返す
     *
     * @param today        今日の日付
     * @param languageCode 言語コード
     * @return 現在開催中の統合済みのテーマ
     */
    @Query("""
            SELECT t.id as themeId,
                   t.type as type,
                   t.startDate as startDate,
                   t.createdAt as themeCreatedAt,
                   t.updatedAt as themeUpdatedAt,
                   tl.id as themeLocalizationId,
                   tl.title as title,
                   tl.description as description,
                   tl.languageCode as languageCode,
                   tl.createdAt as localizationCreatedAt,
                   tl.updatedAt as localizationUpdatedAt
            FROM ThemeEntity t
            INNER JOIN ThemeLocalizationEntity tl ON t.id = tl.themeId
            WHERE t.startDate <= :today
            AND tl.languageCode = :languageCode
            ORDER BY t.startDate DESC
            LIMIT 1
        """)
    Optional<IntegratedTheme> findCurrentIntegratedByLanguageCode(@Param("today") LocalDate today, @Param("languageCode") LanguageCodeValue languageCode);

    /**
     * テーマIDで統合済みのテーマを取得する
     *
     * @param themeId      テーマID
     * @param languageCode 言語コード
     * @return 統合データ
     */
    @Query("""
            SELECT t.id as themeId,
                   t.type as type,
                   t.startDate as startDate,
                   t.createdAt as themeCreatedAt,
                   t.updatedAt as themeUpdatedAt,
                   tl.id as themeLocalizationId,
                   tl.title as title,
                   tl.description as description,
                   tl.languageCode as languageCode,
                   tl.createdAt as localizationCreatedAt,
                   tl.updatedAt as localizationUpdatedAt
            FROM ThemeEntity t
            INNER JOIN ThemeLocalizationEntity tl ON t.id = tl.themeId
            WHERE t.id = :themeId
            AND tl.languageCode = :languageCode
        """)
    Optional<IntegratedTheme> findIntegratedByIdAndLanguageCode(@Param("themeId") UUID themeId, @Param("languageCode") LanguageCodeValue languageCode);

    /**
     * テーマIDで全言語の統合済みテーマを取得する
     * 管理画面での編集・更新処理のため、JA/EN両言語を一括取得する
     *
     * @param themeId テーマID
     * @return 全言語統合データ
     */
    @Query("""
            SELECT t.id as themeId,
                   t.type as type,
                   t.startDate as startDate,
                   t.createdAt as themeCreatedAt,
                   t.updatedAt as themeUpdatedAt,
                   tl_ja.id as themeLocalizationJaId,
                   tl_ja.title as titleJa,
                   tl_ja.description as descriptionJa,
                   tl_ja.createdAt as localizationJaCreatedAt,
                   tl_ja.updatedAt as localizationJaUpdatedAt,
                   tl_en.id as themeLocalizationEnId,
                   tl_en.title as titleEn,
                   tl_en.description as descriptionEn,
                   tl_en.createdAt as localizationEnCreatedAt,
                   tl_en.updatedAt as localizationEnUpdatedAt
            FROM ThemeEntity t
            INNER JOIN ThemeLocalizationEntity tl_ja ON t.id = tl_ja.themeId AND tl_ja.languageCode = 'JA'
            INNER JOIN ThemeLocalizationEntity tl_en ON t.id = tl_en.themeId AND tl_en.languageCode = 'EN'
            WHERE t.id = :themeId
        """)
    Optional<IntegratedThemeMultiLang> findIntegratedMultiLangById(@Param("themeId") UUID themeId);

    /**
     * 現在開催中のテーマを取得する
     * 開始日が今日以降で、最も今日に近い（最も早い）テーマを返す
     *
     * @param today 今日の日付
     * @return 現在開催中のテーマ
     */
    @Query("SELECT t FROM ThemeEntity t WHERE t.startDate <= :today ORDER BY t.startDate DESC LIMIT 1")
    Optional<ThemeEntity> findCurrentTheme(@Param("today") LocalDate today);

    /**
     * 開始日が一致するテーマが存在するかを検証する
     *
     * @param startDate 検証対象の開始日
     * @return 開始日が一致するテーマが存在する場合true、存在しない場合false
     */
    boolean existsByStartDate(LocalDate startDate);

    /**
     * 開始日が一致するテーマが存在するかを検証する（IDを除く）
     *
     * @param startDate 検証対象の開始日
     * @param id        除外対象のID
     * @return 開始日が一致するテーマが存在する場合true、存在しない場合false
     */
    boolean existsByStartDateAndIdNot(LocalDate startDate, UUID id);
}
