package com.favick.adapter.out.persistence.feature.review.mapper;

import com.favick.adapter.out.persistence.feature.review.entity.ReviewEntity;
import com.favick.domain.feature.admin.model.AdminId;
import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.review.model.Review;
import com.favick.domain.feature.review.model.ReviewId;
import com.favick.domain.feature.review.model.ReviewStatus;

/**
 * 審査ドメインモデルとエンティティのマッパー
 */
public class ReviewPersistenceMapper {
    /**
     * エンティティをドメインモデルに変換する
     *
     * @param entity エンティティ
     * @return ドメインモデル
     */
    public static Review toDomain(ReviewEntity entity) {
        return Review.reconstruct(
            new ReviewId(entity.getId()),
            new FaveId(entity.getFaveId()),
            entity.getAdminId() != null ? new AdminId(entity.getAdminId()) : null,
            new ReviewStatus(entity.getStatus()),
            entity.getCreatedAt(),
            entity.getUpdatedAt()
        );
    }

    /**
     * ドメインモデルをエンティティに変換する
     *
     * @param domain ドメインモデル
     * @return エンティティ
     */
    public static ReviewEntity toEntity(Review domain) {
        return ReviewEntity.builder()
            .id(domain.getId().value())
            .faveId(domain.getFaveId().value())
            .adminId(domain.getAdminId() != null ? domain.getAdminId().value() : null)
            .status(domain.getStatus().value())
            .createdAt(domain.getCreatedAt())
            .updatedAt(domain.getUpdatedAt())
            .build();
    }
}
