package com.favick.adapter.out.persistence.feature.token.repository;

import com.favick.adapter.out.persistence.feature.token.entity.VerificationTokenEntity;
import com.favick.adapter.out.persistence.feature.token.mapper.VerificationTokenPersistenceMapper;
import com.favick.application.port.out.feature.token.repository.VerificationTokenRepository;
import com.favick.domain.feature.token.model.TokenValue;
import com.favick.domain.feature.token.model.VerificationToken;
import com.favick.domain.feature.token.model.VerificationTokenId;
import com.favick.domain.feature.user.model.UserId;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 認証トークンリポジトリ実装
 * 認証トークンの永続化を担当する
 */
@Repository
@RequiredArgsConstructor
public class VerificationTokenRepositoryImpl implements VerificationTokenRepository {
    private final VerificationTokenJpaRepository verificationTokenJpaRepository;

    @Override
    public void save(VerificationToken verificationToken) {
        VerificationTokenEntity entity = VerificationTokenPersistenceMapper.toEntity(verificationToken);
        verificationTokenJpaRepository.save(entity);
    }

    @Override
    public Optional<VerificationToken> findById(VerificationTokenId id) {
        return verificationTokenJpaRepository.findById(id.value())
            .map(VerificationTokenPersistenceMapper::toDomain);
    }

    @Override
    public Optional<VerificationToken> findByToken(TokenValue token) {
        return verificationTokenJpaRepository.findByToken(token.value())
            .map(VerificationTokenPersistenceMapper::toDomain);
    }

    @Override
    public Optional<VerificationToken> findByUserId(UserId userId) {
        return verificationTokenJpaRepository.findByUserId(userId.value())
            .map(VerificationTokenPersistenceMapper::toDomain);
    }

    @Override
    public void delete(VerificationTokenId id) {
        verificationTokenJpaRepository.deleteById(id.value());
    }

    @Override
    public void deleteByUserId(UserId userId) {
        verificationTokenJpaRepository.deleteByUserId(userId.value());
    }
}
