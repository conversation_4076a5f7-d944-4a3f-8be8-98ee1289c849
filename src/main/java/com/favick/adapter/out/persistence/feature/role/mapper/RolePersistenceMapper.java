package com.favick.adapter.out.persistence.feature.role.mapper;

import com.favick.adapter.out.persistence.feature.role.entity.RoleEntity;
import com.favick.domain.feature.role.model.Role;
import com.favick.domain.feature.role.model.RoleId;
import com.favick.domain.feature.role.model.RoleName;

/**
 * 権限ドメインモデルとエンティティのマッパー
 */
public class RolePersistenceMapper {
    /**
     * エンティティをドメインモデルに変換する
     *
     * @param entity エンティティ
     * @return ドメインモデル
     */
    public static Role toDomain(RoleEntity entity) {
        return Role.reconstruct(
            new RoleId(entity.getId()),
            new RoleName(entity.getName()),
            entity.getCreatedAt(),
            entity.getUpdatedAt()
        );
    }

    /**
     * ドメインモデルをエンティティに変換する
     *
     * @param domain ドメインモデル
     * @return エンティティ
     */
    public static RoleEntity toEntity(Role domain) {
        return RoleEntity.builder()
            .id(domain.getId().value())
            .name(domain.getName().value())
            .createdAt(domain.getCreatedAt())
            .updatedAt(domain.getUpdatedAt())
            .build();
    }
}
