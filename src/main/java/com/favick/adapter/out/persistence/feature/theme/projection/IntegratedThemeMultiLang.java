package com.favick.adapter.out.persistence.feature.theme.projection;

import com.favick.domain.feature.theme.model.ThemeTypeValue;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * テーマの結合Projection
 */
public interface IntegratedThemeMultiLang {
    // Theme
    UUID getThemeId();

    ThemeTypeValue getType();

    LocalDate getStartDate();

    LocalDateTime getThemeCreatedAt();

    LocalDateTime getThemeUpdatedAt();

    // ThemeLocalization（日本語）
    UUID getThemeLocalizationJaId();

    String getTitleJa();

    String getDescriptionJa();

    LocalDateTime getLocalizationJaCreatedAt();

    LocalDateTime getLocalizationJaUpdatedAt();

    // ThemeLocalization（英語）
    UUID getThemeLocalizationEnId();

    String getTitleEn();

    String getDescriptionEn();

    LocalDateTime getLocalizationEnCreatedAt();

    LocalDateTime getLocalizationEnUpdatedAt();
}
