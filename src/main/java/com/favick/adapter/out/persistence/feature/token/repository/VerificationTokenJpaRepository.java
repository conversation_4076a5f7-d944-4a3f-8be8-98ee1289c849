package com.favick.adapter.out.persistence.feature.token.repository;

import com.favick.adapter.out.persistence.feature.token.entity.VerificationTokenEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;
import java.util.UUID;

/**
 * 認証トークンJPAリポジトリ
 * 認証トークンエンティティの永続化を担当する
 */
public interface VerificationTokenJpaRepository extends JpaRepository<VerificationTokenEntity, UUID> {
    /**
     * トークン値が一致する認証トークンを取得する
     *
     * @param token 検索対象のトークン値
     * @return トークン値が一致する認証トークン
     */
    Optional<VerificationTokenEntity> findByToken(String token);

    /**
     * ユーザーIDが一致する認証トークンを取得する
     *
     * @param userId 検索対象のユーザーID
     * @return ユーザーIDが一致する認証トークン
     */
    Optional<VerificationTokenEntity> findByUserId(UUID userId);

    /**
     * ユーザーIDが一致する認証トークンを削除する
     *
     * @param userId 削除対象のユーザーID
     */
    void deleteByUserId(UUID userId);
}
