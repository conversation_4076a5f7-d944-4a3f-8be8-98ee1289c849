package com.favick.adapter.out.persistence.feature.theme_localization.mapper;

import com.favick.adapter.out.persistence.feature.theme_localization.entity.ThemeLocalizationEntity;
import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.theme_localization.model.ThemeDescription;
import com.favick.domain.feature.theme_localization.model.ThemeLocalization;
import com.favick.domain.feature.theme_localization.model.ThemeLocalizationId;
import com.favick.domain.feature.theme_localization.model.ThemeTitle;

/**
 * テーマローカライゼーションドメインモデルとエンティティのマッパー
 */
public class ThemeLocalizationPersistenceMapper {
    /**
     * エンティティをドメインモデルに変換する
     *
     * @param entity エンティティ
     * @return ドメインモデル
     */
    public static ThemeLocalization toDomain(ThemeLocalizationEntity entity) {
        return ThemeLocalization.reconstruct(
            new ThemeLocalizationId(entity.getId()),
            new ThemeId(entity.getThemeId()),
            new LanguageCode(entity.getLanguageCode()),
            new ThemeTitle(entity.getTitle()),
            new ThemeDescription(entity.getDescription()),
            entity.getCreatedAt(),
            entity.getUpdatedAt()
        );
    }

    /**
     * ドメインモデルをエンティティに変換する
     *
     * @param domain ドメインモデル
     * @return エンティティ
     */
    public static ThemeLocalizationEntity toEntity(ThemeLocalization domain) {
        return ThemeLocalizationEntity.builder()
            .id(domain.getId().value())
            .themeId(domain.getThemeId().value())
            .languageCode(domain.getLanguageCode().value())
            .title(domain.getTitle().value())
            .description(domain.getDescription().value())
            .createdAt(domain.getCreatedAt())
            .updatedAt(domain.getUpdatedAt())
            .build();
    }

}
