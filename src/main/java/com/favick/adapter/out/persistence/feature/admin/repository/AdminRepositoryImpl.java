package com.favick.adapter.out.persistence.feature.admin.repository;

import com.favick.adapter.out.persistence.feature.admin.mapper.AdminPersistenceMapper;
import com.favick.application.port.out.feature.admin.repository.AdminRepository;
import com.favick.domain.feature.admin.model.Admin;
import com.favick.domain.feature.admin.model.AdminEmail;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 管理者リポジトリ実装
 * 管理者の永続化を担当する
 */
@Repository
@RequiredArgsConstructor
public class AdminRepositoryImpl implements AdminRepository {
    private final AdminJpaRepository adminJpaRepository;

    @Override
    public Optional<Admin> findByEmail(AdminEmail email) {
        return adminJpaRepository.findByEmail(email.value())
            .map(AdminPersistenceMapper::toDomain);
    }
}
