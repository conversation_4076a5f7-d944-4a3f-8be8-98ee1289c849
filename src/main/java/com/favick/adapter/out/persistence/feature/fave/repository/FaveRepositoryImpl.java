package com.favick.adapter.out.persistence.feature.fave.repository;

import com.favick.adapter.out.persistence.feature.fave.entity.FaveEntity;
import com.favick.adapter.out.persistence.feature.fave.mapper.FavePersistenceMapper;
import com.favick.application.port.out.feature.fave.repository.FaveRepository;
import com.favick.domain.feature.fave.model.Fave;
import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.review.model.Review;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.user.model.UserId;
import lombok.RequiredArgsConstructor;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * お気に入りリポジトリ実装
 * お気に入りの永続化を担当する
 */
@Repository
@RequiredArgsConstructor
public class FaveRepositoryImpl implements FaveRepository {
    private final FaveJpaRepository faveJpaRepository;

    @Override
    public List<Fave> findAllApprovedByThemeId(ThemeId themeId) {
        return faveJpaRepository.findAllApprovedByThemeId(themeId.value())
            .stream()
            .map(FavePersistenceMapper::toDomain)
            .toList();
    }

    @Override
    public Optional<Fave> findById(FaveId id) {
        return faveJpaRepository.findById(id.value())
            .map(FavePersistenceMapper::toDomain);
    }

    @Override
    public Optional<Pair<Fave, Review>> findIntegratedByUserIdAndThemeId(UserId userId, ThemeId themeId) {
        return faveJpaRepository.findIntegratedByUserIdAndThemeId(userId.value(), themeId.value())
            .map(FavePersistenceMapper::toIntegratedDomain);
    }

    @Override
    public void save(Fave fave) {
        FaveEntity entity = FavePersistenceMapper.toEntity(fave);
        faveJpaRepository.save(entity);
    }
}
