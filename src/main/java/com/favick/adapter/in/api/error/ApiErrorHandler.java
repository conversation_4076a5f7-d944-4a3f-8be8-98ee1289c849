package com.favick.adapter.in.api.error;

import com.favick.adapter.in.api.response.ApiErrorResponse;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import com.favick.common.helper.MessageHelper;
import com.favick.domain.exception.DomainException;
import com.favick.domain.exception.ValueObjectException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import java.util.Locale;

/**
 * APIエラーをハンドリングするクラス
 */
@RestControllerAdvice(annotations = RestController.class)
@RequiredArgsConstructor
@Slf4j
public class ApiErrorHandler {
    private final MessageHelper messageHelper;

    /**
     * 値オブジェクトのエラーをハンドリングする
     *
     * @param ex      ドメインエラー
     * @param request リクエスト
     * @return レスポンス
     */
    @ExceptionHandler(ValueObjectException.class)
    public ResponseEntity<ApiErrorResponse> handleDomainException(ValueObjectException ex, WebRequest request) {
        log.warn("API validation error: field={}, message={}", ex.getField(), ex.getMessageKey());

        Locale locale = LocaleContextHolder.getLocale();
        String message = messageHelper.getMessage(ex.getMessageKey(), ex.getArgs());
        return ResponseEntity.badRequest().body(new ApiErrorResponse(ex.getField(), message));
    }

    /**
     * ドメインエラーをハンドリングする
     *
     * @param ex      ドメインエラー
     * @param request リクエスト
     * @return レスポンス
     */
    @ExceptionHandler(DomainException.class)
    public ResponseEntity<ApiErrorResponse> handleInvalidDomainException(DomainException ex, WebRequest request) {
        log.warn("API domain error: field={}, message={}", ex.getField(), ex.getMessageKey());
        String message = messageHelper.getMessage(ex.getMessageKey(), ex.getArgs());
        String field = ex.hasField() ? ex.getField() : "global";
        return ResponseEntity.badRequest().body(new ApiErrorResponse(field, message));
    }

    /**
     * 複数のドメインエラーをハンドリングする
     *
     * @param ex      複数のドメインエラー
     * @param request リクエスト
     * @return レスポンス
     */
    @ExceptionHandler(InvalidDomainsException.class)
    public ResponseEntity<ApiErrorResponse> handleInvalidDomainsException(InvalidDomainsException ex, WebRequest request) {
        log.warn("API multiple errors: count={}", ex.getExceptions().size());
        // 最初のエラーを代表として返す（シンプルに）
        DomainException firstError = ex.getExceptions().get(0);
        String message = messageHelper.getMessage(firstError.getMessageKey(), firstError.getArgs());
        String field = firstError.hasField() ? firstError.getField() : "global";
        return ResponseEntity.badRequest().body(new ApiErrorResponse(field, message));
    }

    /**
     * リソースが見つからない場合のエラーをハンドリングする
     *
     * @param ex      リソースが見つからない場合のエラー
     * @param request リクエスト
     * @return レスポンス
     */
    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<ApiErrorResponse> handleResourceNotFoundException(ResourceNotFoundException ex, WebRequest request) {
        log.warn("Exception handled: {}", ex.getMessage(), ex);
        String resourceName = messageHelper.getMessage(ex.getMessageKey());
        String message = messageHelper.getMessage("error.not_found", new Object[]{resourceName});
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(new ApiErrorResponse("resource", message));
    }
}
