package com.favick.adapter.in.web.helper;

import com.favick.common.helper.MessageHelper;
import com.favick.domain.exception.DomainException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;

/**
 * DomainExceptionのハンドラ
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class InvalidDomainExceptionHelper implements RequestExceptionHandler {
    private final MessageHelper messageHelper;

    @Override
    public String handle(Exception e) {
        DomainException exception = (DomainException) e;
        log.warn("Domain validation error: field={}, messageKey={}", exception.getField(), exception.getMessageKey());
        return messageHelper.getMessage(exception.getMessageKey(), exception.getArgs());
    }

    @Override
    public void handle(Exception e, BindingResult bindingResult) {
        DomainException exception = (DomainException) e;
        log.warn("Domain validation error with binding result: field={}, messageKey={}", exception.getField(), exception.getMessageKey());
        String message = messageHelper.getMessage(exception.getMessageKey(), exception.getArgs());

        if (exception.hasField()) {
            String field = exception.getField();
            if (!bindingResult.hasFieldErrors(field)) {
                bindingResult.rejectValue(field, exception.getMessageKey(), exception.getArgs(), message);
            }
        } else {
            if (!bindingResult.hasGlobalErrors()) {
                bindingResult.reject(exception.getMessageKey(), exception.getArgs(), message);
            }
        }
    }
}
