package com.favick.adapter.in.web.mapper;

import com.favick.adapter.in.web.form.CreateUserForm;
import com.favick.adapter.in.web.form.RequestPasswordResetForm;
import com.favick.adapter.in.web.form.ResetPasswordForm;
import com.favick.application.dto.command.CreateUserCommandDto;
import com.favick.application.dto.command.RequestPasswordResetCommandDto;
import com.favick.application.dto.command.ResetPasswordCommandDto;
import com.favick.application.dto.query.ActivateUserQueryDto;

/**
 * ユーザーフォームとDTOのマッパー
 * フォームからDTOへの変換を担当する
 */
public class UserWebMapper {
    /**
     * トークンからActivateUserQueryDtoに変換する
     *
     * @param token トークン
     * @return 有効化クエリDTO
     */
    public static ActivateUserQueryDto toActivateQueryDto(String token) {
        return new ActivateUserQueryDto(token);
    }

    /**
     * CreateUserFormをCreateUserCommandDtoに変換する
     *
     * @param form 登録フォーム
     * @return 登録コマンドDTO
     */
    public static CreateUserCommandDto toCreateCommandDto(CreateUserForm form, String origin) {
        return new CreateUserCommandDto(
            form.getName(),
            form.getEmail(),
            form.getPassword(),
            form.getImageUrl(),
            origin
        );
    }

    /**
     * RequestPasswordResetFormをRequestPasswordResetCommandDtoに変換する
     *
     * @param form   パスワードリセット要求フォーム
     * @param origin リクエスト元URL
     * @return パスワードリセット要求コマンドDTO
     */
    public static RequestPasswordResetCommandDto toRequestPasswordResetCommandDto(RequestPasswordResetForm form, String origin) {
        return new RequestPasswordResetCommandDto(
            form.getEmail(),
            origin
        );
    }

    /**
     * ResetPasswordFormとtokenをResetPasswordCommandDtoに変換する
     *
     * @param form  パスワードリセットフォーム
     * @param token パスワードリセットトークン
     * @return パスワードリセット実行コマンドDTO
     */
    public static ResetPasswordCommandDto toResetPasswordCommandDto(ResetPasswordForm form, String token) {
        return new ResetPasswordCommandDto(
            form.getPassword(),
            token
        );
    }
}
