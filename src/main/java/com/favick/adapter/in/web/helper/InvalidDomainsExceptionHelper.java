package com.favick.adapter.in.web.helper;

import com.favick.common.exception.InvalidDomainsException;
import com.favick.domain.exception.DomainException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;

import java.util.stream.Collectors;

/**
 * InvalidDomainExceptionのハンドラ
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class InvalidDomainsExceptionHelper implements RequestExceptionHandler {
    private final InvalidDomainExceptionHelper invalidDomainExceptionHelper;

    @Override
    public String handle(Exception e) {
        InvalidDomainsException exception = (InvalidDomainsException) e;
        log.warn("Multiple domain validation errors occurred: count={}", exception.getExceptions().size());

        // 複数のInvalidDomainsExceptionを一つずつ処理
        return exception.getExceptions().stream()
            .map(invalidDomainExceptionHelper::handle)
            .collect(Collectors.joining("、"));
    }

    @Override
    public void handle(Exception e, BindingResult bindingResult) {
        InvalidDomainsException exception = (InvalidDomainsException) e;
        log.warn("Multiple domain validation errors with binding result: count={}", exception.getExceptions().size());

        // 複数のInvalidDomainsExceptionを一つずつ処理
        for (DomainException error : exception.getExceptions()) {
            invalidDomainExceptionHelper.handle(error, bindingResult);
        }
    }
}
