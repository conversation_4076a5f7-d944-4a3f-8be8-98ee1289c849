package com.favick.adapter.in.web.mapper;

import com.favick.adapter.in.web.form.PickFaveForm;
import com.favick.application.dto.command.PickFaveCommandDto;
import com.favick.application.dto.query.GetCurrentFaveQueryDto;
import com.favick.application.dto.query.ListCurrentThemeFavesQueryDto;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import com.favick.domain.feature.theme.model.ThemeTypeValue;

import java.util.Optional;
import java.util.UUID;

public class FaveWebMapper {
    /**
     * 言語コードからListCurrentThemeFavesQueryDtoに変換する
     *
     * @param languageCode 言語コード
     * @return 開催中のテーマのお気に入り一覧取得クエリDTO
     */
    public static ListCurrentThemeFavesQueryDto toListCurrentThemeQueryDto(LanguageCodeValue languageCode, Optional<UUID> userId) {
        return new ListCurrentThemeFavesQueryDto(languageCode, userId);
    }

    /**
     * ユーザーIDと言語コードからGetCurrentFaveQueryDtoに変換する
     *
     * @param userId       ユーザーID
     * @param languageCode 言語コード
     * @return お気に入り取得クエリDTO
     */
    public static GetCurrentFaveQueryDto toGetCurrentFaveQueryDto(UUID userId, LanguageCodeValue languageCode) {
        return new GetCurrentFaveQueryDto(userId, languageCode);
    }

    /**
     * ユーザーIDとフォーム情報からPickFaveCommandDtoに変換する
     *
     * @param userId ユーザーID
     * @param form   作成フォーム
     * @return お気に入り作成コマンドDTO
     */
    public static PickFaveCommandDto toPickCommandDto(
        UUID userId,
        UUID themeId,
        ThemeTypeValue themeType,
        PickFaveForm form
    ) {
        return new PickFaveCommandDto(
            userId,
            themeId,
            themeType,
            form.getFile()
        );
    }
}
