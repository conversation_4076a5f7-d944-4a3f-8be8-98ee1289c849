package com.favick.adapter.in.web.controller;

import com.favick.adapter.in.security.model.UserDetailsImpl;
import com.favick.adapter.in.web.form.PickFaveForm;
import com.favick.adapter.in.web.helper.RequestErrorHandler;
import com.favick.application.dto.result.CurrentThemePickedFaveResultDto;
import com.favick.application.port.in.GetCurrentThemePickedFaveUseCase;
import com.favick.application.port.in.PickFaveUseCase;
import com.favick.common.helper.MessageHelper;
import com.favick.common.type.MessageType;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import com.favick.domain.feature.theme.model.ThemeTypeValue;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.Locale;
import java.util.UUID;

import static com.favick.adapter.in.web.mapper.FaveWebMapper.toGetCurrentFaveQueryDto;
import static com.favick.adapter.in.web.mapper.FaveWebMapper.toPickCommandDto;

/**
 * お気に入り画面のコントローラー
 */
@Controller
@RequestMapping("/pick")
@RequiredArgsConstructor
@Slf4j
public class PickController {
    private final GetCurrentThemePickedFaveUseCase getCurrentThemePickedFaveUseCase;
    private final PickFaveUseCase pickFaveUseCase;
    private final LocaleResolver localeResolver;
    private final RequestErrorHandler requestErrorHandler;
    private final MessageHelper messageHelper;

    /**
     * お気に入り画面を表示する
     *
     * @return お気に入り画面のテンプレート名
     */
    @GetMapping
    @PreAuthorize("isAuthenticated()")
    public String index(
        @AuthenticationPrincipal UserDetailsImpl userDetails,
        RedirectAttributes redirectAttributes,
        Model model,
        HttpServletRequest request
    ) {
        try {
            if (!model.containsAttribute("form")) {
                model.addAttribute("form", new PickFaveForm());
            }

            UUID userId = userDetails.getUser().id();
            Locale locale = localeResolver.resolveLocale(request);
            LanguageCodeValue languageCode = LanguageCodeValue.valueOf(locale.getLanguage().toUpperCase());
            CurrentThemePickedFaveResultDto result = getCurrentThemePickedFaveUseCase.handle(toGetCurrentFaveQueryDto(userId, languageCode));

            model.addAttribute("theme", result.theme());
            model.addAttribute("fave", result.fave().orElse(null));

            return "pages/pick/index";
        } catch (Exception e) {
            log.error("Failed to load pick page: userId={}", userDetails.getUser().id(), e);
            String message = requestErrorHandler.handle(e);
            redirectAttributes.addFlashAttribute("errorMessage", message);
            return "redirect:/";
        }
    }

    @PostMapping
    @PreAuthorize("isAuthenticated()")
    public String pick(
        @AuthenticationPrincipal UserDetailsImpl userDetails,
        @ModelAttribute("form") @Valid PickFaveForm form,
        BindingResult bindingResult,
        RedirectAttributes redirectAttributes,
        Model model,
        HttpServletRequest request
    ) {
        try {
            UUID userId = userDetails.getUser().id();
            Locale locale = localeResolver.resolveLocale(request);
            LanguageCodeValue languageCode = LanguageCodeValue.valueOf(locale.getLanguage().toUpperCase());
            CurrentThemePickedFaveResultDto result = getCurrentThemePickedFaveUseCase.handle(toGetCurrentFaveQueryDto(userId, languageCode));

            UUID themeId = result.theme().id();
            ThemeTypeValue themeType = result.theme().type();

            model.addAttribute("theme", result.theme());
            model.addAttribute("fave", result.fave().orElse(null));

            if (bindingResult.hasErrors()) {
                return "pages/pick/index";
            }

            try {
                pickFaveUseCase.handle(toPickCommandDto(userId, themeId, themeType, form));
                messageHelper.setMessage(redirectAttributes, MessageType.SUCCESS, "success.create");
                return "redirect:/pick";
            } catch (Exception e) {
                String fileName = form.getFile() != null ? form.getFile().getOriginalFilename() : "null";
                log.error("Fave creation failed: content={}", fileName, e);
                requestErrorHandler.handle(e, bindingResult);
                return "pages/pick/index";
            }
        } catch (Exception e) {
            log.error("Failed to load pick page: userId={}", userDetails.getUser().id(), e);
            model.addAttribute("theme", null);
            model.addAttribute("fave", null);
            return "pages/pick/index";
        }
    }
}
