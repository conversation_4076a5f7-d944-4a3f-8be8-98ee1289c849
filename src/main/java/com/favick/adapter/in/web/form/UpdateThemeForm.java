package com.favick.adapter.in.web.form;

import com.favick.common.validation.UniqueDate;
import jakarta.validation.constraints.FutureOrPresent;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * テーマ更新フォーム
 */
@Getter
@Setter
public class UpdateThemeForm {
    @NotBlank(message = "{validation.required.title}")
    @Size(max = 50, message = "{validation.too_long.title}")
    private String titleJa;

    @NotBlank(message = "{validation.required.description}")
    @Size(max = 255, message = "{validation.too_long.description}")
    private String descriptionJa;

    @NotBlank(message = "{validation.required.title}")
    @Size(max = 50, message = "{validation.too_long.title}")
    private String titleEn;

    @NotBlank(message = "{validation.required.description}")
    @Size(max = 255, message = "{validation.too_long.description}")
    private String descriptionEn;

    @NotNull(message = "{validation.required.type}")
    private String type;

    @NotNull(message = "{validation.required.start_date}")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @FutureOrPresent(message = "{validation.must_be.future_or_present.date}")
    @UniqueDate
    private LocalDate startDate;
}
