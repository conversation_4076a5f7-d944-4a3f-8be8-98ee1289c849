package com.favick.adapter.in.web.controller.auth;

import com.favick.adapter.in.web.form.CreateUserForm;
import com.favick.adapter.in.web.form.RequestPasswordResetForm;
import com.favick.adapter.in.web.form.ResetPasswordForm;
import com.favick.adapter.in.web.helper.RequestErrorHandler;
import com.favick.application.dto.command.CreateUserCommandDto;
import com.favick.application.dto.command.RequestPasswordResetCommandDto;
import com.favick.application.dto.command.ResetPasswordCommandDto;
import com.favick.application.port.in.ActivateUserUseCase;
import com.favick.application.port.in.CreateUserUseCase;
import com.favick.application.port.in.RequestPasswordResetUseCase;
import com.favick.application.port.in.ResetPasswordUseCase;
import com.favick.common.helper.MessageHelper;
import com.favick.common.type.MessageType;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import static com.favick.adapter.in.web.mapper.UserWebMapper.*;
import static com.favick.common.util.SecurityLogUtil.maskEmail;
import static com.favick.common.util.SecurityLogUtil.maskToken;

/**
 * 認証関連（ユーザー）画面のコントローラー
 */
@Controller
@RequestMapping("/auth")
@RequiredArgsConstructor
@Slf4j
public class AuthController {
    private final CreateUserUseCase createUserUseCase;
    private final ActivateUserUseCase activateUserUseCase;
    private final RequestPasswordResetUseCase requestPasswordResetUseCase;
    private final ResetPasswordUseCase resetPasswordUseCase;
    private final RequestErrorHandler requestErrorHandler;
    private final MessageHelper messageHelper;

    /**
     * ログイン画面を表示する
     *
     * @return ログイン画面のテンプレート名
     */
    @GetMapping("/login")
    public String showLoginForm() {
        return "pages/auth/login";
    }

    /**
     * ユーザー登録画面を表示する
     *
     * @param model モデル
     * @return ユーザー登録画面のテンプレート名
     */
    @GetMapping("/register")
    public String showRegisterForm(Model model) {
        if (!model.containsAttribute("form")) {
            model.addAttribute("form", new CreateUserForm());
        }
        return "pages/auth/register";
    }

    /**
     * ユーザーを登録する
     *
     * @param form               ユーザー登録フォーム
     * @param bindingResult      バリデーション結果
     * @param redirectAttributes リダイレクト属性
     * @param model              モデル
     * @param request            HTTPリクエスト
     * @return リダイレクト先
     */
    @PostMapping("/register")
    public String register(
        @ModelAttribute("form") @Valid CreateUserForm form,
        BindingResult bindingResult,
        RedirectAttributes redirectAttributes,
        Model model,
        HttpServletRequest request
    ) {
        if (bindingResult.hasErrors()) {
            model.addAttribute("form", form);
            return "pages/auth/register";
        }

        try {
            String origin = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort();
            CreateUserCommandDto command = toCreateCommandDto(form, origin);

            createUserUseCase.handle(command);

            messageHelper.setMessage(redirectAttributes, MessageType.SUCCESS, "success.verified_requested");
            return "redirect:/auth/login";
        } catch (Exception e) {
            log.error("User registration failed: email={}", maskEmail(form.getEmail()), e);
            requestErrorHandler.handle(e, bindingResult);
            model.addAttribute("form", form);
            return "pages/auth/register";
        }
    }

    /**
     * ユーザーを有効化する
     *
     * @param token              認証トークン
     * @param redirectAttributes リダイレクト属性
     * @return リダイレクト先
     */
    @GetMapping("/register/verify")
    public String verifyEmail(
        @RequestParam("token") String token,
        RedirectAttributes redirectAttributes
    ) {
        try {
            activateUserUseCase.handle(toActivateQueryDto(token));
            messageHelper.setMessage(redirectAttributes, MessageType.SUCCESS, "success.verified");
            return "redirect:/auth/login";
        } catch (Exception e) {
            log.error("User email verification failed: token={}", maskToken(token), e);
            String message = requestErrorHandler.handle(e);
            redirectAttributes.addFlashAttribute("errorMessage", message);
            return "redirect:/auth/login";
        }
    }

    /**
     * パスワードリセット要求画面を表示する
     *
     * @param model モデル
     * @return パスワードリセット要求画面のテンプレート名
     */
    @GetMapping("/password-reset")
    public String showPasswordResetRequestForm(Model model) {
        if (!model.containsAttribute("form")) {
            model.addAttribute("form", new RequestPasswordResetForm());
        }
        return "pages/auth/password-reset";
    }

    /**
     * パスワードリセット要求を処理する
     *
     * @param form               パスワードリセット要求フォーム
     * @param bindingResult      バリデーション結果
     * @param redirectAttributes リダイレクト属性
     * @param model              モデル
     * @param request            HTTPリクエスト
     * @return リダイレクト先
     */
    @PostMapping("/password-reset")
    public String requestPasswordReset(
        @ModelAttribute("form") @Valid RequestPasswordResetForm form,
        BindingResult bindingResult,
        RedirectAttributes redirectAttributes,
        Model model,
        HttpServletRequest request
    ) {
        if (bindingResult.hasErrors()) {
            model.addAttribute("form", form);
            return "pages/auth/password-reset-request";
        }

        try {
            String origin = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort();
            RequestPasswordResetCommandDto command = toRequestPasswordResetCommandDto(form, origin);

            requestPasswordResetUseCase.handle(command);

            messageHelper.setMessage(redirectAttributes, MessageType.SUCCESS, "success.password_reset_requested");
            return "redirect:/auth/login";
        } catch (Exception e) {
            log.error("Password reset request failed: email={}", maskEmail(form.getEmail()), e);
            requestErrorHandler.handle(e, bindingResult);
            model.addAttribute("form", form);
            return "pages/auth/password-reset-request";
        }
    }

    /**
     * パスワード変更画面を表示する
     *
     * @param token              パスワードリセットトークン
     * @param model              モデル
     * @param redirectAttributes リダイレクト属性
     * @return パスワード変更画面のテンプレート名またはリダイレクト先
     */
    @GetMapping("/password-reset/verify")
    public String showResetPasswordForm(
        @RequestParam("token") String token,
        Model model,
        RedirectAttributes redirectAttributes
    ) {
        try {
            if (!model.containsAttribute("form")) {
                model.addAttribute("form", new ResetPasswordForm());
            }
            model.addAttribute("token", token);
            return "pages/auth/password-reset-verify";
        } catch (Exception e) {
            String message = requestErrorHandler.handle(e);
            redirectAttributes.addFlashAttribute("errorMessage", message);
            return "redirect:/auth/login";
        }
    }

    /**
     * パスワード変更を処理する
     *
     * @param form               パスワードリセットフォーム
     * @param bindingResult      バリデーション結果
     * @param token              パスワードリセットトークン
     * @param redirectAttributes リダイレクト属性
     * @param model              モデル
     * @return リダイレクト先
     */
    @PostMapping("/password-reset/verify")
    public String resetPassword(
        @ModelAttribute("form") @Valid ResetPasswordForm form,
        BindingResult bindingResult,
        @RequestParam("token") String token,
        RedirectAttributes redirectAttributes,
        Model model
    ) {
        if (bindingResult.hasErrors()) {
            model.addAttribute("form", form);
            model.addAttribute("token", token);
            return "pages/auth/password-reset-verify";
        }

        try {
            ResetPasswordCommandDto command = toResetPasswordCommandDto(form, token);

            resetPasswordUseCase.handle(command);

            messageHelper.setMessage(redirectAttributes, MessageType.SUCCESS, "success.password_reset");
            return "redirect:/auth/login";
        } catch (Exception e) {
            log.error("Password reset failed: token={}", maskToken(token), e);
            requestErrorHandler.handle(e, bindingResult);
            model.addAttribute("form", form);
            model.addAttribute("token", token);
            return "pages/auth/password-reset-verify";
        }
    }

}
