package com.favick.adapter.in.web.helper;

import com.favick.common.exception.FileStorageException;
import com.favick.common.helper.MessageHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;

/**
 * FileStorageExceptionのハンドラ
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class FileStorageExceptionHelper implements RequestExceptionHandler {
    private final MessageHelper messageHelper;

    @Override
    public String handle(Exception e) {
        FileStorageException exception = (FileStorageException) e;
        log.error("Exception handled: {}", e.getMessage(), e);
        return messageHelper.getMessage(exception.getMessageKey());
    }

    @Override
    public void handle(Exception e, BindingResult bindingResult) {
        FileStorageException exception = (FileStorageException) e;
        log.error("Exception handled: {}", e.getMessage(), e);
        String message = messageHelper.getMessage(exception.getMessageKey());
        bindingResult.reject("generalError", message);
    }
}
