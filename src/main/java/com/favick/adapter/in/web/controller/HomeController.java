package com.favick.adapter.in.web.controller;

import com.favick.adapter.in.security.model.UserDetailsImpl;
import com.favick.adapter.in.web.helper.RequestErrorHandler;
import com.favick.application.dto.command.ToggleLikeCommandDto;
import com.favick.application.dto.result.CurrentThemeFavesResultDto;
import com.favick.application.dto.result.FaveSummaryResultDto;
import com.favick.application.port.in.ListCurrentThemeFavesUseCase;
import com.favick.application.port.in.ToggleLikeUseCase;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.Locale;
import java.util.Optional;
import java.util.UUID;

import static com.favick.adapter.in.web.mapper.FaveWebMapper.toListCurrentThemeQueryDto;

/**
 * ホーム画面のコントローラー
 */
@Controller
@RequestMapping("/")
@RequiredArgsConstructor
@Slf4j
public class HomeController {
    private final ListCurrentThemeFavesUseCase listCurrentThemeFavesUseCase;
    private final ToggleLikeUseCase toggleLikeUseCase;
    private final LocaleResolver localeResolver;
    private final RequestErrorHandler requestErrorHandler;

    /**
     * ホーム画面を表示する
     *
     * @return ホーム画面のテンプレート名
     */
    @GetMapping
    public String index(
        @AuthenticationPrincipal UserDetailsImpl userDetails,
        RedirectAttributes redirectAttributes,
        Model model,
        HttpServletRequest request
    ) {
        try {
            // 言語コードを取得
            Locale locale = localeResolver.resolveLocale(request);
            LanguageCodeValue languageCode = LanguageCodeValue.valueOf(locale.getLanguage().toUpperCase());

            // ログインユーザーIDを取得
            Optional<UUID> userId = Optional.ofNullable(userDetails)
                .map(ud -> ud.getUser().id());

            // 開催中のテーマのお気に入り一覧を取得
            CurrentThemeFavesResultDto result = listCurrentThemeFavesUseCase.handle(
                toListCurrentThemeQueryDto(languageCode, userId)
            );

            model.addAttribute("theme", result.theme());
            model.addAttribute("faves", result.faves());

            return "pages/index";
        } catch (Exception e) {
            log.error("Failed to load home page", e);
            String message = requestErrorHandler.handle(e);
            redirectAttributes.addFlashAttribute("errorMessage", message);
            return "redirect:/error";
        }
    }

    /**
     * いいねを切り替える
     *
     * @param userDetails ユーザー詳細
     * @param faveId      お気に入りID
     * @param request     HTTPリクエスト
     * @param model       モデル
     * @return いいねボタンフラグメント
     */
    @PostMapping("/like")
    @PreAuthorize("isAuthenticated()")
    public String toggleLike(
        @AuthenticationPrincipal UserDetailsImpl userDetails,
        @RequestParam UUID faveId,
        RedirectAttributes redirectAttributes,
        HttpServletRequest request,
        HttpServletResponse response,
        Model model
    ) {
        try {
            ToggleLikeCommandDto command = new ToggleLikeCommandDto(
                userDetails.getUser().id(),
                faveId
            );

            FaveSummaryResultDto updatedFave = toggleLikeUseCase.handle(command);
            model.addAttribute("fave", updatedFave);

            return "fragments/like-button :: like-button(fave=${fave})";
        } catch (Exception e) {
            log.error("Failed to toggle like for fave: {}", faveId, e);

            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            String message = requestErrorHandler.handle(e);
            model.addAttribute("message", message);

            return "fragments/error-message :: error-message(message=${message})";
        }
    }
}
