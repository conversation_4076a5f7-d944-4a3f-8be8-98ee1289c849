package com.favick.adapter.in.web.error;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

/**
 * Webエラーをハンドリングするクラス
 */
@ControllerAdvice(annotations = Controller.class)
@Slf4j
public class WebErrorHandler {
    /**
     * アップロードサイズ超過エラー (413 Payload Too Large)
     *
     * @return エラーページのテンプレート名
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    @ResponseStatus(HttpStatus.PAYLOAD_TOO_LARGE)
    public String handleMaxSizeException(MaxUploadSizeExceededException e) {
        log.warn("Upload size exceeded: maxSize={}, message={}", e.getMaxUploadSize(), e.getMessage());

        return "pages/error/413";
    }
}
