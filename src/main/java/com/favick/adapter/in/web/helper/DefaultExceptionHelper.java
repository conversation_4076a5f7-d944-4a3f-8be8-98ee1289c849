package com.favick.adapter.in.web.helper;

import com.favick.common.helper.MessageHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;

/**
 * デフォルトの例外ハンドラ
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DefaultExceptionHelper implements RequestExceptionHandler {
    private final MessageHelper messageHelper;

    @Override
    public String handle(Exception e) {
        log.error("Exception handled: {}", e.getMessage(), e);
        return messageHelper.getMessage("error.internal_server_error");
    }

    @Override
    public void handle(Exception e, BindingResult bindingResult) {
        log.error("Exception handled with binding result: {}", e.getMessage(), e);
        String message = messageHelper.getMessage("error.internal_server_error");
        bindingResult.reject("generalError", message);
    }
}
