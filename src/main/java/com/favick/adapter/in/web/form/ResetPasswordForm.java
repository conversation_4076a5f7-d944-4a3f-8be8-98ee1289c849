package com.favick.adapter.in.web.form;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

/**
 * パスワードリセットフォーム
 */
@Getter
@Setter
public class ResetPasswordForm {
    @NotBlank(message = "{validation.required.password}")
    @Size(min = 8, message = "{validation.too_short.password}")
    @Size(max = 72, message = "{validation.too_long.password}")
    private String password;
}
