package com.favick.adapter.in.web.helper;

import com.favick.common.exception.ResourceNotFoundException;
import com.favick.common.helper.MessageHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;

/**
 * ResourceNotFoundExceptionのハンドラ
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ResourceNotFoundExceptionHelper implements RequestExceptionHandler {
    private final MessageHelper messageHelper;

    @Override
    public String handle(Exception e) {
        ResourceNotFoundException exception = (ResourceNotFoundException) e;
        log.warn("Exception handled: {}", e.getMessage(), e);
        String resourceName = messageHelper.getMessage(exception.getMessageKey());
        return messageHelper.getMessage("error.not_found", new Object[]{resourceName});
    }

    @Override
    public void handle(Exception e, BindingResult bindingResult) {
        ResourceNotFoundException exception = (ResourceNotFoundException) e;
        log.warn("Exception handled: {}", e.getMessage(), e);
        String resourceName = messageHelper.getMessage(exception.getMessageKey());
        String message = messageHelper.getMessage("error.not_found", new Object[]{resourceName});
        bindingResult.reject("generalError", message);
    }
}
