package com.favick.adapter.in.web.form;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

/**
 * パスワードリセット要求フォーム
 */
@Getter
@Setter
public class RequestPasswordResetForm {
    @NotBlank(message = "{validation.required.email}")
    @Size(max = 255, message = "{validation.too_long.email}")
    @Email(message = "{validation.invalid_format.email}")
    private String email;
}
