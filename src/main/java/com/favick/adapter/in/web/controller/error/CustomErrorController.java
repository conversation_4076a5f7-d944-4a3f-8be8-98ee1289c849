package com.favick.adapter.in.web.controller.error;

import jakarta.servlet.RequestDispatcher;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.error.ErrorController;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * カスタムエラー画面のコントローラー
 */
@Controller
@Slf4j
public class CustomErrorController implements ErrorController {
    /**
     * エラー画面を表示する
     *
     * @param request HTTPリクエスト
     * @param model   モデル
     * @return テンプレート名
     */
    @RequestMapping("/error")
    public String handleError(HttpServletRequest request, Model model) {
        Object status = request.getAttribute(RequestDispatcher.ERROR_STATUS_CODE);
        Object errorMessage = request.getAttribute(RequestDispatcher.ERROR_MESSAGE);
        Object requestUri = request.getAttribute(RequestDispatcher.ERROR_REQUEST_URI);

        if (status != null) {
            int statusCode = Integer.parseInt(status.toString());

            log.warn("HTTP Error occurred: status={}, uri={}, message={}", statusCode, requestUri, errorMessage);

            if (statusCode == HttpStatus.FORBIDDEN.value()) {
                return "pages/error/403";
            } else if (statusCode == HttpStatus.NOT_FOUND.value()) {
                return "pages/error/404";
            } else if (statusCode == HttpStatus.PAYLOAD_TOO_LARGE.value()) {
                return "pages/error/413";
            } else if (statusCode >= HttpStatus.INTERNAL_SERVER_ERROR.value()) {
                log.error("Server error occurred: status={}, uri={}", statusCode, requestUri);
                return "pages/error/500";
            }
        }

        log.error("Unknown error occurred: uri={}", requestUri);
        return "pages/error/500";
    }
}
