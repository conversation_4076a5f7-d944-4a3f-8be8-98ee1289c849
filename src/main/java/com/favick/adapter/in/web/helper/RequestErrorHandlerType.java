package com.favick.adapter.in.web.helper;

import com.favick.common.exception.FileStorageException;
import com.favick.common.exception.InvalidDomainsException;
import com.favick.common.exception.ResourceNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.BindingResult;

import java.util.Arrays;
import java.util.Map;

/**
 * 例外ハンドラの種類
 */
@RequiredArgsConstructor
public enum RequestErrorHandlerType {
    INVALID_DOMAINS_EXCEPTION(InvalidDomainsException.class),
    RESOURCE_NOT_FOUND_EXCEPTION(ResourceNotFoundException.class),
    FILE_STORAGE_EXCEPTION(FileStorageException.class),
    DEFAULT_EXCEPTION(Exception.class);

    private final Class<? extends Exception> exceptionClass;

    public static RequestErrorHandlerType of(Exception e) {
        return Arrays.stream(values())
            .filter(type -> type.exceptionClass.isInstance(e))
            .findFirst()
            .orElse(DEFAULT_EXCEPTION);
    }

    /**
     * 例外ハンドラのクラスを取得する
     *
     * @return 例外ハンドラのクラス
     */
    public Class<?> getHandlerClass() {
        return switch (this) {
            case INVALID_DOMAINS_EXCEPTION -> InvalidDomainsExceptionHelper.class;
            case FILE_STORAGE_EXCEPTION -> FileStorageExceptionHelper.class;
            case RESOURCE_NOT_FOUND_EXCEPTION -> ResourceNotFoundExceptionHelper.class;
            default -> DefaultExceptionHelper.class;
        };
    }

    /**
     * 例外をハンドリングし、メッセージを返す
     *
     * @param e          例外
     * @param handlerMap 例外ハンドラのマップ
     * @return メッセージ
     */
    public String handle(
        Exception e,
        Map<Class<?>, RequestExceptionHandler> handlerMap
    ) {
        RequestExceptionHandler handler = handlerMap.get(this.getHandlerClass());
        return handler.handle(e);
    }

    /**
     * 例外をハンドリングし、BindingResultにエラーを追加する
     *
     * @param e             例外
     * @param handlerMap    例外ハンドラのマップ
     * @param bindingResult BindingResult
     */
    public void handle(
        Exception e,
        Map<Class<?>, RequestExceptionHandler> handlerMap,
        BindingResult bindingResult
    ) {
        RequestExceptionHandler handler = handlerMap.get(this.getHandlerClass());
        handler.handle(e, bindingResult);
    }
}
