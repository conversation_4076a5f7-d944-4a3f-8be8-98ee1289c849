package com.favick.adapter.in.web.controller.console.theme;

import com.favick.adapter.in.web.form.CreateThemeForm;
import com.favick.adapter.in.web.form.UpdateThemeForm;
import com.favick.adapter.in.web.helper.RequestErrorHandler;
import com.favick.application.dto.command.CreateThemeCommandDto;
import com.favick.application.dto.command.DeleteThemeCommandDto;
import com.favick.application.dto.command.UpdateThemeCommandDto;
import com.favick.application.dto.result.ThemeDetailResultDto;
import com.favick.application.dto.result.ThemeEditResultDto;
import com.favick.application.dto.result.ThemeSummaryResultDto;
import com.favick.application.port.in.*;
import com.favick.common.helper.MessageHelper;
import com.favick.common.type.MessageType;
import com.favick.domain.feature.language.model.LanguageCodeValue;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.List;
import java.util.Locale;
import java.util.UUID;

import static com.favick.adapter.in.web.mapper.ThemeWebMapper.*;

/**
 * テーマ管理画面のコントローラー
 */
@Controller
@RequestMapping("/console/themes")
@RequiredArgsConstructor
@Slf4j
public class ConsoleThemeController {
    private final ListThemesUseCase listThemesUseCase;
    private final CreateThemeUseCase createThemeUseCase;
    private final GetThemeUseCase getThemeUseCase;
    private final GetThemeForEditUseCase getThemeForEditUseCase;
    private final UpdateThemeUseCase updateThemeUseCase;
    private final DeleteThemeUseCase deleteThemeUseCase;
    private final LocaleResolver localeResolver;
    private final RequestErrorHandler requestErrorHandler;
    private final MessageHelper messageHelper;

    /**
     * テーマ一覧画面を表示する
     *
     * @param model モデル
     * @return テーマ一覧画面
     */
    @GetMapping
    public String index(Model model, HttpServletRequest request) {
        try {
            Locale locale = localeResolver.resolveLocale(request);
            LanguageCodeValue languageCode = LanguageCodeValue.valueOf(locale.getLanguage().toUpperCase());
            List<ThemeSummaryResultDto> themes = listThemesUseCase.handle(toListQueryDto(languageCode));
            model.addAttribute("themes", themes);
            return "pages/console/themes/index";
        } catch (Exception e) {
            log.error("Failed to load theme list", e);
            model.addAttribute("themes", List.of());
            return "pages/console/themes/index";
        }
    }

    /**
     * テーマ登録画面を表示する
     *
     * @param model モデル
     * @return テーマ登録画面
     */
    @GetMapping("/create")
    public String createForm(Model model) {
        if (!model.containsAttribute("form")) {
            model.addAttribute("form", new CreateThemeForm());
        }
        return "pages/console/themes/create";
    }

    /**
     * テーマを登録する
     *
     * @param form               テーマ登録フォーム
     * @param bindingResult      バリデーション結果
     * @param redirectAttributes リダイレクト属性
     * @param model              モデル
     * @return リダイレクト先
     */
    @PostMapping("/create")
    public String create(
        @ModelAttribute("form") @Valid CreateThemeForm form,
        BindingResult bindingResult,
        RedirectAttributes redirectAttributes,
        Model model
    ) {
        if (bindingResult.hasErrors()) {
            model.addAttribute("form", form);
            return "pages/console/themes/create";
        }

        try {
            CreateThemeCommandDto command = toCreateCommandDto(form);
            createThemeUseCase.handle(command);
            messageHelper.setMessage(redirectAttributes, MessageType.SUCCESS, "success.create");
            return "redirect:/console/themes";
        } catch (Exception e) {
            log.error("Theme creation failed: title={}, startDate={}", form.getTitleJa(), form.getStartDate(), e);
            requestErrorHandler.handle(e, bindingResult);
            model.addAttribute("form", form);
            return "pages/console/themes/create";
        }
    }

    /**
     * テーマ詳細画面を表示する
     *
     * @param id                 テーマID
     * @param redirectAttributes リダイレクト属性
     * @param model              モデル
     * @return テーマ詳細画面
     */
    @GetMapping("/{id}")
    public String show(
        @PathVariable("id") UUID id,
        RedirectAttributes redirectAttributes,
        Model model,
        HttpServletRequest request
    ) {
        try {
            Locale locale = localeResolver.resolveLocale(request);
            LanguageCodeValue languageCode = LanguageCodeValue.valueOf(locale.getLanguage().toUpperCase());
            ThemeDetailResultDto theme = getThemeUseCase.handle(toGetQueryDto(id, languageCode));
            model.addAttribute("theme", theme);
            return "pages/console/themes/show";
        } catch (Exception e) {
            log.error("Failed to load theme details: themeId={}", id, e);
            String message = requestErrorHandler.handle(e);
            redirectAttributes.addFlashAttribute("errorMessage", message);
            return "redirect:/console/themes";
        }
    }

    /**
     * テーマ編集画面を表示する
     *
     * @param id                 テーマID
     * @param redirectAttributes リダイレクト属性
     * @param model              モデル
     * @return テーマ編集画面
     */
    @GetMapping("/{id}/edit")
    public String updateForm(
        @PathVariable("id") UUID id,
        RedirectAttributes redirectAttributes,
        Model model,
        HttpServletRequest request
    ) {
        try {
            ThemeEditResultDto theme = getThemeForEditUseCase.handle(toGetForEditQueryDto(id));

            if (!theme.isEditable()) {
                log.warn("Theme is not editable: themeId={}", id);
                messageHelper.setMessage(redirectAttributes, MessageType.ERROR, "error.cannot_edit_past_or_present.theme");
                return "redirect:/console/themes";
            }

            model.addAttribute("theme", theme);

            if (!model.containsAttribute("form")) {
                UpdateThemeForm form = new UpdateThemeForm();
                form.setTitleJa(theme.titleJa());
                form.setDescriptionJa(theme.descriptionJa());
                form.setTitleEn(theme.titleEn());
                form.setDescriptionEn(theme.descriptionEn());
                form.setType(theme.type().name());
                form.setStartDate(theme.startDate());
                model.addAttribute("form", form);
            }
            return "pages/console/themes/edit";
        } catch (Exception e) {
            log.error("Failed to load theme for edit: themeId={}", id, e);
            String message = requestErrorHandler.handle(e);
            redirectAttributes.addFlashAttribute("errorMessage", message);
            return "redirect:/console/themes";
        }
    }

    /**
     * テーマを更新する
     *
     * @param id                 テーマID
     * @param form               テーマ更新フォーム
     * @param bindingResult      バリデーション結果
     * @param redirectAttributes リダイレクト属性
     * @param model              モデル
     * @return リダイレクト先
     */
    @PostMapping("/{id}/edit")
    public String update(
        @PathVariable("id") UUID id,
        @ModelAttribute("form") @Valid UpdateThemeForm form,
        BindingResult bindingResult,
        RedirectAttributes redirectAttributes,
        Model model,
        HttpServletRequest request
    ) {
        ThemeEditResultDto theme;

        try {
            theme = getThemeForEditUseCase.handle(toGetForEditQueryDto(id));
        } catch (Exception e) {
            log.error("Failed to load theme for edit: themeId={}", id, e);
            String message = requestErrorHandler.handle(e);
            redirectAttributes.addFlashAttribute("errorMessage", message);
            return "redirect:/console/themes";
        }

        if (bindingResult.hasErrors()) {
            model.addAttribute("theme", theme);
            model.addAttribute("form", form);
            return "pages/console/themes/edit";
        }

        try {
            UpdateThemeCommandDto command = toUpdateCommandDto(id, form);
            updateThemeUseCase.handle(command);
            messageHelper.setMessage(redirectAttributes, MessageType.SUCCESS, "success.update");
            return "redirect:/console/themes";
        } catch (Exception e) {
            log.error("Theme update failed: themeId={}, title={}", id, form.getTitleJa(), e);
            requestErrorHandler.handle(e, bindingResult);
            model.addAttribute("theme", theme);
            model.addAttribute("form", form);
            return "pages/console/themes/edit";
        }
    }

    /**
     * テーマを削除する
     *
     * @param id                 テーマID
     * @param redirectAttributes リダイレクト属性
     * @return リダイレクト先
     */
    @PostMapping("/{id}/delete")
    public String delete(
        @PathVariable("id") UUID id,
        RedirectAttributes redirectAttributes
    ) {
        try {
            DeleteThemeCommandDto command = toDeleteCommandDto(id);
            deleteThemeUseCase.handle(command);
            messageHelper.setMessage(redirectAttributes, MessageType.SUCCESS, "success.delete");
            return "redirect:/console/themes";
        } catch (Exception e) {
            log.error("Theme deletion failed: themeId={}", id, e);
            String message = requestErrorHandler.handle(e);
            redirectAttributes.addFlashAttribute("errorMessage", message);
            return "redirect:/console/themes";
        }
    }
}
