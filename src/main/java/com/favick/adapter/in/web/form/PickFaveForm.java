package com.favick.adapter.in.web.form;

import com.favick.common.validation.MaxFileSize;
import com.favick.common.validation.NotEmptyFile;
import com.favick.common.validation.ThemeFileType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

/**
 * お気に入り登録フォーム
 */
@Getter
@Setter
public class PickFaveForm {
    @NotEmptyFile
    @ThemeFileType
    @MaxFileSize
    private MultipartFile file;
}
