package com.favick.adapter.in.web.helper;

import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;

import java.util.Map;

/**
 * 例外ハンドラ
 */
@Component
public class RequestErrorHandler {
    private final Map<Class<?>, RequestExceptionHandler> handlerMap;

    public RequestErrorHandler(
        InvalidDomainsExceptionHelper invalidDomainsExceptionHelper,
        FileStorageExceptionHelper fileStorageExceptionHelper,
        ResourceNotFoundExceptionHelper resourceNotFoundExceptionHelper,
        DefaultExceptionHelper defaultExceptionHelper
    ) {
        this.handlerMap = Map.of(
            InvalidDomainsExceptionHelper.class, invalidDomainsExceptionHelper,
            FileStorageExceptionHelper.class, fileStorageExceptionHelper,
            ResourceNotFoundExceptionHelper.class, resourceNotFoundExceptionHelper,
            DefaultExceptionHelper.class, defaultExceptionHelper
        );
    }

    /**
     * 例外をハンドリングし、メッセージを返す
     *
     * @param e 例外
     * @return メッセージ
     */
    public String handle(Exception e) {
        return RequestErrorHandlerType.of(e).handle(e, handlerMap);
    }

    /**
     * 例外をハンドリングし、BindingResultにエラーを追加する
     *
     * @param e             例外
     * @param bindingResult BindingResult
     */
    public void handle(Exception e, BindingResult bindingResult) {
        RequestErrorHandlerType.of(e).handle(e, handlerMap, bindingResult);
    }
}
