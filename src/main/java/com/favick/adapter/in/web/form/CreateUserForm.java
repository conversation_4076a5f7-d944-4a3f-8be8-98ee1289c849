package com.favick.adapter.in.web.form;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

/**
 * ユーザー登録フォーム
 */
@Getter
@Setter
public class CreateUserForm {
    @NotBlank(message = "{validation.required.name}")
    @Size(max = 50, message = "{validation.too_long.name}")
    private String name;

    @NotBlank(message = "{validation.required.email}")
    @Size(max = 255, message = "{validation.too_long.email}")
    @Email(message = "{validation.invalid_format.email}")
    private String email;

    @NotBlank(message = "{validation.required.password}")
    @Size(min = 8, message = "{validation.too_short.password}")
    @Size(max = 72, message = "{validation.too_long.password}")
    private String password;

    @Size(max = 255, message = "{validation.too_long.image_url}")
    private String imageUrl;
}
