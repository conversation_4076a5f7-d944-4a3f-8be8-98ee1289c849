package com.favick.adapter.in.web.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * アーカイブ画面のコントローラー
 */
@Controller
@RequestMapping("/archives")
public class ArchiveController {
    /**
     * アーカイブ一覧画面を表示する
     *
     * @return アーカイブ一覧画面のテンプレート名
     */
    @GetMapping
    public String index() {
        return "pages/archives/index";
    }

    /**
     * アーカイブ詳細画面を表示する
     *
     * @param themeId テーマID
     * @param model   モデル
     * @return アーカイブ詳細画面のテンプレート名
     */
    @GetMapping("/{themeId}")
    public String show(
        @PathVariable Integer themeId,
        Model model
    ) {
        model.addAttribute("name", themeId);

        return "pages/archives/show";
    }
}
