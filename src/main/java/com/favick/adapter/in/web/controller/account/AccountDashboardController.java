package com.favick.adapter.in.web.controller.account;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * アカウントダッシュボード画面のコントローラー
 */
@Controller
@RequestMapping("/account")
public class AccountDashboardController {
    /**
     * アカウントダッシュボード画面を表示する
     *
     * @return テンプレート名
     */
    @GetMapping
    public String index() {
        return "pages/account/index";
    }
}
