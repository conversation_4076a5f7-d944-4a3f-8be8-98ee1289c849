package com.favick.adapter.in.web.mapper;

import com.favick.adapter.in.web.form.CreateThemeForm;
import com.favick.adapter.in.web.form.UpdateThemeForm;
import com.favick.application.dto.command.CreateThemeCommandDto;
import com.favick.application.dto.command.DeleteThemeCommandDto;
import com.favick.application.dto.command.LocalizedContent;
import com.favick.application.dto.command.UpdateThemeCommandDto;
import com.favick.application.dto.query.GetThemeForEditQueryDto;
import com.favick.application.dto.query.GetThemeQueryDto;
import com.favick.application.dto.query.ListThemesQueryDto;
import com.favick.domain.feature.language.model.LanguageCodeValue;

import java.util.Map;
import java.util.UUID;

/**
 * テーマフォームとDTOのマッパー
 * フォームからDTOへの変換を担当する
 */
public class ThemeWebMapper {
    /**
     * 言語コードからListThemesQueryDtoに変換する
     *
     * @param languageCode 言語コード
     * @return テーマ一覧クエリDTO
     */
    public static ListThemesQueryDto toListQueryDto(LanguageCodeValue languageCode) {
        return new ListThemesQueryDto(languageCode);
    }

    /**
     * テーマIDと言語コードからGetThemeQueryDtoに変換する
     *
     * @param themeId      テーマID
     * @param languageCode 言語コード
     * @return テーマ取得クエリDTO
     */
    public static GetThemeQueryDto toGetQueryDto(UUID themeId, LanguageCodeValue languageCode) {
        return new GetThemeQueryDto(themeId, languageCode);
    }

    /**
     * テーマIDからGetThemeForEditQueryDtoに変換する
     *
     * @param themeId テーマID
     * @return テーマ編集用取得クエリDTO
     */
    public static GetThemeForEditQueryDto toGetForEditQueryDto(UUID themeId) {
        return new GetThemeForEditQueryDto(themeId);
    }

    /**
     * CreateThemeFormをCreateThemeCommandDtoに変換する
     *
     * @param form 作成フォーム
     * @return 作成コマンドDTO
     */
    public static CreateThemeCommandDto toCreateCommandDto(CreateThemeForm form) {
        return new CreateThemeCommandDto(
            buildLocalizations(form.getTitleJa(), form.getDescriptionJa(), form.getTitleEn(), form.getDescriptionEn()),
            form.getType(),
            form.getStartDate()
        );
    }

    /**
     * UpdateThemeFormをUpdateThemeCommandDtoに変換する
     *
     * @param id   テーマID
     * @param form 更新フォーム
     * @return 更新コマンドDTO
     */
    public static UpdateThemeCommandDto toUpdateCommandDto(UUID id, UpdateThemeForm form) {
        return new UpdateThemeCommandDto(
            id,
            buildLocalizations(form.getTitleJa(), form.getDescriptionJa(), form.getTitleEn(), form.getDescriptionEn()),
            form.getType(),
            form.getStartDate()
        );
    }

    /**
     * テーマIDからDeleteThemeCommandDtoに変換する
     *
     * @param id テーマID
     * @return 削除コマンドDTO
     */
    public static DeleteThemeCommandDto toDeleteCommandDto(UUID id) {
        return new DeleteThemeCommandDto(id);
    }


    /**
     * フォームのタイトル・説明フィールドからローカライゼーションマップを作成する
     */
    private static Map<LanguageCodeValue, LocalizedContent> buildLocalizations(
        String titleJa, String descriptionJa,
        String titleEn, String descriptionEn
    ) {
        return Map.of(
            LanguageCodeValue.JA, new LocalizedContent(titleJa, descriptionJa),
            LanguageCodeValue.EN, new LocalizedContent(titleEn, descriptionEn)
        );
    }
}
