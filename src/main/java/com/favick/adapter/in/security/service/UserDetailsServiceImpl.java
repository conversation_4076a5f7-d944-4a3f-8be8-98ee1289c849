package com.favick.adapter.in.security.service;

import com.favick.adapter.in.security.model.UserDetailsImpl;
import com.favick.application.dto.result.UserDetailsResultDto;
import com.favick.application.port.in.LoadUserUseCase;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;

import static com.favick.adapter.in.security.mapper.UserDetailsMapper.toQueryByEmailDto;
import static com.favick.common.util.SecurityLogUtil.maskEmail;

@Service("userDetailsServiceImpl")
@RequiredArgsConstructor
@Slf4j
public class UserDetailsServiceImpl implements UserDetailsService {
    private final LoadUserUseCase loadUserUseCase;

    @Override
    public UserDetails loadUserByUsername(String email) throws UsernameNotFoundException {
        try {
            UserDetailsResultDto loadUser = loadUserUseCase.handle(toQueryByEmailDto(email));

            Collection<GrantedAuthority> authorities = new ArrayList<>();
            authorities.add(new SimpleGrantedAuthority(loadUser.rankName().name()));

            return new UserDetailsImpl(loadUser, authorities);
        } catch (UsernameNotFoundException e) {
            log.warn("Authentication failed - user not found: email={}", maskEmail(email));
            throw e;
        } catch (Exception e) {
            log.error("Authentication failed - system error: email={}", maskEmail(email), e);
            throw new UsernameNotFoundException("Authentication failed", e);
        }
    }
}
