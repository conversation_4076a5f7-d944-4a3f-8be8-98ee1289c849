package com.favick.adapter.in.security.expression;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Component;

@Component("rankAuth")
public class RankAuthorization {
    public boolean hasRank(Authentication authentication, String rank) {
        if (authentication == null || !authentication.isAuthenticated()) return false;
        String expected = "RANK_" + rank;
        return authentication.getAuthorities().stream()
            .map(GrantedAuthority::getAuthority)
            .anyMatch(auth -> auth.equals(expected));
    }
}
