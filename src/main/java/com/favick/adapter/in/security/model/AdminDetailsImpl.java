package com.favick.adapter.in.security.model;

import com.favick.application.dto.result.AdminDetailsResultDto;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;

@RequiredArgsConstructor
public class AdminDetailsImpl implements UserDetails {
    @Getter
    private final AdminDetailsResultDto admin;
    private final Collection<GrantedAuthority> authorities;

    @Override
    public String getPassword() {
        return this.admin.password();
    }

    @Override
    public String getUsername() {
        return this.admin.email();
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return this.authorities;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }
}
