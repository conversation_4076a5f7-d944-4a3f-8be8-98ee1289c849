package com.favick.adapter.in.security.mapper;

import com.favick.application.dto.query.LoadAdminQueryDto;

/**
 * 管理者情報とDTOのマッパー
 * 管理者情報からDTOへの変換を担当する
 */
public class AdminDetailsMapper {
    /**
     * emailをGetAdminByEmailQueryDtoに変換する
     *
     * @param email メールアドレス
     * @return 管理者取得クエリDTO
     */
    public static LoadAdminQueryDto toQueryByEmailDto(String email) {
        return new LoadAdminQueryDto(email);
    }
}
