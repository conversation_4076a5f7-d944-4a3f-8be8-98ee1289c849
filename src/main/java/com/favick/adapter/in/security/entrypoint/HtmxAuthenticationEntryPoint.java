package com.favick.adapter.in.security.entrypoint;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.LoginUrlAuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * HTMX リクエスト用の認証エントリーポイント
 * セッション切れ時にHTMXリクエストには適切なレスポンスを返す
 */
@Component
public class HtmxAuthenticationEntryPoint extends LoginUrlAuthenticationEntryPoint {
    public HtmxAuthenticationEntryPoint() {
        super("/auth/login");
    }

    @Override
    public void commence(
        HttpServletRequest request,
        HttpServletResponse response,
        AuthenticationException authException
    ) throws IOException, ServletException {

        String hxRequestHeader = request.getHeader("HX-Request");

        if ("true".equals(hxRequestHeader)) {
            // HTMXリクエストの場合：ログインページにリダイレクト
            response.setStatus(HttpServletResponse.SC_OK);
            response.setHeader("HX-Redirect", "/auth/login");
            return;
        }

        // 通常リクエストの場合：標準の認証フロー
        super.commence(request, response, authException);
    }
}
