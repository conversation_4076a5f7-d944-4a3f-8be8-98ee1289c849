package com.favick.adapter.in.security.service;

import com.favick.adapter.in.security.model.AdminDetailsImpl;
import com.favick.application.dto.result.AdminDetailsResultDto;
import com.favick.application.port.in.LoadAdminUseCase;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;

import static com.favick.adapter.in.security.mapper.AdminDetailsMapper.toQueryByEmailDto;
import static com.favick.common.util.SecurityLogUtil.maskEmail;

@Service("adminDetailsServiceImpl")
@RequiredArgsConstructor
@Slf4j
public class AdminDetailsServiceImpl implements UserDetailsService {
    private final LoadAdminUseCase loadAdminUseCase;

    @Override
    public UserDetails loadUserByUsername(String email) throws UsernameNotFoundException {
        try {
            AdminDetailsResultDto loadAdmin = loadAdminUseCase.handle(toQueryByEmailDto(email));

            Collection<GrantedAuthority> authorities = new ArrayList<>();
            authorities.add(new SimpleGrantedAuthority(loadAdmin.roleName().name()));

            return new AdminDetailsImpl(loadAdmin, authorities);
        } catch (UsernameNotFoundException e) {
            log.warn("Admin authentication failed - user not found: email={}", maskEmail(email));
            throw e;
        } catch (Exception e) {
            log.error("Admin authentication failed - system error: email={}", maskEmail(email), e);
            throw new UsernameNotFoundException("Admin authentication failed", e);
        }
    }
}
