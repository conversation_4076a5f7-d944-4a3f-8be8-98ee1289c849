package com.favick.adapter.in.security.mapper;

import com.favick.application.dto.query.LoadUserQueryDto;

/**
 * ユーザー情報とDTOのマッパー
 * ユーザー情報からDTOへの変換を担当する
 */
public class UserDetailsMapper {
    /**
     * emailをGetUserByEmailQueryDtoに変換する
     *
     * @param email メールアドレス
     * @return ユーザー取得クエリDTO
     */
    public static LoadUserQueryDto toQueryByEmailDto(String email) {
        return new LoadUserQueryDto(email);
    }
}
