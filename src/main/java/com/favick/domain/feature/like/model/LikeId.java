package com.favick.domain.feature.like.model;

import com.favick.domain.exception.ValueObjectException;

import java.util.UUID;

/**
 * いいねIDを表す値オブジェクト
 * UUIDでいいねを一意に識別する
 */
public record LikeId(UUID value) {
    public LikeId {
        if (value == null) {
            throw new ValueObjectException("id", "validation.required.like_id");
        }
    }

    public static LikeId generate() {
        return new LikeId(UUID.randomUUID());
    }
}
