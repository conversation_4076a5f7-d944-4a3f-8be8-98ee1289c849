package com.favick.domain.feature.like.model;

import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.feature.user.model.UserId;
import com.favick.domain.validation.ValueObjectValidator;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * いいね（Like）ドメインエンティティ
 * ユーザーがFaveに対して行ういいねを表す
 */
@Getter
@EqualsAndHashCode(of = "id")
public class Like {
    private final LikeId id;
    private final UserId userId;
    private final FaveId faveId;
    private final LocalDateTime createdAt;

    private Like(
        LikeId id,
        UserId userId,
        FaveId faveId,
        LocalDateTime createdAt
    ) {
        this.id = ValueObjectValidator.requireNonNull(id, "id", "validation.required.like_id");
        this.userId = ValueObjectValidator.requireNonNull(userId, "userId", "validation.required.user_id");
        this.faveId = ValueObjectValidator.requireNonNull(faveId, "faveId", "validation.required.fave_id");
        this.createdAt = ValueObjectValidator.requireNonNull(createdAt, "createdAt", "validation.required.created_at");
    }

    /**
     * DBからいいねを再構築する
     *
     * @param id        いいねID
     * @param userId    ユーザーID
     * @param faveId    FaveID
     * @param createdAt 作成日時
     * @return 再構築したいいね
     */
    public static Like reconstruct(
        LikeId id,
        UserId userId,
        FaveId faveId,
        LocalDateTime createdAt
    ) {
        return new Like(id, userId, faveId, createdAt);
    }

    /**
     * 新しいいいねを作成する
     *
     * @param userId ユーザーID
     * @param faveId FaveID
     * @return 作成したいいね
     */
    public static Like create(UserId userId, FaveId faveId) {
        return new Like(
            LikeId.generate(),
            userId,
            faveId,
            LocalDateTime.now()
        );
    }

    @Override
    public String toString() {
        return "Like{id=" + id.value() + ", userId=" + userId.value() + ", faveId=" + faveId.value() + "}";
    }
}
