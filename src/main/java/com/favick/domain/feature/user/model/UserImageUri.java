package com.favick.domain.feature.user.model;

import com.favick.domain.exception.ValueObjectException;

/**
 * ユーザーの画像URIを表す値オブジェクト
 */
public record UserImageUri(String value) {
    private static final int MAX_LENGTH = 255;

    public UserImageUri(String value) {
        if (value == null) {
            this.value = "";
            return;
        }
        if (value.length() > MAX_LENGTH) {
            throw new ValueObjectException("image_url", "validation.too_long.image_url", MAX_LENGTH);
        }
        this.value = value;
    }
}
