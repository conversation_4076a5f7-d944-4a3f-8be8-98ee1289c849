package com.favick.domain.feature.user.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.UUID;

/**
 * ユーザー登録完了ドメインイベント
 * ユーザーが登録されたことを通知する
 */
@Getter
public class UserRegisteredEvent extends ApplicationEvent {
    private final UUID userId;
    private final String userEmail;
    private final String origin;

    public UserRegisteredEvent(Object source, UUID userId, String userEmail, String origin) {
        super(source);
        this.userId = userId;
        this.userEmail = userEmail;
        this.origin = origin;
    }
}
