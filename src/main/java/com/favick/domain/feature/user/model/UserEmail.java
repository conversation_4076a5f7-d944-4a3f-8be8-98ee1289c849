package com.favick.domain.feature.user.model;

import com.favick.domain.exception.ValueObjectException;

import java.util.regex.Pattern;

/**
 * ユーザーのメールアドレスを表す値オブジェクト
 */
public record UserEmail(String value) {
    private static final int MAX_LENGTH = 255;
    private static final int MAX_LOCAL_LENGTH = 64;
    private static final Pattern PATTERN = Pattern.compile("(?i)^" +
        "(?:[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+" +
        "|\"(?:[a-zA-Z0-9!#$%&'*.(),<>\\[\\]:; @+/=?^_`{|}~-]|\\\\\\\\|\\\\\")+\")" +
        "(?:\\." +
        "(?:[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+" +
        "|\"(?:[a-zA-Z0-9!#$%&'*.(),<>\\[\\]:; @+/=?^_`{|}~-]|\\\\\\\\|\\\\\")+\")" +
        ")*" +
        "@" +
        "(?:[a-zA-Z0-9-]+(?:\\.[a-zA-Z0-9-]+)*" +
        "|\\[(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(?:\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}]" +
        "|\\[IPv6:(?:[0-9a-f]+:){2,7}[0-9a-f]+]" +
        ")$");

    public UserEmail {
        if (value == null || value.trim().isEmpty()) {
            throw new ValueObjectException("email", "validation.required.email");
        }
        if (value.length() > MAX_LENGTH) {
            throw new ValueObjectException("email", "validation.too_long.email", MAX_LENGTH);
        }
        int atIndex = value.lastIndexOf('@');
        if (atIndex < 0 || atIndex > MAX_LOCAL_LENGTH) {
            throw new ValueObjectException("email", "validation.invalid_format.email");
        }
        if (!PATTERN.matcher(value).matches()) {
            throw new ValueObjectException("email", "validation.invalid_format.email");
        }
    }
}
