package com.favick.domain.feature.user.model;

import com.favick.domain.exception.ValueObjectException;

import java.util.UUID;

/**
 * ユーザーIDを表す値オブジェクト
 * UUIDでユーザーを一意に識別する
 */
public record UserId(UUID value) {
    public UserId {
        if (value == null) {
            throw new ValueObjectException("id", "validation.required.user_id");
        }
    }

    public static UserId generate() {
        return new UserId(UUID.randomUUID());
    }
}
