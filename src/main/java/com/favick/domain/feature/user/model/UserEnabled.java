package com.favick.domain.feature.user.model;

/**
 * ユーザーの有効フラグを表す値オブジェクト
 */
public record UserEnabled(boolean value) {

    /**
     * ユーザーを有効にする
     *
     * @return 有効なユーザー
     */
    public UserEnabled enable() {
        return new UserEnabled(true);
    }

    /**
     * ユーザーを無効にする
     *
     * @return 無効なユーザー
     */
    public UserEnabled disable() {
        return new UserEnabled(false);
    }
}
