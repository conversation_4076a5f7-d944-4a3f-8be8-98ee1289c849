package com.favick.domain.feature.user.service;

import com.favick.domain.exception.ValueObjectException;
import com.favick.domain.feature.user.model.UserEmail;

import java.util.function.Predicate;

/**
 * ユーザードメインサービス
 * ユーザードメインモデルのビジネスロジックを提供する
 */
public class UserDomainService {
    /**
     * メールアドレスが他のユーザーと重複していないか検証する
     *
     * @param email                検証対象のメールアドレス
     * @param emailExistsPredicate メールアドレスが存在するかを確認する述語関数
     * @throws ValueObjectException メールアドレスが重複している場合
     */
    public void validateEmailUniqueness(
        UserEmail email,
        Predicate<UserEmail> emailExistsPredicate
    ) {
        if (emailExistsPredicate.test(email)) {
            throw new ValueObjectException("email", "validation.must_be.unique.email");
        }
    }
}
