package com.favick.domain.feature.user.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.UUID;

/**
 * パスワードリセットドメインイベント
 * ユーザーがパスワードリセットを要求したことを通知する
 */
@Getter
public class PasswordResetEvent extends ApplicationEvent {
    private final UUID userId;
    private final String userEmail;
    private final String origin;

    public PasswordResetEvent(Object source, UUID userId, String userEmail, String origin) {
        super(source);
        this.userId = userId;
        this.userEmail = userEmail;
        this.origin = origin;
    }
}
