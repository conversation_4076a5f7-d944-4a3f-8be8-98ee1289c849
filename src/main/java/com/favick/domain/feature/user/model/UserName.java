package com.favick.domain.feature.user.model;

import com.favick.domain.exception.ValueObjectException;

/**
 * ユーザー名を表す値オブジェクト
 */
public record UserName(String value) {
    private static final int MAX_LENGTH = 50;

    public UserName {
        if (value == null || value.trim().isEmpty()) {
            throw new ValueObjectException("name", "validation.required.name");
        }
        if (value.length() > MAX_LENGTH) {
            throw new ValueObjectException("name", "validation.too_long.name", MAX_LENGTH);
        }
    }
}
