package com.favick.domain.feature.user.model;

import com.favick.domain.feature.rank.model.RankId;
import com.favick.domain.validation.ValueObjectValidator;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * ユーザー（User）ドメインエンティティ
 * ユーザー情報を表す
 */
@Getter
@EqualsAndHashCode(of = "id")
public class User {
    private final UserId id;
    private final LocalDateTime createdAt;
    private UserName name;
    private UserEmail email;
    private UserPassword password;
    private RankId rankId;
    private UserEnabled enabled;
    private UserImageUri imageUrl;
    private LocalDateTime updatedAt;

    private User(
        UserId id,
        RankId rankId,
        UserName name,
        UserEmail email,
        UserPassword password,
        UserEnabled enabled,
        UserImageUri imageUrl,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
    ) {
        this.id = ValueObjectValidator.requireNonNull(id, "id", "validation.required.user_id");
        this.rankId = ValueObjectValidator.requireNonNull(rankId, "rankId", "validation.required.rank_id");
        this.name = ValueObjectValidator.requireNonNull(name, "name", "validation.required.name");
        this.email = ValueObjectValidator.requireNonNull(email, "email", "validation.required.email");
        this.password = ValueObjectValidator.requireNonNull(password, "password", "validation.required.password");
        this.enabled = enabled;
        this.imageUrl = imageUrl;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    /**
     * DBからユーザーを再構築する
     *
     * @param id        ユーザーID
     * @param rankId    ランクID
     * @param name      ユーザー名
     * @param email     メールアドレス
     * @param password  パスワード
     * @param enabled   有効かどうか
     * @param imageUrl  ユーザー画像のURL
     * @param createdAt 作成日時
     * @param updatedAt 更新日時
     * @return 再構築したユーザー
     */
    public static User reconstruct(
        UserId id,
        RankId rankId,
        UserName name,
        UserEmail email,
        UserPassword password,
        UserEnabled enabled,
        UserImageUri imageUrl,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
    ) {
        return new User(
            id,
            rankId,
            name,
            email,
            password,
            enabled,
            imageUrl,
            createdAt,
            updatedAt
        );
    }

    /**
     * 新しいユーザーを作成する
     *
     * @param rankId   ランクID
     * @param name     ユーザー名
     * @param email    メールアドレス
     * @param password パスワード
     * @param imageUrl ユーザー画像のURL
     * @return 作成したユーザー
     */
    public static User create(
        RankId rankId,
        UserName name,
        UserEmail email,
        UserPassword password,
        UserImageUri imageUrl
    ) {
        LocalDateTime now = LocalDateTime.now();

        return new User(
            UserId.generate(),
            rankId,
            name,
            email,
            password,
            new UserEnabled(false),
            imageUrl,
            now,
            now
        );
    }

    /**
     * ユーザーを有効にする
     */
    public void enable() {
        this.enabled = this.enabled.enable();
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * ユーザーを無効にする
     */
    public void disable() {
        this.enabled = this.enabled.disable();
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * ユーザーが有効かどうかを返す
     *
     * @return ユーザーが有効かどうか
     */
    public boolean isEnabled() {
        return this.enabled.value();
    }

    /**
     * ユーザーのプロフィールを更新する
     *
     * @param name     ユーザー名
     * @param email    メールアドレス
     * @param imageUrl ユーザー画像のURL
     */
    public void updateProfile(UserName name, UserEmail email, UserImageUri imageUrl) {
        this.name = ValueObjectValidator.requireNonNull(name, "name", "validation.required.name");
        this.email = ValueObjectValidator.requireNonNull(email, "email", "validation.required.email");
        this.imageUrl = imageUrl;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * ユーザーのパスワードを変更する
     *
     * @param newPassword 新しいパスワード
     */
    public void changePassword(UserPassword newPassword) {
        this.password = this.password.changePassword(newPassword);
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * ユーザーのランクを変更する
     *
     * @param newRankId 新しいランクID
     */
    public void changeRank(RankId newRankId) {
        this.rankId = ValueObjectValidator.requireNonNull(newRankId, "rankId", "validation.required.rank_id");
        this.updatedAt = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return "User{" + "id=" + id.value() + ", name=" + name.value() + ", email=" + email.value() + ", rankId=" + rankId.value() + ", imageUrl=" + imageUrl.value() + ", enabled=" + enabled.value() + '}';
    }
}
