package com.favick.domain.feature.user.model;

import com.favick.domain.exception.ValueObjectException;

import java.util.regex.Pattern;

/**
 * ユーザー用平文パスワードを表す値オブジェクト
 */
public record RawUserPassword(String value) {
    private static final int MIN_LENGTH = 8;
    private static final int MAX_LENGTH = 72;
    private static final Pattern PASSWORD_PATTERN =
        Pattern.compile("^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,72}$");
    private static final String[] COMMON_PASSWORDS = {
        "password", "12345678", "11111111"
    };

    public RawUserPassword {
        if (value == null || value.trim().isEmpty()) {
            throw new ValueObjectException("password", "validation.required.password");
        }
        if (value.length() < MIN_LENGTH) {
            throw new ValueObjectException("password", "validation.too_short.password", MIN_LENGTH);
        }
        if (value.length() > MAX_LENGTH) {
            throw new ValueObjectException("password", "validation.too_long.password", MAX_LENGTH);
        }
        if (!PASSWORD_PATTERN.matcher(value).matches()) {
            throw new ValueObjectException("password", "validation.invalid_format.password");
        }
        for (String common : COMMON_PASSWORDS) {
            if (value.trim().equalsIgnoreCase(common.trim())) {
                throw new ValueObjectException("password", "validation.weak.password");
            }
        }
    }
}
