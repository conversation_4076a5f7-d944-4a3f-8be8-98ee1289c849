package com.favick.domain.feature.rank.model;

import com.favick.domain.exception.ValueObjectException;

import java.util.UUID;

/**
 * ランクIDを表す値オブジェクト
 * UUIDでランクを一意に識別する
 */
public record RankId(UUID value) {
    public RankId {
        if (value == null) {
            throw new ValueObjectException("id", "validation.required.rank_id");
        }
    }

    public static RankId generate() {
        return new RankId(UUID.randomUUID());
    }
}
