package com.favick.domain.feature.rank.model;

import com.favick.domain.validation.ValueObjectValidator;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * ランク（Rank）ドメインエンティティ
 * ユーザーのランクを表す
 */
@Getter
@EqualsAndHashCode(of = "id")
public class Rank {
    private final RankId id;
    private final LocalDateTime createdAt;
    private RankName name;
    private LocalDateTime updatedAt;

    private Rank(
        RankId id,
        RankName name,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
    ) {
        this.id = ValueObjectValidator.requireNonNull(id, "id", "validation.required.rank_id");
        this.name = ValueObjectValidator.requireNonNull(name, "name", "validation.required.name");
        this.createdAt = ValueObjectValidator.requireNonNull(createdAt, "createdAt", "validation.required.created_at");
        this.updatedAt = ValueObjectValidator.requireNonNull(updatedAt, "updatedAt", "validation.required.updated_at");
    }

    /**
     * DBからランクを再構築する
     *
     * @param id        ランクID
     * @param name      名前
     * @param createdAt 作成日時
     * @param updatedAt 更新日時
     * @return 再構築したランク
     */
    public static Rank reconstruct(
        RankId id,
        RankName name,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
    ) {
        return new Rank(
            id,
            name,
            createdAt,
            updatedAt
        );
    }

    /**
     * 新しいランクを作成する
     *
     * @param name 名前
     * @return 作成したランク
     */
    public static Rank create(
        RankName name
    ) {
        LocalDateTime now = LocalDateTime.now();

        return new Rank(
            RankId.generate(),
            name,
            now,
            now
        );
    }

    /**
     * ランク情報を更新する
     *
     * @param name 名前
     */
    public void update(
        RankName name
    ) {
        this.name = ValueObjectValidator.requireNonNull(name, "name", "validation.required.name");
        this.updatedAt = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return "Rank{" + "id=" + id.value() + ", name=" + name.value() + '}';
    }
}
