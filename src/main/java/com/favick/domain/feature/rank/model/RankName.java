package com.favick.domain.feature.rank.model;

import com.favick.domain.exception.ValueObjectException;

/**
 * ランクの名前を表す値オブジェクト
 */
public record RankName(RankNameValue value) {
    public RankName {
        if (value == null) {
            throw new ValueObjectException("name", "validation.required.name");
        }
    }

    /**
     * 文字列からRankNameを作成するファクトリメソッド
     */
    public static RankName fromString(String name) {
        if (name == null) {
            throw new ValueObjectException("name", "validation.required.name");
        }
        try {
            return new RankName(RankNameValue.valueOf(name));
        } catch (Exception e) {
            throw new ValueObjectException("name", "validation.invalid.name", name);
        }
    }
}
