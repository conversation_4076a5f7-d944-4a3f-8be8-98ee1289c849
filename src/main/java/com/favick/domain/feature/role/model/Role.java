package com.favick.domain.feature.role.model;

import com.favick.domain.validation.ValueObjectValidator;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * 権限（Role）ドメインエンティティ
 * 管理者の権限を表す
 */
@Getter
@EqualsAndHashCode(of = "id")
public class Role {
    private final RoleId id;
    private final LocalDateTime createdAt;
    private RoleName name;
    private LocalDateTime updatedAt;

    private Role(
        RoleId id,
        RoleName name,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
    ) {
        this.id = ValueObjectValidator.requireNonNull(id, "id", "validation.required.language_id");
        this.name = ValueObjectValidator.requireNonNull(name, "name", "validation.required.name");
        this.createdAt = ValueObjectValidator.requireNonNull(createdAt, "createdAt", "validation.required.created_at");
        this.updatedAt = ValueObjectValidator.requireNonNull(updatedAt, "updatedAt", "validation.required.updated_at");
    }

    /**
     * DBから権限を再構築する
     *
     * @param id        権限ID
     * @param name      権限名
     * @param createdAt 作成日時
     * @param updatedAt 更新日時
     * @return 再構築した権限
     */
    public static Role reconstruct(
        RoleId id,
        RoleName name,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
    ) {
        return new Role(
            id,
            name,
            createdAt,
            updatedAt
        );
    }

    /**
     * 新しい権限を作成する
     *
     * @param name 名前
     * @return 作成した権限
     */
    public static Role create(
        RoleName name
    ) {
        LocalDateTime now = LocalDateTime.now();

        return new Role(
            RoleId.generate(),
            name,
            now,
            now
        );
    }

    /**
     * 権限情報を更新する
     *
     * @param name 名前
     */
    public void update(
        RoleName name
    ) {
        this.name = ValueObjectValidator.requireNonNull(name, "name", "validation.required.name");
        this.updatedAt = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return "Role{" + "id=" + id.value() + ", name=" + name.value() + '}';
    }
}
