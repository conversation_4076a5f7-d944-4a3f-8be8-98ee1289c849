package com.favick.domain.feature.role.model;

import com.favick.domain.exception.ValueObjectException;

import java.util.UUID;

/**
 * 権限IDを表す値オブジェクト
 * UUIDで権限を一意に識別する
 */
public record RoleId(UUID value) {
    public RoleId {
        if (value == null) {
            throw new ValueObjectException("id", "validation.required.role_id");
        }
    }

    public static RoleId generate() {
        return new RoleId(UUID.randomUUID());
    }
}
