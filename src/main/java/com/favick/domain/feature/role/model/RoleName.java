package com.favick.domain.feature.role.model;

import com.favick.domain.exception.ValueObjectException;

/**
 * 権限の名前を表す値オブジェクト
 */
public record RoleName(RoleNameValue value) {
    public RoleName {
        if (value == null) {
            throw new ValueObjectException("name", "validation.required.name");
        }
    }

    /**
     * 文字列からRoleNameを作成するファクトリメソッド
     */
    public static RoleName fromString(String name) {
        if (name == null) {
            throw new ValueObjectException("name", "validation.required.name");
        }
        try {
            return new RoleName(RoleNameValue.valueOf(name));
        } catch (Exception e) {
            throw new ValueObjectException("name", "validation.invalid.name", name);
        }
    }
}
