package com.favick.domain.feature.language.model;

import com.favick.domain.validation.ValueObjectValidator;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * 言語（Language）ドメインエンティティ
 * アプリケーションで使用する言語を表す
 */
@Getter
@EqualsAndHashCode(of = "id")
public class Language {
    private final LanguageId id;
    private final LocalDateTime createdAt;
    private LanguageCode code;
    private LanguageDisplayName displayName;
    private LocalDateTime updatedAt;

    private Language(
        LanguageId id,
        LanguageCode code,
        LanguageDisplayName displayName,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
    ) {
        this.id = ValueObjectValidator.requireNonNull(id, "id", "validation.required.language_id");
        this.code = ValueObjectValidator.requireNonNull(code, "code", "validation.required.code");
        this.displayName = ValueObjectValidator.requireNonNull(displayName, "displayName", "validation.required.display_name");
        this.createdAt = ValueObjectValidator.requireNonNull(createdAt, "createdAt", "validation.required.created_at");
        this.updatedAt = ValueObjectValidator.requireNonNull(updatedAt, "updatedAt", "validation.required.updated_at");
    }

    /**
     * DBから言語を再構築する
     *
     * @param id          言語ID
     * @param code        コード
     * @param displayName 表示名
     * @param createdAt   作成日時
     * @param updatedAt   更新日時
     * @return 再構築した言語
     */
    public static Language reconstruct(
        LanguageId id,
        LanguageCode code,
        LanguageDisplayName displayName,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
    ) {
        return new Language(
            id,
            code,
            displayName,
            createdAt,
            updatedAt
        );
    }

    /**
     * 新しい言語を作成する
     *
     * @param code        コード
     * @param displayName 表示名
     * @return 作成した言語
     */
    public static Language create(
        LanguageCode code,
        LanguageDisplayName displayName
    ) {
        LocalDateTime now = LocalDateTime.now();

        return new Language(
            LanguageId.generate(),
            code,
            displayName,
            now,
            now
        );
    }

    /**
     * 言語情報を更新する
     *
     * @param code        コード
     * @param displayName 表示名
     */
    public void update(
        LanguageCode code,
        LanguageDisplayName displayName
    ) {
        this.code = ValueObjectValidator.requireNonNull(code, "code", "validation.required.code");
        this.displayName = ValueObjectValidator.requireNonNull(displayName, "displayName", "validation.required.display_name");
        this.updatedAt = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return "Language{id='" + id.value() + '\'' + ", code=" + code.value() + ", displayName=" + displayName.value() + '}';
    }
}
