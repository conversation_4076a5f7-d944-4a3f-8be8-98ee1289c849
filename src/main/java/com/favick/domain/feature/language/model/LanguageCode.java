package com.favick.domain.feature.language.model;

import com.favick.domain.exception.ValueObjectException;

/**
 * 言語のコードを表す値オブジェクト
 */
public record LanguageCode(LanguageCodeValue value) {
    public LanguageCode {
        if (value == null) {
            throw new ValueObjectException("code", "validation.required.code");
        }
    }

    /**
     * 文字列からLanguageCodeを作成するファクトリメソッド
     */
    public static LanguageCode fromString(String code) {
        if (code == null) {
            throw new ValueObjectException("code", "validation.required.code");
        }
        try {
            return new LanguageCode(LanguageCodeValue.valueOf(code));
        } catch (Exception e) {
            throw new ValueObjectException("code", "validation.invalid.code", code);
        }
    }

    /**
     * フィールド名用のサフィックスを取得する
     */
    public String getFieldSuffix() {
        String code = value.name();
        return code.substring(0, 1).toUpperCase() + code.substring(1).toLowerCase();
    }
}
