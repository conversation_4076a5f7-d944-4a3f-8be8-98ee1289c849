package com.favick.domain.feature.language.model;

import com.favick.domain.exception.ValueObjectException;

/**
 * 言語の表示名を表す値オブジェクト
 */
public record LanguageDisplayName(String value) {
    private static final int MAX_LENGTH = 50;

    public LanguageDisplayName {
        if (value == null || value.trim().isEmpty()) {
            throw new ValueObjectException("display_name", "validation.required.display_name");
        }
        if (value.length() > MAX_LENGTH) {
            throw new ValueObjectException("display_name", "validation.too_long.display_name", MAX_LENGTH);
        }
    }
}
