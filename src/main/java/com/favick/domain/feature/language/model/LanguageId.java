package com.favick.domain.feature.language.model;

import com.favick.domain.exception.ValueObjectException;

import java.util.UUID;

/**
 * 言語IDを表す値オブジェクト
 * UUIDで言語を一意に識別する
 */
public record LanguageId(UUID value) {
    public LanguageId {
        if (value == null) {
            throw new ValueObjectException("id", "validation.required.language_id");
        }
    }

    public static LanguageId generate() {
        return new LanguageId(UUID.randomUUID());
    }
}
