package com.favick.domain.feature.theme_localization.model;

import com.favick.domain.feature.language.model.LanguageCode;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.validation.ValueObjectValidator;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * テーマローカライゼーション（ThemeLocalization）ドメインモデル
 * 言語ごとのテーマ情報を表す
 */
@Getter
@EqualsAndHashCode(of = {"id", "themeId", "languageCode"})
public class ThemeLocalization {
    private final ThemeLocalizationId id;
    private final ThemeId themeId;
    private final LanguageCode languageCode;
    private final LocalDateTime createdAt;
    private ThemeTitle title;
    private ThemeDescription description;
    private LocalDateTime updatedAt;

    private ThemeLocalization(
        ThemeLocalizationId id,
        ThemeId themeId,
        LanguageCode languageCode,
        ThemeTitle title,
        ThemeDescription description,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
    ) {
        this.id = ValueObjectValidator.requireNonNull(id, "id", "validation.required.theme_localization_id");
        this.themeId = ValueObjectValidator.requireNonNull(themeId, "themeId", "validation.required.theme_id");
        this.languageCode = ValueObjectValidator.requireNonNull(languageCode, "languageCode", "validation.required.language_code");
        this.title = ValueObjectValidator.requireNonNull(title, "title", "validation.required.title");
        this.description = ValueObjectValidator.requireNonNull(description, "description", "validation.required.description");
        this.createdAt = ValueObjectValidator.requireNonNull(createdAt, "createdAt", "validation.required.created_at");
        this.updatedAt = ValueObjectValidator.requireNonNull(updatedAt, "updatedAt", "validation.required.updated_at");
    }

    /**
     * DBからテーマローカライゼーションを再構築する
     *
     * @param id           テーマローカライゼーションID
     * @param themeId      テーマID
     * @param languageCode 言語コード
     * @param title        タイトル
     * @param description  説明
     * @param createdAt    作成日時
     * @param updatedAt    更新日時
     * @return 再構築したテーマローカライゼーション
     */
    public static ThemeLocalization reconstruct(
        ThemeLocalizationId id,
        ThemeId themeId,
        LanguageCode languageCode,
        ThemeTitle title,
        ThemeDescription description,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
    ) {
        return new ThemeLocalization(
            id,
            themeId,
            languageCode,
            title,
            description,
            createdAt,
            updatedAt
        );
    }

    /**
     * テーマローカライゼーションを作成する
     *
     * @param themeId      テーマID
     * @param languageCode 言語コード
     * @param title        タイトル
     * @param description  説明
     * @return 作成したテーマローカライゼーション
     */
    public static ThemeLocalization create(
        ThemeId themeId,
        LanguageCode languageCode,
        ThemeTitle title,
        ThemeDescription description
    ) {
        LocalDateTime now = LocalDateTime.now();

        return new ThemeLocalization(
            ThemeLocalizationId.generate(),
            themeId,
            languageCode,
            title,
            description,
            now,
            now
        );
    }

    /**
     * テーマローカライゼーション情報を更新する
     *
     * @param title       タイトル
     * @param description 説明
     */
    public void update(
        ThemeTitle title,
        ThemeDescription description
    ) {
        this.title = ValueObjectValidator.requireNonNull(title, "title", "validation.required.title");
        this.description = ValueObjectValidator.requireNonNull(description, "description", "validation.required.description");
        this.updatedAt = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return "ThemeLocalization{id='" + id.value() + '\'' + ", themeId=" + themeId.value() + ", languageCode=" + languageCode.value() + ", title=" + title.value() + ", description=" + description.value() + '}';
    }
}
