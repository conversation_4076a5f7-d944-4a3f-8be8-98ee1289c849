package com.favick.domain.feature.theme_localization.model;

import com.favick.domain.exception.ValueObjectException;
import lombok.EqualsAndHashCode;

/**
 * テーマの説明を表す値オブジェクト
 */
@EqualsAndHashCode(of = "value")
public class ThemeDescription {
    private static final int MAX_LENGTH = 255;
    private final String value;

    public ThemeDescription(String value) {
        this(value, "description");
    }

    public ThemeDescription(String value, String fieldName) {
        validate(value, fieldName);
        this.value = value;
    }

    private static void validate(String value, String fieldName) {
        if (value == null || value.trim().isEmpty()) {
            throw new ValueObjectException(fieldName, "validation.required.description");
        }
        if (value.length() > MAX_LENGTH) {
            throw new ValueObjectException(fieldName, "validation.too_long.description", MAX_LENGTH);
        }
    }

    public String value() {
        return this.value;
    }

    @Override
    public String toString() {
        return "ThemeDescription[value=" + value + "]";
    }
}
