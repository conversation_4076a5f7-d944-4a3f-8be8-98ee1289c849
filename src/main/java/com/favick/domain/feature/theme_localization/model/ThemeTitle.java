package com.favick.domain.feature.theme_localization.model;

import com.favick.domain.exception.ValueObjectException;
import lombok.EqualsAndHashCode;

/**
 * テーマのタイトルを表す値オブジェクト
 */
@EqualsAndHashCode(of = "value")
public class ThemeTitle {
    private static final int MAX_LENGTH = 50;
    private final String value;

    public ThemeTitle(String value) {
        this(value, "title");
    }

    public ThemeTitle(String value, String fieldName) {
        validate(value, fieldName);
        this.value = value;
    }

    private static void validate(String value, String fieldName) {
        if (value == null || value.trim().isEmpty()) {
            throw new ValueObjectException(fieldName, "validation.required.title");
        }
        if (value.length() > MAX_LENGTH) {
            throw new ValueObjectException(fieldName, "validation.too_long.title", MAX_LENGTH);
        }
    }

    public String value() {
        return this.value;
    }

    @Override
    public String toString() {
        return "ThemeTitle[value=" + value + "]";
    }
}
