package com.favick.domain.feature.theme_localization.model;

import com.favick.domain.exception.ValueObjectException;

import java.util.UUID;

/**
 * テーマローカライゼーションIDを表す値オブジェクト
 * UUIDでテーマローカライゼーションを一意に識別する
 */
public record ThemeLocalizationId(UUID value) {
    public ThemeLocalizationId {
        if (value == null) {
            throw new ValueObjectException("id", "validation.required.theme_localization_id");
        }
    }

    public static ThemeLocalizationId generate() {
        return new ThemeLocalizationId(UUID.randomUUID());
    }
}
