package com.favick.domain.feature.review.model;

import com.favick.domain.exception.ValueObjectException;

/**
 * 審査のステータスを表す値オブジェクト
 */
public record ReviewStatus(ReviewStatusValue value) {
    public ReviewStatus {
        if (value == null) {
            throw new ValueObjectException("status", "validation.required.status");
        }
    }

    /**
     * 文字列からReviewStatusを作成するファクトリメソッド
     */
    public static ReviewStatus fromString(String status) {
        if (status == null) {
            throw new ValueObjectException("status", "validation.required.status");
        }
        try {
            return new ReviewStatus(ReviewStatusValue.valueOf(status));
        } catch (Exception e) {
            throw new ValueObjectException("status", "validation.invalid.status", status);
        }
    }

    /**
     * 審査中かどうか
     */
    public boolean isPending() {
        return value == ReviewStatusValue.PENDING;
    }

    /**
     * 承認済みかどうか
     */
    public boolean isApproved() {
        return value == ReviewStatusValue.APPROVED;
    }

    /**
     * 却下済みかどうか
     */
    public boolean isRejected() {
        return value == ReviewStatusValue.REJECTED;
    }
}
