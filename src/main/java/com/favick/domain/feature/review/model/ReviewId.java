package com.favick.domain.feature.review.model;

import com.favick.domain.exception.ValueObjectException;

import java.util.UUID;

/**
 * 審査IDを表す値オブジェクト
 * UUIDで審査を一意に識別する
 */
public record ReviewId(UUID value) {
    public ReviewId {
        if (value == null) {
            throw new ValueObjectException("id", "validation.required.review_id");
        }
    }

    public static ReviewId generate() {
        return new ReviewId(UUID.randomUUID());
    }
}
