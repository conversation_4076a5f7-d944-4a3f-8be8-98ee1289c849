package com.favick.domain.feature.review.model;

import com.favick.domain.feature.admin.model.AdminId;
import com.favick.domain.feature.fave.model.FaveId;
import com.favick.domain.validation.ValueObjectValidator;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * 審査（Review）ドメインエンティティ
 * 管理者によるお気に入りの審査結果を表す
 */
@Getter
@EqualsAndHashCode(of = "id")
public class Review {
    private final ReviewId id;
    private final FaveId faveId;
    private final LocalDateTime createdAt;
    private AdminId adminId;
    private ReviewStatus status;
    private LocalDateTime updatedAt;

    private Review(
        ReviewId id,
        FaveId faveId,
        AdminId adminId,
        ReviewStatus status,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
    ) {
        this.id = ValueObjectValidator.requireNonNull(id, "id", "validation.required.review_id");
        this.faveId = ValueObjectValidator.requireNonNull(faveId, "faveId", "validation.required.fave_id");
        this.adminId = adminId;
        this.status = ValueObjectValidator.requireNonNull(status, "status", "validation.required.status");
        this.createdAt = ValueObjectValidator.requireNonNull(createdAt, "createdAt", "validation.required.created_at");
        this.updatedAt = ValueObjectValidator.requireNonNull(updatedAt, "updatedAt", "validation.required.updated_at");
    }

    /**
     * DBから審査を再構築する
     *
     * @param id        審査ID
     * @param faveId    お気に入りID
     * @param adminId   管理者ID（未審査の場合はnull）
     * @param status    ステータス
     * @param createdAt 作成日時
     * @param updatedAt 更新日時
     * @return 再構築した審査
     */
    public static Review reconstruct(
        ReviewId id,
        FaveId faveId,
        AdminId adminId,
        ReviewStatus status,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
    ) {
        return new Review(
            id,
            faveId,
            adminId,
            status,
            createdAt,
            updatedAt
        );
    }

    /**
     * 新しい審査を作成する
     *
     * @param faveId お気に入りID
     * @return 作成した審査
     */
    public static Review create(
        FaveId faveId
    ) {
        LocalDateTime now = LocalDateTime.now();

        return new Review(
            ReviewId.generate(),
            faveId,
            null,
            new ReviewStatus(ReviewStatusValue.PENDING),
            now,
            now
        );
    }

    /**
     * 審査を保留する
     */
    public void pending() {
        this.status = new ReviewStatus(ReviewStatusValue.PENDING);
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 審査を承認する
     *
     * @param adminId 承認した管理者ID
     */
    public void approve(AdminId adminId) {
        ValueObjectValidator.requireNonNull(adminId, "adminId", "validation.required.admin_id");
        this.adminId = adminId;
        this.status = new ReviewStatus(ReviewStatusValue.APPROVED);
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 審査を却下する
     *
     * @param adminId 却下した管理者ID
     */
    public void reject(AdminId adminId) {
        ValueObjectValidator.requireNonNull(adminId, "adminId", "validation.required.admin_id");
        this.adminId = adminId;
        this.status = new ReviewStatus(ReviewStatusValue.REJECTED);
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 審査中かどうか
     */
    public boolean isPending() {
        return status.isPending();
    }

    /**
     * 承認済みかどうか
     */
    public boolean isApproved() {
        return status.isApproved();
    }

    /**
     * 却下済みかどうか
     */
    public boolean isRejected() {
        return status.isRejected();
    }

    @Override
    public String toString() {
        String adminIdStr = adminId != null ? adminId.value().toString() : "null";
        return "Review{id=" + id.value() + ", faveId=" + faveId.value() + ", adminId=" + adminIdStr + ", status=" + status.value() + "}";
    }
}
