package com.favick.domain.feature.theme.model;

import com.favick.domain.exception.ValueObjectException;

/**
 * テーマの状態を表す値オブジェクト
 */
public record ThemeStatus(ThemeStatusValue value) {
    public ThemeStatus {
        if (value == null) {
            throw new ValueObjectException("status", "validation.required.status");
        }
    }

    /**
     * 文字列からThemeStatusを作成するファクトリメソッド
     */
    public static ThemeStatus fromString(String status) {
        if (status == null) {
            throw new ValueObjectException("status", "validation.required.status");
        }
        try {
            return new ThemeStatus(ThemeStatusValue.valueOf(status));
        } catch (Exception e) {
            throw new ValueObjectException("status", "validation.invalid.status", status);
        }
    }
}
