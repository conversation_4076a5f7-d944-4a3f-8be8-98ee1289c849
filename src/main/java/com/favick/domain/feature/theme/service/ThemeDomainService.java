package com.favick.domain.feature.theme.service;

import com.favick.domain.exception.ValueObjectException;
import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.theme.model.ThemeStartDate;

import java.util.function.BiPredicate;
import java.util.function.Predicate;

/**
 * テーマドメインサービス
 * テーマドメインモデルのビジネスロジックを提供する
 */
public class ThemeDomainService {
    /**
     * テーマの開始日が他のテーマと重複していないか検証する（新規作成用）
     *
     * @param startDate           検証対象の開始日
     * @param dateExistsPredicate 開始日が存在するかを確認する述語関数
     * @throws ValueObjectException 開始日が重複している場合
     */
    public void validateStartDateUniqueness(
        ThemeStartDate startDate,
        Predicate<ThemeStartDate> dateExistsPredicate
    ) {
        if (dateExistsPredicate.test(startDate)) {
            throw new ValueObjectException("startDate", "validation.must_be.unique.date");
        }
    }

    /**
     * テーマの開始日が他のテーマと重複していないか検証する（更新用）
     *
     * @param startDate           検証対象の開始日
     * @param themeId             除外するテーマID
     * @param dateExistsPredicate 開始日が存在するかを確認する述語関数（テーマIDを除外）
     * @throws ValueObjectException 開始日が重複している場合
     */
    public void validateStartDateUniqueness(
        ThemeId themeId,
        ThemeStartDate startDate,
        BiPredicate<ThemeStartDate, ThemeId> dateExistsPredicate
    ) {
        if (dateExistsPredicate.test(startDate, themeId)) {
            throw new ValueObjectException("startDate", "validation.must_be.unique.date");
        }
    }
}
