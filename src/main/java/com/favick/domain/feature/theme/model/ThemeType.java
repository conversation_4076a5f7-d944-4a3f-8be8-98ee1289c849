package com.favick.domain.feature.theme.model;

import com.favick.domain.exception.ValueObjectException;

/**
 * テーマ種別を表す値オブジェクト
 */
public record ThemeType(ThemeTypeValue value) {
    public ThemeType {
        if (value == null) {
            throw new ValueObjectException("type", "validation.required.type");
        }
    }

    /**
     * 文字列からThemeTypeを作成するファクトリメソッド
     */
    public static ThemeType fromString(String type) {
        if (type == null) {
            throw new ValueObjectException("type", "validation.required.type");
        }
        try {
            return new ThemeType(ThemeTypeValue.valueOf(type));
        } catch (Exception e) {
            throw new ValueObjectException("type", "validation.invalid.type", type);
        }
    }
}
