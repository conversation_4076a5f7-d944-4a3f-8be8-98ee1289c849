package com.favick.domain.feature.theme.model;

import com.favick.domain.exception.ValueObjectException;

import java.util.UUID;

/**
 * テーマのIDを表す値オブジェクト
 * UUIDを使用してテーマを一意に識別する
 */
public record ThemeId(UUID value) {
    public ThemeId {
        if (value == null) {
            throw new ValueObjectException("id", "validation.required.theme_id");
        }
    }

    public static ThemeId generate() {
        return new ThemeId(UUID.randomUUID());
    }
}
