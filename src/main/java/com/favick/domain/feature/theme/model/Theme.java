package com.favick.domain.feature.theme.model;

import com.favick.domain.exception.EntityException;
import com.favick.domain.exception.ValueObjectException;
import com.favick.domain.validation.ValueObjectValidator;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * テーマ（Theme）ドメインエンティティ
 * 特定の期間に募集されるテーマを表す
 */
@Getter
@EqualsAndHashCode(of = "id")
public class Theme {
    private final ThemeId id;
    private final LocalDateTime createdAt;
    private ThemeType type;
    private ThemeStartDate startDate;
    private LocalDateTime updatedAt;

    private Theme(
        ThemeId id,
        ThemeType type,
        ThemeStartDate startDate,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
    ) {
        this.id = ValueObjectValidator.requireNonNull(id, "id", "validation.required.theme_id");
        this.type = ValueObjectValidator.requireNonNull(type, "type", "validation.required.type");
        this.startDate = ValueObjectValidator.requireNonNull(startDate, "startDate", "validation.required.start_date");
        this.createdAt = ValueObjectValidator.requireNonNull(createdAt, "createdAt", "validation.required.created_at");
        this.updatedAt = ValueObjectValidator.requireNonNull(updatedAt, "updatedAt", "validation.required.updated_at");
    }

    /**
     * DBからテーマを再構築する
     *
     * @param id        テーマID
     * @param type      種別
     * @param startDate 開始日
     * @param createdAt 作成日時
     * @param updatedAt 更新日時
     * @return 再構築したテーマ
     */
    public static Theme reconstruct(
        ThemeId id,
        ThemeType type,
        ThemeStartDate startDate,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
    ) {
        return new Theme(
            id,
            type,
            startDate,
            createdAt,
            updatedAt
        );
    }

    /**
     * 新しいテーマを作成する
     *
     * @param type      種別
     * @param startDate 開始日
     * @return 作成したテーマ
     * @throws ValueObjectException ドメインルール違反が発生した場合
     */
    public static Theme create(
        ThemeType type,
        ThemeStartDate startDate
    ) {
        if (startDate.value().isBefore(LocalDate.now())) {
            throw new ValueObjectException("startDate", "validation.must_be.future_or_present.date");
        }

        LocalDateTime now = LocalDateTime.now();

        return new Theme(
            ThemeId.generate(),
            type,
            startDate,
            now,
            now
        );
    }

    /**
     * テーマ情報を更新する
     *
     * @param type      新しいタイプ
     * @param startDate 新しい開始日
     * @throws ValueObjectException ドメインルール違反が発生した場合
     */
    public void update(
        ThemeType type,
        ThemeStartDate startDate
    ) {
        if (!isEditable()) {
            throw new EntityException("error.cannot_edit_past_or_present.theme");
        }
        if (startDate.value().isBefore(LocalDate.now())) {
            throw new ValueObjectException("startDate", "validation.must_be.future_or_present.date");
        }

        LocalDateTime now = LocalDateTime.now();

        this.type = ValueObjectValidator.requireNonNull(type, "type", "validation.required.type");
        this.startDate = ValueObjectValidator.requireNonNull(startDate, "startDate", "validation.required.start_date");
        this.updatedAt = now;
    }

    /**
     * テーマの現在の状態を取得する
     */
    public ThemeStatus getCurrentStatus() {
        LocalDate today = LocalDate.now();
        LocalDate themeStartDate = this.startDate.value();

        if (themeStartDate.isAfter(today)) {
            return new ThemeStatus(ThemeStatusValue.UPCOMING);
        } else if (themeStartDate.isEqual(today)) {
            return new ThemeStatus(ThemeStatusValue.ACTIVE);
        } else {
            return new ThemeStatus(ThemeStatusValue.PAST);
        }
    }

    /**
     * テーマが編集可能かどうかを判定する
     */
    public boolean isEditable() {
        return getCurrentStatus().value() == ThemeStatusValue.UPCOMING;
    }

    @Override
    public String toString() {
        return "Theme{id='" + id.value() + '\'' + ", type=" + type.value() + ", startDate=" + startDate.value() + '}';
    }
}
