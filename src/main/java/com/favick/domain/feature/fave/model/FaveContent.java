package com.favick.domain.feature.fave.model;

import com.favick.domain.exception.ValueObjectException;

/**
 * お気に入りの内容を表す値オブジェクト
 */
public record FaveContent(String value) {
    private static final int MAX_LENGTH = 255;

    public FaveContent {
        if (value == null || value.trim().isEmpty()) {
            throw new ValueObjectException("content", "validation.required.content");
        }
        if (value.length() > MAX_LENGTH) {
            throw new ValueObjectException("content", "validation.too_long.content", MAX_LENGTH);
        }
    }
}
