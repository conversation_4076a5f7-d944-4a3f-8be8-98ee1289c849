package com.favick.domain.feature.fave.model;

import com.favick.domain.feature.theme.model.ThemeId;
import com.favick.domain.feature.user.model.UserId;
import com.favick.domain.validation.ValueObjectValidator;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * お気に入り（Fave）ドメインエンティティ
 * ユーザーがテーマに対して選んだお気に入りを表す
 */
@Getter
@EqualsAndHashCode(of = "id")
public class Fave {
    private final FaveId id;
    private final UserId userId;
    private final ThemeId themeId;
    private final LocalDateTime createdAt;
    private FaveContent content;
    private LocalDateTime updatedAt;

    private Fave(
        FaveId id,
        UserId userId,
        ThemeId themeId,
        FaveContent content,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
    ) {
        this.id = ValueObjectValidator.requireNonNull(id, "id", "validation.required.fave_id");
        this.userId = ValueObjectValidator.requireNonNull(userId, "userId", "validation.required.user_id");
        this.themeId = ValueObjectValidator.requireNonNull(themeId, "themeId", "validation.required.theme_id");
        this.content = ValueObjectValidator.requireNonNull(content, "content", "validation.required.content");
        this.createdAt = ValueObjectValidator.requireNonNull(createdAt, "createdAt", "validation.required.created_at");
        this.updatedAt = ValueObjectValidator.requireNonNull(updatedAt, "updatedAt", "validation.required.updated_at");
    }

    /**
     * DBからお気に入りを再構築する
     *
     * @param id        お気に入りID
     * @param userId    ユーザーID
     * @param themeId   テーマID
     * @param content   お気に入りの内容
     * @param createdAt 作成日時
     * @param updatedAt 更新日時
     * @return 再構築したお気に入り
     */
    public static Fave reconstruct(
        FaveId id,
        UserId userId,
        ThemeId themeId,
        FaveContent content,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
    ) {
        return new Fave(
            id,
            userId,
            themeId,
            content,
            createdAt,
            updatedAt
        );
    }

    /**
     * 新しいお気に入りを作成する
     *
     * @param userId  ユーザーID
     * @param themeId テーマID
     * @param content お気に入りの内容
     * @return 作成したお気に入り
     */
    public static Fave create(
        UserId userId,
        ThemeId themeId,
        FaveContent content
    ) {
        LocalDateTime now = LocalDateTime.now();

        return new Fave(
            FaveId.generate(),
            userId,
            themeId,
            content,
            now,
            now
        );
    }

    /**
     * お気に入りの内容を更新する
     *
     * @param content お気に入りの内容
     */
    public void updateContent(FaveContent content) {
        this.content = ValueObjectValidator.requireNonNull(content, "content", "validation.required.content");
        this.updatedAt = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return "Fave{id=" + id.value() + ", userId=" + userId.value() + ", themeId=" + themeId.value() + "}";
    }
}
