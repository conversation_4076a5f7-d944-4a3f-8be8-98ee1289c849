package com.favick.domain.feature.fave.model;

import com.favick.domain.exception.ValueObjectException;

import java.util.UUID;

/**
 * お気に入りIDを表す値オブジェクト
 * UUIDでお気に入りを一意に識別する
 */
public record FaveId(UUID value) {
    public FaveId {
        if (value == null) {
            throw new ValueObjectException("id", "validation.required.fave_id");
        }
    }

    public static FaveId generate() {
        return new FaveId(UUID.randomUUID());
    }
}
