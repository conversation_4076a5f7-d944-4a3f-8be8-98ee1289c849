package com.favick.domain.feature.token.model;

import com.favick.domain.exception.ValueObjectException;

import java.util.UUID;

/**
 * トークンの値を表す値オブジェクト
 */
public record TokenValue(String value) {
    public TokenValue {
        if (value == null || value.trim().isEmpty()) {
            throw new ValueObjectException("token", "validation.required.token");
        }
    }

    public static TokenValue generate() {
        return new TokenValue(UUID.randomUUID().toString());
    }
}
