package com.favick.domain.feature.token.model;

import com.favick.domain.feature.user.model.UserId;
import com.favick.domain.validation.ValueObjectValidator;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * パスワードリセットトークン（PasswordResetToken）ドメインエンティティ
 * ユーザーのパスワードリセットに使用するトークンを表す
 */
@Getter
@EqualsAndHashCode(of = "id")
public class PasswordResetToken {
    private static final int EXPIRE_HOURS = 2;

    private final PasswordResetTokenId id;
    private final UserId userId;
    private final TokenValue token;
    private final TokenExpireAt expireAt;
    private final LocalDateTime createdAt;

    private PasswordResetToken(
        PasswordResetTokenId id,
        UserId userId,
        TokenValue token,
        TokenExpireAt expireAt,
        LocalDateTime createdAt
    ) {
        this.id = ValueObjectValidator.requireNonNull(id, "id", "validation.required.token_id");
        this.userId = ValueObjectValidator.requireNonNull(userId, "userId", "validation.required.user_id");
        this.token = ValueObjectValidator.requireNonNull(token, "token", "validation.required.token");
        this.expireAt = ValueObjectValidator.requireNonNull(expireAt, "expireAt", "validation.required.expire_at");
        this.createdAt = ValueObjectValidator.requireNonNull(createdAt, "createdAt", "validation.required.created_at");
    }

    /**
     * DBからパスワードリセットトークンを再構築する
     */
    public static PasswordResetToken reconstruct(
        PasswordResetTokenId id,
        UserId userId,
        TokenValue token,
        TokenExpireAt expireAt,
        LocalDateTime createdAt
    ) {
        return new PasswordResetToken(id, userId, token, expireAt, createdAt);
    }

    /**
     * 新しいパスワードリセットトークンを作成する
     */
    public static PasswordResetToken create(UserId userId) {
        LocalDateTime now = LocalDateTime.now();

        return new PasswordResetToken(
            PasswordResetTokenId.generate(),
            userId,
            TokenValue.generate(),
            TokenExpireAt.fromHours(EXPIRE_HOURS),
            now
        );
    }

    /**
     * トークンが有効期限切れかチェックする
     */
    public boolean isExpired() {
        return expireAt.isExpired();
    }

    @Override
    public String toString() {
        return "PasswordResetToken{id=" + id.value() + ", userId=" + userId.value() + ", token=" + token.value() + '}';
    }
}
