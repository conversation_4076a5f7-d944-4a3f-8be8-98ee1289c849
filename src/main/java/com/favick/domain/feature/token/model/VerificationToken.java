package com.favick.domain.feature.token.model;

import com.favick.domain.feature.user.model.UserId;
import com.favick.domain.validation.ValueObjectValidator;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * 認証トークン（VerificationToken）ドメインエンティティ
 * ユーザーのメールアドレス確認に使用するトークンを表す
 */
@Getter
@EqualsAndHashCode(of = "id")
public class VerificationToken {
    private static final int EXPIRE_HOURS = 24;

    private final VerificationTokenId id;
    private final UserId userId;
    private final TokenValue token;
    private final TokenExpireAt expireAt;
    private final LocalDateTime createdAt;

    private VerificationToken(
        VerificationTokenId id,
        UserId userId,
        TokenValue token,
        TokenExpireAt expireAt,
        LocalDateTime createdAt
    ) {
        this.id = ValueObjectValidator.requireNonNull(id, "id", "validation.required.token_id");
        this.userId = ValueObjectValidator.requireNonNull(userId, "userId", "validation.required.user_id");
        this.token = ValueObjectValidator.requireNonNull(token, "token", "validation.required.token");
        this.expireAt = ValueObjectValidator.requireNonNull(expireAt, "expireAt", "validation.required.expire_at");
        this.createdAt = ValueObjectValidator.requireNonNull(createdAt, "createdAt", "validation.required.created_at");
    }

    /**
     * DBから認証トークンを再構築する
     */
    public static VerificationToken reconstruct(
        VerificationTokenId id,
        UserId userId,
        TokenValue token,
        TokenExpireAt expireAt,
        LocalDateTime createdAt
    ) {
        return new VerificationToken(id, userId, token, expireAt, createdAt);
    }

    /**
     * 新しい認証トークンを作成する
     */
    public static VerificationToken create(UserId userId) {
        LocalDateTime now = LocalDateTime.now();

        return new VerificationToken(
            VerificationTokenId.generate(),
            userId,
            TokenValue.generate(),
            TokenExpireAt.fromHours(EXPIRE_HOURS),
            now
        );
    }

    /**
     * トークンが有効期限切れかチェックする
     */
    public boolean isExpired() {
        return expireAt.isExpired();
    }

    @Override
    public String toString() {
        return "VerificationToken{id=" + id.value() + ", userId=" + userId.value() + ", token=" + token.value() + '}';
    }
}
