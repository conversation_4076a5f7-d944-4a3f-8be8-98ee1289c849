package com.favick.domain.feature.token.model;

import com.favick.domain.exception.ValueObjectException;

import java.util.UUID;

/**
 * パスワードリセットトークンIDを表す値オブジェクト
 * UUIDでパスワードリセットトークンを一意に識別する
 */
public record PasswordResetTokenId(UUID value) {
    public PasswordResetTokenId {
        if (value == null) {
            throw new ValueObjectException("id", "validation.required.token_id");
        }
    }

    public static PasswordResetTokenId generate() {
        return new PasswordResetTokenId(UUID.randomUUID());
    }
}
