package com.favick.domain.feature.token.model;

import com.favick.domain.exception.ValueObjectException;

import java.util.UUID;

/**
 * 認証トークンIDを表す値オブジェクト
 * UUIDで認証トークンを一意に識別する
 */
public record VerificationTokenId(UUID value) {
    public VerificationTokenId {
        if (value == null) {
            throw new ValueObjectException("id", "validation.required.token_id");
        }
    }

    public static VerificationTokenId generate() {
        return new VerificationTokenId(UUID.randomUUID());
    }
}
