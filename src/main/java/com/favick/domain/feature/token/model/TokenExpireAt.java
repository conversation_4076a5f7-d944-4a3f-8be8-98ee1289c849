package com.favick.domain.feature.token.model;

import com.favick.domain.exception.ValueObjectException;

import java.time.LocalDateTime;

/**
 * トークンの有効期限を表す値オブジェクト
 */
public record TokenExpireAt(LocalDateTime value) {
    public TokenExpireAt {
        if (value == null) {
            throw new ValueObjectException("expireAt", "validation.required.expire_at");
        }
    }

    /**
     * 指定した時間後に期限切れになるトークン有効期限を作成する
     *
     * @param hours 有効期限（時間）
     * @return 有効期限
     */
    public static TokenExpireAt fromHours(int hours) {
        if (hours <= 0) {
            throw new ValueObjectException("hours", "validation.positive.hours");
        }
        return new TokenExpireAt(LocalDateTime.now().plusHours(hours));
    }

    /**
     * 有効期限が切れているかチェックする
     *
     * @return 有効期限切れの場合true
     */
    public boolean isExpired() {
        return value.isBefore(LocalDateTime.now());
    }
}
