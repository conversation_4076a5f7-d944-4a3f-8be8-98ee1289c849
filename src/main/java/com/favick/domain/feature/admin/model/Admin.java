package com.favick.domain.feature.admin.model;

import com.favick.domain.feature.role.model.RoleId;
import com.favick.domain.validation.ValueObjectValidator;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * 管理者（Admin）ドメインエンティティ
 * 管理者情報を表す
 */
@Getter
@EqualsAndHashCode(of = "id")
public class Admin {
    private final AdminId id;
    private final LocalDateTime createdAt;
    private AdminName name;
    private AdminEmail email;
    private AdminPassword password;
    private RoleId roleId;
    private LocalDateTime updatedAt;

    private Admin(
        AdminId id,
        RoleId roleId,
        AdminName name,
        AdminEmail email,
        AdminPassword password,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
    ) {
        this.id = ValueObjectValidator.requireNonNull(id, "id", "validation.required.admin_id");
        this.roleId = ValueObjectValidator.requireNonNull(roleId, "roleId", "validation.required.role_id");
        this.name = ValueObjectValidator.requireNonNull(name, "name", "validation.required.name");
        this.email = ValueObjectValidator.requireNonNull(email, "email", "validation.required.email");
        this.password = ValueObjectValidator.requireNonNull(password, "password", "validation.required.password");
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    /**
     * DBから管理者を再構築する
     *
     * @param id        管理者ID
     * @param roleId    権限ID
     * @param name      管理者名
     * @param email     メールアドレス
     * @param password  パスワード
     * @param createdAt 作成日時
     * @param updatedAt 更新日時
     * @return 再構築した管理者
     */
    public static Admin reconstruct(
        AdminId id,
        RoleId roleId,
        AdminName name,
        AdminEmail email,
        AdminPassword password,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
    ) {
        return new Admin(
            id,
            roleId,
            name,
            email,
            password,
            createdAt,
            updatedAt
        );
    }

    /**
     * 新しい管理者を作成する
     *
     * @param roleId   権限ID
     * @param name     管理者名
     * @param email    メールアドレス
     * @param password パスワード
     * @return 作成した管理者
     */
    public static Admin create(
        RoleId roleId,
        AdminName name,
        AdminEmail email,
        AdminPassword password
    ) {
        LocalDateTime now = LocalDateTime.now();

        return new Admin(
            AdminId.generate(),
            roleId,
            name,
            email,
            password,
            now,
            now
        );
    }

    /**
     * 管理者のプロフィールを更新する
     *
     * @param name  ユーザー名
     * @param email メールアドレス
     */
    public void updateProfile(AdminName name, AdminEmail email) {
        this.name = ValueObjectValidator.requireNonNull(name, "name", "validation.required.name");
        this.email = ValueObjectValidator.requireNonNull(email, "email", "validation.required.email");
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 管理者のパスワードを変更する
     *
     * @param newPassword 新しいパスワード
     */
    public void changePassword(AdminPassword newPassword) {
        this.password = this.password.changePassword(newPassword);
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 管理者の権限を変更する
     *
     * @param newRoleId 新しい権限ID
     */
    public void changeRole(RoleId newRoleId) {
        this.roleId = ValueObjectValidator.requireNonNull(newRoleId, "roleId", "validation.required.role_id");
        this.updatedAt = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return "Admin{" + "id=" + id.value() + ", name=" + name.value() + ", email=" + email.value() + ", roleId=" + roleId.value() + '}';
    }
}
