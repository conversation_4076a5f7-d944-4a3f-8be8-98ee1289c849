package com.favick.domain.feature.admin.model;

import com.favick.domain.exception.ValueObjectException;

import java.util.UUID;

/**
 * 管理者IDを表す値オブジェクト
 * UUIDで管理者を一意に識別する
 */
public record AdminId(UUID value) {
    public AdminId {
        if (value == null) {
            throw new ValueObjectException("id", "validation.required.admin_id");
        }
    }

    public static AdminId generate() {
        return new AdminId(UUID.randomUUID());
    }
}
