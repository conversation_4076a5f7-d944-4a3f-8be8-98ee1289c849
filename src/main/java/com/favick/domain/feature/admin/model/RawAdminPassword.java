package com.favick.domain.feature.admin.model;

import com.favick.domain.exception.ValueObjectException;

import java.util.regex.Pattern;

/**
 * 管理者用平文パスワードを表す値オブジェクト
 */
public record RawAdminPassword(String value) {
    private static final int MIN_LENGTH = 12;
    private static final int MAX_LENGTH = 72;
    private static final Pattern PASSWORD_PATTERN =
        Pattern.compile("^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{12,72}$");
    private static final String[] COMMON_PASSWORDS = {
        "password", "12345678", "123456789", "11111111",
        "admin1234", "Admin1234", "Password1", "Admin@123", "Welcome1",
        "Changeme1", "P@ssw0rd", "Aa123456", "Admin123!", "Qwerty123"
    };

    public RawAdminPassword {
        if (value == null || value.trim().isEmpty()) {
            throw new ValueObjectException("password", "validation.required.password");
        }
        if (value.length() < MIN_LENGTH) {
            throw new ValueObjectException("password", "validation.too_short.password", MIN_LENGTH);
        }
        if (value.length() > MAX_LENGTH) {
            throw new ValueObjectException("password", "validation.too_long.password", MAX_LENGTH);
        }
        if (!PASSWORD_PATTERN.matcher(value).matches()) {
            throw new ValueObjectException("password", "validation.invalid_format.password");
        }
        for (String common : COMMON_PASSWORDS) {
            if (value.trim().equalsIgnoreCase(common.trim())) {
                throw new ValueObjectException("password", "validation.weak.password");
            }
        }
    }
}
