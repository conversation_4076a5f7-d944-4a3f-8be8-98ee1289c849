package com.favick.domain.feature.admin.model;

import com.favick.domain.exception.ValueObjectException;

/**
 * 管理者名を表す値オブジェクト
 */
public record AdminName(String value) {
    private static final int MAX_LENGTH = 50;

    public AdminName {
        if (value == null || value.trim().isEmpty()) {
            throw new ValueObjectException("name", "validation.required.name");
        }
        if (value.length() > MAX_LENGTH) {
            throw new ValueObjectException("name", "validation.too_long.name", MAX_LENGTH);
        }
    }
}
