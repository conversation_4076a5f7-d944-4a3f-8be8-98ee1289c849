package com.favick.domain.exception;

import lombok.Getter;

/**
 * ドメインオブジェクトが不正な場合にスローされる例外
 */
@Getter
public class DomainException extends RuntimeException {
    private final String messageKey;
    private final Object[] args;
    private final String field;

    public DomainException(String messageKey, Object... args) {
        super("Domain Error");
        this.messageKey = messageKey;
        this.args = args;
        this.field = null;
    }

    public DomainException(String field, String messageKey, Object... args) {
        super("Domain Error");
        this.messageKey = messageKey;
        this.args = args;
        this.field = field;
    }

    public boolean hasField() {
        return field != null;
    }
}
