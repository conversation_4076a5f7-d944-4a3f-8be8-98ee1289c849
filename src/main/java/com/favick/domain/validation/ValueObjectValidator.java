package com.favick.domain.validation;

import com.favick.domain.exception.ValueObjectException;

/**
 * ドメイン層のバリデータ
 * ドメイン層で共通のバリデーションロジックを提供する
 */
public class ValueObjectValidator {
    /**
     * 値がnullでないことを検証する
     *
     * @param value      検証対象の値
     * @param field      フィールド名
     * @param messageKey エラーメッセージのキー
     * @param args       エラーメッセージの引数
     * @return 検証結果
     * @throws ValueObjectException 値がnullの場合
     */
    public static <T> T requireNonNull(T value, String field, String messageKey, Object... args) {
        if (value == null) {
            throw new ValueObjectException(field, messageKey, args);
        }
        return value;
    }
}
