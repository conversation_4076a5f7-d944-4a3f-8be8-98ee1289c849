/**
 * Minified by jsDelivr using Terser v5.37.0.
 * Original file: /npm/htmx-ext-response-targets@2.0.3/response-targets.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
!function(){var e;function r(e,r){return e.substring(0,r.length)===r}function t(e){e.detail.isError?htmx.config.responseTargetUnsetsError&&(e.detail.isError=!1):htmx.config.responseTargetSetsError&&(e.detail.isError=!0)}htmx.defineExtension("response-targets",{init:function(r){e=r,void 0===htmx.config.responseTargetUnsetsError&&(htmx.config.responseTargetUnsetsError=!0),void 0===htmx.config.responseTargetSetsError&&(htmx.config.responseTargetSetsError=!1),void 0===htmx.config.responseTargetPrefersExisting&&(htmx.config.responseTargetPrefersExisting=!1),void 0===htmx.config.responseTargetPrefersRetargetHeader&&(htmx.config.responseTargetPrefersRetargetHeader=!0)},onEvent:function(s,n){if("htmx:beforeSwap"===s&&n.detail.xhr&&200!==n.detail.xhr.status){if(n.detail.target){if(htmx.config.responseTargetPrefersExisting)return n.detail.shouldSwap=!0,t(n),!0;if(htmx.config.responseTargetPrefersRetargetHeader&&n.detail.xhr.getAllResponseHeaders().match(/HX-Retarget:/i))return n.detail.shouldSwap=!0,t(n),!0}if(!n.detail.requestConfig)return!0;var i=function(t,s){if(!t||!s)return null;var n=s.toString(),i=[n,n.substring(0,2)+"*",n.substring(0,2)+"x",n.substring(0,1)+"*",n.substring(0,1)+"x",n.substring(0,1)+"**",n.substring(0,1)+"xx","*","x","***","xxx"];(r(n,"4")||r(n,"5"))&&i.push("error");for(var o=0;o<i.length;o++){var a="hx-target-"+i[o],g=e.getClosestAttributeValue(t,a);if(g)return"this"===g?e.findThisElement(t,a):e.querySelectorExt(t,g)}return null}(n.detail.requestConfig.elt,n.detail.xhr.status);return i&&(t(n),n.detail.shouldSwap=!0,n.detail.target=i),!0}}})}();
//# sourceMappingURL=/sm/176f5157e31ba0363e468d451537d9a9d87125fbca92e7105b511baa4bcba673.map