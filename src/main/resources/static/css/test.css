.box {
  display: flex;
  overflow: hidden;
  border-radius: 10px 10px 0 10px;
  position: relative;
}

.box img {
  width: 100%;
  height: auto;
  mask-image: linear-gradient(#000000 0 0), url('/img/mask.svg');
  mask-repeat: no-repeat, no-repeat;
  mask-position: 0 0, 100% 100%;
  mask-composite: exclude;
}

.box .like {
  width: 44px;
  height: 44px;
  border: none;
  text-decoration: none;
  cursor: pointer;
  border-radius: 100%;
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: lightgray;
  color: #000;
}
