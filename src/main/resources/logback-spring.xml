<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!-- ログパターンの定義 -->
    <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"/>
    <property name="ERROR_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n%ex"/>

    <!-- ログディレクトリの定義 -->
    <property name="LOG_DIR" value="logs"/>

    <!-- コンソールアペンダー（開発用） -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- アプリケーション全体ログ -->
    <appender name="APPLICATION_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/application.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_DIR}/application-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- エラー専用ログ -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/error.log</file>
        <encoder>
            <pattern>${ERROR_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_DIR}/error-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>90</maxHistory>
            <totalSizeCap>500MB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- セキュリティログ -->
    <appender name="SECURITY_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/security.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_DIR}/security-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>90</maxHistory>
            <totalSizeCap>500MB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- パフォーマンスログ -->
    <appender name="PERFORMANCE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/performance.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_DIR}/performance-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>300MB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- 非同期アペンダー（パフォーマンス向上） -->
    <appender name="ASYNC_APPLICATION" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="APPLICATION_FILE"/>
        <queueSize>512</queueSize>
        <discardingThreshold>20</discardingThreshold>
    </appender>

    <appender name="ASYNC_ERROR" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="ERROR_FILE"/>
        <queueSize>256</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>true</includeCallerData>
    </appender>

    <!-- favick 特有のロガー設定 -->

    <!-- セキュリティ関連 -->
    <logger name="com.favick.adapter.in.security" level="INFO" additivity="false">
        <appender-ref ref="SECURITY_FILE"/>
        <appender-ref ref="ASYNC_APPLICATION"/>
    </logger>

    <!-- 認証関連 -->
    <logger name="org.springframework.security" level="INFO" additivity="false">
        <appender-ref ref="SECURITY_FILE"/>
        <appender-ref ref="ASYNC_APPLICATION"/>
    </logger>

    <!-- データベース関連 -->
    <logger name="com.favick.adapter.out.persistence" level="DEBUG" additivity="false">
        <appender-ref ref="ASYNC_APPLICATION"/>
    </logger>

    <!-- HikariCP 接続プール -->
    <logger name="com.zaxxer.hikari" level="INFO" additivity="false">
        <appender-ref ref="PERFORMANCE_FILE"/>
        <appender-ref ref="ASYNC_APPLICATION"/>
    </logger>

    <!-- SQL実行ログ（開発時のみ） -->
    <springProfile name="development">
        <logger name="org.hibernate.SQL" level="DEBUG" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ASYNC_APPLICATION"/>
        </logger>
        <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="TRACE" additivity="false">
            <appender-ref ref="CONSOLE"/>
        </logger>
    </springProfile>

    <!-- アプリケーション固有のログ -->
    <logger name="com.favick.application.service" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_APPLICATION"/>
    </logger>

    <logger name="com.favick.domain" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_APPLICATION"/>
    </logger>

    <!-- ファイルアップロード関連 -->
    <logger name="org.springframework.web.multipart" level="INFO" additivity="false">
        <appender-ref ref="PERFORMANCE_FILE"/>
        <appender-ref ref="ASYNC_APPLICATION"/>
    </logger>

    <!-- プロファイル別ルートロガー設定 -->

    <!-- 開発環境 -->
    <springProfile name="development">
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ASYNC_APPLICATION"/>
            <appender-ref ref="ASYNC_ERROR"/>
        </root>
    </springProfile>

    <!-- テスト環境 -->
    <springProfile name="test">
        <root level="WARN">
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>

    <!-- 本番環境 -->
    <springProfile name="production">
        <root level="INFO">
            <appender-ref ref="ASYNC_APPLICATION"/>
            <appender-ref ref="ASYNC_ERROR"/>
        </root>
    </springProfile>

    <!-- デフォルト（プロファイル未指定時） -->
    <springProfile name="!development &amp; !test &amp; !production">
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ASYNC_APPLICATION"/>
            <appender-ref ref="ASYNC_ERROR"/>
        </root>
    </springProfile>

</configuration>
