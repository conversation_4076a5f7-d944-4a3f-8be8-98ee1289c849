# ドメイン
domain.fave=お気に入り
domain.theme=テーマ
domain.user=ユーザー
domain.admin=管理者
domain.rank=ランク
domain.role=権限
domain.token=トークン
# ページ
pages.home.title=ホーム
pages.archives.title=アーカイブ
pages.archives.show.title={0}
pages.pick.title=お気に入り選択
pages.auth.login.title=ログイン
pages.auth.register.title=ユーザー登録
pages.auth.password_reset.title=パスワードリセット
pages.auth.password_reset_verify.title=新しいパスワードの設定
pages.account.dashboard.title=ダッシュボード
pages.console.dashboard.title=ダッシュボード
pages.console.theme.title=テーマ管理
pages.console.theme.create.title=テーマ登録
pages.console.theme.show.title=テーマ詳細
pages.console.theme.edit.title=テーマ編集
pages.error.403.title=403 Forbidden
pages.error.404.title=404 Not Found
pages.error.413.title=413 Payload Too Large
pages.error.500.title=500 Internal Server Error
# メール
mail.user_registration.subject=メール認証
mail.user_registration.body=以下のリンクをクリックして会員登録を完了してください。\nこのリンクは24時間で有効期限が切れます。
mail.password_reset.subject=パスワードリセット
mail.password_reset.body=以下のリンクをクリックしてパスワードをリセットしてください。\nこのリンクは2時間で有効期限が切れます。
# エラー
error.invalid=無効な{0}です
error.not_found=指定された{0}は存在しません
error.not_found.review=対応する審査情報が見つかりません
error.expired={0}の有効期限が切れています
error.cannot_edit_past_or_present.theme=過去または現在のテーマは編集できません
error.file_storage.store_failed=ファイルの保存に失敗しました
error.file_storage.delete_failed=ファイルの削除に失敗しました
error.internal_server_error=システムエラーが発生しました
# バリデーション
validation.required.fave_id=お気に入りIDは必須です
validation.required.user_id=ユーザーIDは必須です
validation.required.admin_id=管理者IDは必須です
validation.required.theme_id=テーマIDは必須です
validation.required.rank_id=ランクIDは必須です
validation.required.role_id=権限IDは必須です
validation.required.language_id=言語IDは必須です
validation.required.review_id=言語IDは必須です
validation.required.name=名前は必須です
validation.required.email=メールアドレスは必須です
validation.required.password=パスワードは必須です
validation.required.token_id=トークンIDは必須です
validation.required.token=トークンは必須です
validation.required.expire_at=有効期限は必須です
validation.required.title=タイトルは必須です
validation.required.content=投稿内容は必須です
validation.required.description=説明文は必須です
validation.required.status=ステータスは必須です
validation.required.type=種別は必須です
validation.required.code=コードは必須です
validation.required.display_name=表示名は必須です
validation.required.start_date=開始日は必須です
validation.required.created_at=作成日は必須です
validation.required.updated_at=更新日は必須です
validation.required.file=ファイルは必須です
validation.invalid.name=この名前は無効です
validation.invalid.status=このステータスは無効です
validation.invalid.type=この種別は無効です
validation.invalid.file_type=このファイル種別は無効です
validation.invalid_format.email=メールアドレスの形式が正しくありません
validation.invalid_format.password=パスワードの形式が正しくありません
validation.too_short.password=パスワードは{0}文字以上で入力してください
validation.too_long.name=名前は{0}文字以内で入力してください
validation.too_long.email=メールアドレスは{0}文字以内で入力してください
validation.too_long.image_url=画像URLは{0}文字以内で入力してください
validation.too_long.password=パスワードは{0}文字以内で入力してください
validation.too_long.title=タイトルは{0}文字以内で入力してください
validation.too_long.content=投稿内容は{0}文字以内で入力してください
validation.too_long.description=説明文は{0}文字以内で入力してください
validation.too_long.display_name=表示名は{0}文字以内で入力してください
validation.too_large.file_size=ファイルサイズが上限を超えています
validation.weak.password=パスワードが弱すぎます
validation.must_be.future_or_present.date=日付は今日以降である必要があります
validation.must_be.unique.email=このメールアドレスは既に使用されています
validation.must_be.unique.date=この日付は既に使用されているため選択できません
validation.cannot_change=変更できません
# 成功
success.create=登録しました
success.update=更新しました
success.delete=削除しました
success.verified_requested=認証メールを送信しました
success.verified=認証しました
success.password_reset_requested=パスワードリセットメールを送信しました
success.password_reset=パスワードをリセットしました
# 失敗
failure.query=データの取得に失敗しました
failure.command=データ操作の実行に失敗しました
