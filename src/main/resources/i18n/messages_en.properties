# ドメイン
domain.fave=Fave
domain.theme=Theme
domain.user=User
domain.admin=Admin
domain.rank=Rank
domain.role=Role
domain.token=Token
# ページ
pages.home.title=Home
pages.archives.title=Archive
pages.archives.show.title={0}
pages.pick.title=Fave Pick
pages.auth.login.title=Login
pages.auth.register.title=Register
pages.auth.password_reset.title=Password Reset
pages.auth.password_reset_verify.title=Set a new password
pages.account.dashboard.title=Dashboard
pages.console.dashboard.title=Dashboard
pages.console.theme.title=Theme Management
pages.console.theme.create.title=Theme Registration
pages.console.theme.show.title=Theme Details
pages.console.theme.edit.title=Theme Edit
pages.error.403.title=403 Forbidden
pages.error.404.title=404 Not Found
pages.error.413.title=413 Payload Too Large
pages.error.500.title=500 Internal Server Error
# メール
mail.user_registration.subject=Email Verification
mail.user_registration.body=Please click the following link to complete your registration.\nThis link will expire in 24 hours.
mail.password_reset.subject=Password Reset
mail.password_reset.body=Please click the following link to reset your password.\nThis link will expire in 2 hours.
# エラー
error.invalid=Invalid {0}
error.not_found=The specified {0} does not exist
error.not_found.review=No corresponding review information found
error.expired=The {0} has expired
error.cannot_edit_past_or_present.theme=You cannot edit past or present themes
error.file_storage.store_failed=Failed to store file
error.file_storage.delete_failed=Failed to delete file
error.internal_server_error=Internal server error
# バリデーション
validation.required.fave_id=Fave ID is required
validation.required.user_id=User ID is required
validation.required.admin_id=Admin ID is required
validation.required.theme_id=Theme ID is required
validation.required.rank_id=Rank ID is required
validation.required.role_id=Role ID is required
validation.required.language_id=Language ID is required
validation.required.review_id=Review ID is required
validation.required.name=Name is required
validation.required.email=Email is required
validation.required.password=Password is required
validation.required.token_id=Token ID is required
validation.required.token=Token is required
validation.required.expire_at=Expire date is required
validation.required.title=Title is required
validation.required.content=content is required
validation.required.description=Description is required
validation.required.status=Status is required
validation.required.type=Type is required
validation.required.code=Code is required
validation.required.display_name=Display name is required
validation.required.start_date=Start date is required
validation.required.created_at=Created date is required
validation.required.updated_at=Updated date is required
validation.required.file=File is required
validation.invalid.name=This name is invalid
validation.invalid.status=This status is invalid
validation.invalid.type=This type is invalid
validation.invalid.file_type=This file type is invalid
validation.invalid_format.email=Email format is invalid
validation.invalid_format.password=Password format is invalid
validation.too_short.password=Password must be {0} characters or less
validation.too_long.name=Name must be {0} characters or less
validation.too_long.email=Email must be {0} characters or less
validation.too_long.password=Password must be {0} characters or less
validation.too_long.image_url=Image URL must be {0} characters or less
validation.too_long.title=Title must be {0} characters or less
validation.too_long.content=Content must be {0} characters or less
validation.too_long.description=Description must be {0} characters or less
validation.too_long.display_name=Display name must be {0} characters or less
validation.too_large.file_size=File size is too large
validation.weak.password=Password is too weak
validation.must_be.future_or_present.date=The date must be today or in the future.
validation.must_be.unique.email=This email is already in use.
validation.must_be.unique.date=This date is already in use and cannot be selected
validation.cannot_change=cannot be changed.
# 成功
success.create=Registered.
success.update=Updated.
success.delete=Deleted.
success.verified_requested=Verification email has been sent
success.verified=Verified.
success.password_reset_requested=Password reset email has been sent
success.password_reset=Password has been reset
# 失敗
failure.query=Failed to retrieve data
failure.command=Failed to execute data operation
