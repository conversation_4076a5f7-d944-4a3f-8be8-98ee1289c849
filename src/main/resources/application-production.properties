spring.datasource.url=${DB_URL}
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}
spring.mail.sender=${MAIL_SENDER}
spring.mail.host=${MAILGUN_SMTP_SERVER}
spring.mail.port=${MAILGUN_SMTP_PORT}
spring.mail.username=${MAILGUN_SMTP_LOGIN}
spring.mail.password=${MAILGUN_SMTP_PASSWORD}
server.error.include-exception=false
logging.level.com.favick=INFO
logging.level.org.springframework.boot.actuator=WARN
management.endpoints.web.exposure.include=health
management.endpoint.health.show-details=never
management.endpoint.health.show-components=never
management.server.port=8081
management.server.address=127.0.0.1
