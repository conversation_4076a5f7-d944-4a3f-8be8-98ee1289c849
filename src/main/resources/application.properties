spring.application.name=favick
spring.sql.init.encoding=utf-8
spring.sql.init.mode=always
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=2
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.max-lifetime=1200000
spring.datasource.hikari.leak-detection-threshold=60000
spring.datasource.hikari.pool-name=FavickHikariPool
spring.data.web.pageable.one-indexed-parameters=true
spring.jpa.hibernate.ddl-auto=none
spring.jpa.open-in-view=false
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
spring.servlet.multipart.resolve-lazily=true
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.messages.basename=i18n/messages
spring.messages.encoding=UTF-8
spring.messages.fallback-to-system-locale=false
server.error.whitelabel.enabled=false
server.error.include-message=always
server.error.include-binding-errors=always
server.error.include-stacktrace=never
app.file.upload-dir=src/main/resources/static/storage
management.endpoint.health.enabled=true
management.endpoint.info.enabled=true
management.endpoint.prometheus.enabled=true
management.endpoint.metrics.enabled=false
management.metrics.export.prometheus.enabled=true
management.metrics.tags.application=favick
management.endpoints.web.base-path=/admin/monitor
management.info.build.enabled=true
