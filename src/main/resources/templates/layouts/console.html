<!DOCTYPE html>
<html
  lang="ja"
  xmlns:layout="https://www.ultraq.net.nz/thymeleaf/layout"
  xmlns:sec="https://www.thymeleaf.org/extras/spring-security"
  xmlns:th="https://www.thymeleaf.org"
>
<head>
  <meta charset="UTF-8">
  <meta content="width=device-width, initial-scale=1" name="viewport">
  <title layout:title-pattern="$CONTENT_TITLE - favick">favick</title>
  <script defer th:src="@{/js/htmx.min.js}"></script>
  <script defer th:src="@{/js/response-targets.min.js}"></script>
  <script defer th:src="@{/js/rive.js}"></script>
</head>
<body hx-ext="response-targets">
<!-- header -->
<header class="header" id="header">
  <div>
    <!-- 言語切り替え -->
    <a hx-get="?lang=ja" hx-target="body">日本語</a>
    <a hx-get="?lang=en" hx-target="body">English</a>

    <!-- 管理者情報とナビゲーション -->
    <div sec:authorize="hasRole('ADMIN')">
      <span>管理者: <span sec:authentication="name"></span></span>

      <!-- 管理者用ナビゲーション -->
      <nav>
        <a th:href="@{/console}">ダッシュボード</a>
        <a th:href="@{/console/themes}">テーマ管理</a>
        <a th:href="@{/console/reviews}">審査管理</a>
        <a th:href="@{/console/users}">ユーザー管理</a>
        <a th:href="@{/console/notifications}">通知管理</a>
        <a th:href="@{/console/contacts}">お問い合わせ</a>
        <a th:href="@{/console/settings}">設定</a>
      </nav>

      <!-- ログアウトフォーム -->
      <form method="post" style="display: inline;" th:action="@{/auth/admin/logout}">
        <input th:name="${_csrf.parameterName}" th:value="${_csrf.token}" type="hidden"/>
        <button onclick="return confirm('ログアウトしますか？')" type="submit">
          ログアウト
        </button>
      </form>
    </div>
  </div>
</header>

<!-- main -->
<main class="main" id="main">
  <!-- メッセージ表示エリア -->
  <div class="message" th:if="${successMessage}">
    <p class="alert success-box" th:text="${successMessage}"></p>
  </div>
  <div class="message" th:if="${param.login}">
    <p class="alert success-box">ログインしました。</p>
  </div>
  <div class="message" th:if="${param.logout}">
    <p class="alert success-box">ログアウトしました。</p>
  </div>
  <div class="message" th:if="${errorMessage}">
    <p class="alert error-box" th:text="${errorMessage}"></p>
  </div>
  <div class="message" th:if="${param.error}">
    <p class="alert error-box">メールアドレスまたはパスワードが正しくありません。</p>
  </div>

  <th:block layout:fragment="content"></th:block>
</main>

<!-- footer -->
<footer class="footer" id="footer">
  フッター
</footer>
</body>
</html>
