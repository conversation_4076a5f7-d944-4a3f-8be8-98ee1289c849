<!DOCTYPE html>
<html
  lang="ja"
  xmlns:layout="https://www.ultraq.net.nz/thymeleaf/layout"
  xmlns:sec="https://www.thymeleaf.org/extras/spring-security"
  xmlns:th="https://www.thymeleaf.org"
>
<head>
  <meta charset="UTF-8">
  <meta content="width=device-width, initial-scale=1" name="viewport">
  <title layout:title-pattern="$CONTENT_TITLE - favick">favick</title>
  <script defer th:src="@{/js/htmx.min.js}"></script>
  <script defer th:src="@{/js/response-targets.min.js}"></script>
  <script defer th:src="@{/js/rive.js}"></script>
</head>
<body hx-ext="response-targets">
<!-- header -->
<header class="header" id="header">
  <div>
    <!-- 言語切り替え -->
    <a hx-get="?lang=ja" hx-target="body">日本語</a>
    <a hx-get="?lang=en" hx-target="body">English</a>

    <!-- ユーザー情報とナビゲーション -->
    <div sec:authorize="isAuthenticated()">
      <span>こんにちは、<span sec:authentication="name"></span>さん</span>

      <!-- アカウント関連ナビゲーション -->
      <nav>
        <a th:href="@{/}">ホーム</a>
        <a th:href="@{/account}">ダッシュボード</a>
        <a th:href="@{/account/faves}">過去の投稿</a>
        <a th:href="@{/account/badges}">バッジ</a>
        <a th:href="@{/account/settings}">設定</a>
        <a th:href="@{/pick}">投稿</a>
      </nav>

      <!-- ログアウトフォーム -->
      <form method="post" style="display: inline;" th:action="@{/auth/logout}">
        <input th:name="${_csrf.parameterName}" th:value="${_csrf.token}" type="hidden"/>
        <button onclick="return confirm('ログアウトしますか？')" type="submit">
          ログアウト
        </button>
      </form>
    </div>
  </div>
</header>

<!-- main -->
<main class="main" id="main">
  <!-- メッセージ表示エリア -->
  <div class="message" th:if="${successMessage}">
    <p class="alert success-box" th:text="${successMessage}"></p>
  </div>
  <div class="message" th:if="${param.login}">
    <p class="alert success-box">ログインしました。</p>
  </div>
  <div class="message" th:if="${param.logout}">
    <p class="alert success-box">ログアウトしました。</p>
  </div>
  <div class="message" th:if="${errorMessage}">
    <p class="alert error-box" th:text="${errorMessage}"></p>
  </div>
  <div class="message" th:if="${param.error}">
    <p class="alert error-box">エラーが発生しました。</p>
  </div>

  <th:block layout:fragment="content"></th:block>
</main>

<!-- footer -->
<footer class="footer" id="footer">
  フッター
</footer>
</body>
</html>
