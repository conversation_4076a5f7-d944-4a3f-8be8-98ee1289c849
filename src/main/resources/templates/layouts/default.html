<!DOCTYPE html>
<html
  lang="ja"
  xmlns:layout="https://www.ultraq.net.nz/thymeleaf/layout"
  xmlns:sec="https://www.thymeleaf.org/extras/spring-security"
  xmlns:th="https://www.thymeleaf.org"
>
<head>
  <meta charset="UTF-8">
  <meta content="width=device-width, initial-scale=1" name="viewport">
  <title layout:title-pattern="$CONTENT_TITLE - favick">favick</title>
  <link rel="stylesheet" th:href="@{/css/test.css}">
  <script defer th:src="@{/js/htmx.min.js}"></script>
  <script defer th:src="@{/js/response-targets.min.js}"></script>
  <script defer th:src="@{/js/rive.js}"></script>
</head>
<body hx-ext="response-targets">
<!-- header -->
<header class="header" id="header">
  <div>
    <!-- 言語切り替え -->
    <a hx-get="?lang=ja" hx-target="body">日本語</a>
    <a hx-get="?lang=en" hx-target="body">English</a>

    <!-- 認証状態による表示切り替え -->
    <div sec:authorize="!isAuthenticated()">
      <!-- 未ログイン時 -->
      <a th:href="@{/auth/login}">ログイン</a>
      <a th:href="@{/auth/register}">会員登録</a>
    </div>

    <div sec:authorize="isAuthenticated()">
      <!-- ログイン時 -->
      <span>こんにちは、<span sec:authentication="name"></span>さん</span>
      <a th:href="@{/account}">マイページ</a>

      <!-- ログアウトフォーム -->
      <form method="post" style="display: inline;" th:action="@{/auth/logout}">
        <input th:name="${_csrf.parameterName}" th:value="${_csrf.token}" type="hidden"/>
        <button onclick="return confirm('ログアウトしますか？')" type="submit">
          ログアウト
        </button>
      </form>
    </div>
  </div>
</header>

<!-- main -->
<main class="main" id="main">
  <!-- メッセージ表示エリア -->
  <div class="message" th:if="${successMessage}">
    <p class="alert success-box" th:text="${successMessage}"></p>
  </div>
  <div class="message" th:if="${param.login}">
    <p class="alert success-box">ログインしました。</p>
  </div>
  <div class="message" th:if="${param.logout}">
    <p class="alert success-box">ログアウトしました。</p>
  </div>
  <div class="message" th:if="${errorMessage}">
    <p class="alert error-box" th:text="${errorMessage}"></p>
  </div>
  <div class="message" th:if="${param.error}">
    <p class="alert error-box">ログインに失敗しました。</p>
  </div>

  <th:block layout:fragment="content"></th:block>
</main>

<!-- footer -->
<footer class="footer" id="footer">
  フッター
</footer>
</body>
</html>
