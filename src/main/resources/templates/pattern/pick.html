<div th:fragment="content"
     xmlns:th="https://www.thymeleaf.org">
  <div id="pick-content">
    <section th:if="${!isUnregistered}">
      <img alt="" style="max-width: 100%;" th:src="@{/storage/{filename}(filename=${faveContent})}">
      <dl>
        <dt>ステータス</dt>
        <dd th:text="${faveStatus}">ステータス</dd>
      </dl>
    </section>
    <form
      enctype="multipart/form-data"
      hx-post="/pick"
      hx-target="#pick-content"
      th:object="${form}"
    >
      <input th:name="${_csrf.parameterName}" th:value="${_csrf.token}" type="hidden"/>

      <div>
        <label for="file">ファイルを選択</label>
        <input accept="image/*,video/*,audio/*" id="file" th:field="*{file}" type="file">
        <div style="color: red;" th:errors="*{file}" th:if="${#fields.hasErrors('file')}"></div>
      </div>

      <div style="margin-top: 10px;">
        <button type="submit">投稿する</button>
      </div>
    </form>
  </div>
</div>


