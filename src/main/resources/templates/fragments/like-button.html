<div
  hx-post="/like"
  hx-swap="outerHTML"
  hx-target="this"
  sec:authorize="isAuthenticated()"
  th:attr="hx-vals='{&quot;faveId&quot;: &quot;' + ${fave.id} + '&quot;}',hx-headers='{&quot;X-CSRF-TOKEN&quot;: &quot;' + ${_csrf.token} + '&quot;}',hx-target-400='#error-' + ${fave.id}"
  th:fragment="like-button(fave)"
  th:id="'like-' + ${fave.id}"
  xmlns:sec="https://www.thymeleaf.org/extras/spring-security"
  xmlns:th="https://www.thymeleaf.org"
>
  <button class="like" th:text="${fave.isLiked ? '❤︎' : '♡'}">
    ♡
  </button>
</div>
