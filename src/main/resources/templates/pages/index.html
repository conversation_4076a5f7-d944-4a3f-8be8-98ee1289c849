<!DOCTYPE html>
<html
  lang="ja"
  layout:decorate="~{layouts/default}"
  xmlns:layout="https://www.ultraq.net.nz/thymeleaf/layout"
  xmlns:sec="https://www.thymeleaf.org/extras/spring-security"
  xmlns:th="https://www.thymeleaf.org"
>
<head>
  <title th:text="#{pages.home.title}">ホーム</title>
</head>
<body>

<th:block layout:fragment="content">
  <h1 th:text="#{pages.home.title}">ホーム</h1>

  <!-- テーマ情報セクション -->
  <div th:if="${theme != null}">
    <h2 th:text="${theme.title}">テーマタイトル</h2>
    <p th:text="${theme.description}">テーマの説明がここに表示されます</p>
  </div>

  <!-- お気に入りがない場合 -->
  <div th:if="${#lists.isEmpty(faves)}">
    <p>まだお気に入りが投稿されていません。</p>
    <p sec:authorize="isAuthenticated()">
      <a th:href="@{/pick}">お気に入りを投稿する</a>
    </p>
    <p sec:authorize="!isAuthenticated()">
      <a th:href="@{/auth/login}">ログインして投稿する</a>
    </p>
  </div>

  <!-- お気に入り一覧 -->
  <div th:if="${!#lists.isEmpty(faves)}">
    <div class="box" th:each="fave : ${faves}">
      <img alt="" th:src="@{/storage/{filepath}(filepath=${fave.content})}">

      <!-- エラーメッセージ表示エリア -->
      <div th:id="'error-' + ${fave.id}"></div>

      <!-- ログイン済みユーザー: いいねボタンフラグメント -->
      <div sec:authorize="isAuthenticated()">
        <div th:replace="~{fragments/like-button :: like-button(fave=${fave})}"></div>
      </div>

      <!-- 未ログインユーザー: ログインページへのリンク -->
      <a class="like" sec:authorize="!isAuthenticated()" th:href="@{/auth/login}">
        ♡
      </a>
    </div>
  </div>
</th:block>
</body>
</html>
