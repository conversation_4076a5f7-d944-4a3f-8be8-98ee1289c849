<!DOCTYPE html>
<html
  lang="ja"
  layout:decorate="~{layouts/default}"
  xmlns:layout="https://www.ultraq.net.nz/thymeleaf/layout"
  xmlns:th="https://www.thymeleaf.org"
>
<head>
  <title th:text="#{pages.auth.password_reset_verify.title}">新しいパスワードの設定</title>
</head>
<body>
<th:block layout:fragment="content">
  <h1 th:text="#{pages.auth.password_reset_verify.title}">新しいパスワードの設定</h1>

  <form method="post" th:action="@{/auth/password-reset/verify}" th:object="${form}">
    <input name="token" th:value="${token}" type="hidden"/>

    <div class="mb-4">
      <label class="block text-sm font-medium text-gray-700 mb-2" for="password">
        新しいパスワード
      </label>
      <input
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        id="password"
        placeholder="新しいパスワードを入力してください"
        required
        th:field="*{password}"
        type="password"
      />
      <div class="mt-1 text-sm text-red-600" th:if="${#fields.hasErrors('password')}">
        <span th:errors="*{password}"></span>
      </div>
    </div>

    <button
      class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200"
      type="submit"
    >
      パスワードを変更
    </button>
  </form>

  <div class="mt-6 text-center">
    <p class="text-sm text-gray-600">
      パスワードを変更すると、ログイン画面に移動します。
    </p>
  </div>
</th:block>
</body>
</html>
