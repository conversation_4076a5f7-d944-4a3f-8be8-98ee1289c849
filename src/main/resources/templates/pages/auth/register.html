<!DOCTYPE html>
<html
  lang="ja"
  layout:decorate="~{layouts/default}"
  xmlns:layout="https://www.ultraq.net.nz/thymeleaf/layout"
  xmlns:th="https://www.thymeleaf.org"
>
<head>
  <title th:text="#{pages.auth.register.title}">ユーザー登録</title>
</head>
<body>
<th:block layout:fragment="content">
  <h1 th:text="#{pages.auth.register.title}">ユーザー登録</h1>

  <form action="#" method="post" th:action="@{/auth/register}" th:object="${form}">
    <div>
      <label for="name">名前</label>
      <input id="name" th:field="*{name}" type="text"/>
      <div th:errors="*{name}" th:if="${#fields.hasErrors('name')}"></div>
    </div>

    <div>
      <label for="email">メールアドレス</label>
      <input id="email" th:field="*{email}" type="email"/>
      <div th:errors="*{email}" th:if="${#fields.hasErrors('email')}"></div>
    </div>

    <div>
      <label for="password">パスワード</label>
      <input id="password" th:field="*{password}" type="password"/>
      <div th:errors="*{password}" th:if="${#fields.hasErrors('password')}"></div>
    </div>

    <div>
      <label for="imageUrl">画像URL</label>
      <input id="imageUrl" th:field="*{imageUrl}" type="url"/>
      <div th:errors="*{imageUrl}" th:if="${#fields.hasErrors('imageUrl')}"></div>
    </div>

    <button type="submit">登録</button>
  </form>

  <a th:href="@{/auth/login}">ログインページへ</a>
  <a th:href="@{/auth/password-reset}">パスワードを忘れた方</a>
</th:block>
</body>
</html>
