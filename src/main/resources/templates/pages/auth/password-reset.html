<!DOCTYPE html>
<html
  lang="ja"
  layout:decorate="~{layouts/default}"
  xmlns:layout="https://www.ultraq.net.nz/thymeleaf/layout"
  xmlns:th="https://www.thymeleaf.org"
>
<head>
  <title th:text="#{pages.auth.password_reset.title}">パスワードリセット</title>
</head>
<body>
<th:block layout:fragment="content">
  <h1 th:text="#{pages.auth.password_reset.title}">パスワードリセット</h1>

  <form method="post" th:action="@{/auth/password-reset}" th:object="${form}">
    <div class="mb-4">
      <label class="block text-sm font-medium text-gray-700 mb-2" for="email">
        メールアドレス
      </label>
      <input
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        id="email"
        placeholder="メールアドレスを入力してください"
        required
        th:field="*{email}"
        type="email"
      />
      <div class="mt-1 text-sm text-red-600" th:if="${#fields.hasErrors('email')}">
        <span th:errors="*{email}"></span>
      </div>
    </div>

    <button
      class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200"
      type="submit"
    >
      リセットリンクを送信
    </button>
  </form>

  <div class="mt-6 text-center">
    <p class="text-sm text-gray-600">
      登録済みのメールアドレスにパスワードリセット用のリンクをお送りします。
    </p>
  </div>

  <div class="mt-4 text-center">
    <a class="text-blue-600 hover:text-blue-800 text-sm" th:href="@{/auth/login}">
      ログイン画面に戻る
    </a>
  </div>
</th:block>
</body>
</html>
