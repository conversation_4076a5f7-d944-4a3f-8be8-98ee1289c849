<!DOCTYPE html>
<html
  lang="ja"
  layout:decorate="~{layouts/default}"
  xmlns:layout="https://www.ultraq.net.nz/thymeleaf/layout"
  xmlns:sec="https://www.thymeleaf.org/extras/spring-security"
  xmlns:th="https://www.thymeleaf.org"
>
<head>
  <title th:text="#{pages.pick.title}">お気に入り選択</title>
</head>
<body>
<th:block layout:fragment="content">
  <h1 th:text="#{pages.pick.title}">お気に入り選択</h1>

  <!-- 現在のテーマ情報表示 -->
  <section>
    <h2 th:text="${theme.title}">テーマ</h2>
    <p th:text="${theme.description}">テーマの説明</p>
    <p th:text="${theme.type}">テーマの種類</p>
  </section>

  <section th:if="${fave != null}">
    <img alt="" style="max-width: 100%;" th:src="@{/storage/{filename}(filename=${fave.content})}">
    <dl>
      <dt>ステータス</dt>
      <dd th:text="${fave.status}">ステータス</dd>
    </dl>
  </section>
  <form
    enctype="multipart/form-data"
    method="post"
    th:action="@{/pick}"
    th:if="${fave == null or fave.status?.name() == 'REJECTED'}"
    th:object="${form}"
  >
    <div>
      <label for="file">ファイルを選択</label>
      <input accept="image/*,video/*,audio/*" id="file" th:field="*{file}" type="file">
      <div style="color: red;" th:errors="*{file}" th:if="${#fields.hasErrors('file')}"></div>
    </div>

    <div style="margin-top: 10px;">
      <button type="submit">投稿する</button>
    </div>
  </form>

  <!-- ROLE_ADMIN を持つユーザーだけが見える -->
  <div sec:authorize="hasRole('ADMIN')">
    管理者メニュー
  </div>

  <!-- RANK_SEED のユーザーだけが見える -->
  <div sec:authorize="@rankAuth.hasRank(authentication, 'SEED')">
    SEEDランクのユーザー向けコンテンツ
  </div>

  <!-- RANK_SPROUT のユーザーだけが見える -->
  <div sec:authorize="@rankAuth.hasRank(authentication, 'SPROUT')">
    SPROUTランク専用メニュー
  </div>

  <!-- RANK_BUD のユーザーだけが見える -->
  <div sec:authorize="@rankAuth.hasRank(authentication, 'BUD')">
    BUDランク専用メニュー
  </div>

  <!-- RANK_BLOOM のユーザーだけが見える -->
  <div sec:authorize="@rankAuth.hasRank(authentication, 'BLOOM')">
    BLOOMランク専用メニュー
  </div>

  <!-- RANK_GARDEN のユーザーだけが見える -->
  <div sec:authorize="@rankAuth.hasRank(authentication, 'GARDEN')">
    GARDENランク専用メニュー
  </div>
</th:block>
</body>
</html>
