<!DOCTYPE html>
<html
  lang="ja"
  layout:decorate="~{layouts/console}"
  xmlns:layout="https://www.ultraq.net.nz/thymeleaf/layout"
  xmlns:th="https://www.thymeleaf.org"
>
<head>
  <title th:text="#{pages.console.theme.title}">テーマ管理</title>
</head>
<body>
<th:block layout:fragment="content">
  <h1 th:text="#{pages.console.theme.title}">テーマ管理</h1>

  <a th:href="@{/console/themes/create}">新規登録</a>
  <table>
    <thead>
    <tr>
      <th>タイトル</th>
      <th>説明</th>
      <th>種別</th>
      <th>開始日</th>
      <th>操作</th>
    </tr>
    </thead>
    <tbody>
    <tr th:each="theme : ${themes}">
      <td>
        <a th:href="@{/console/themes/{id}(id=${theme.id})}" th:text="${theme.title}">タイトル</a>
      </td>
      <td th:text="${theme.description}">説明</td>
      <td th:text="${theme.type}">種別</td>
      <td th:text="${theme.startDate}">開始日</td>
      <td>
        <a th:href="@{/console/themes/{id}/edit(id=${theme.id})}">編集</a>
        <form action="#" method="post" th:action="@{/console/themes/{id}/delete(id=${theme.id})}">
          <button type="submit">削除</button>
        </form>
      </td>
    </tr>
    </tbody>
  </table>
</th:block>
</body>
</html>
