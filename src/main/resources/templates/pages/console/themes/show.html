<!DOCTYPE html>
<html
  lang="ja"
  layout:decorate="~{layouts/console}"
  xmlns:layout="https://www.ultraq.net.nz/thymeleaf/layout"
  xmlns:th="https://www.thymeleaf.org"
>
<head>
  <title th:text="#{pages.console.theme.show.title}">テーマ詳細</title>
</head>
<body>
<th:block layout:fragment="content">
  <h1 th:text="#{pages.console.theme.show.title}">テーマ詳細</h1>

  <table>
    <tr>
      <th>ID</th>
      <td th:text="${theme.id}">UUID</td>
    </tr>
    <tr>
      <th>タイトル</th>
      <td th:text="${theme.title}">タイトル</td>
    </tr>
    <tr>
      <th>説明</th>
      <td th:text="${theme.description}">説明</td>
    </tr>
    <tr>
      <th>タイプ</th>
      <td th:text="${theme.type}">タイプ</td>
    </tr>
    <tr>
      <th>開始日</th>
      <td th:text="${theme.startDate}">2025-04-01</td>
    </tr>
    <tr>
      <th>作成日時</th>
      <td th:text="${theme.createdAt}">2025-04-01T10:00:00</td>
    </tr>
    <tr>
      <th>更新日時</th>
      <td th:text="${theme.updatedAt}">2025-04-02T09:30:00</td>
    </tr>
  </table>

  <a th:href="@{/console/themes}">一覧へ戻る</a>
</th:block>
</body>
</html>
