<!DOCTYPE html>
<html
  lang="ja"
  layout:decorate="~{layouts/console}"
  xmlns:layout="https://www.ultraq.net.nz/thymeleaf/layout"
  xmlns:th="https://www.thymeleaf.org"
>
<head>
  <title th:text="#{pages.console.theme.edit.title}">テーマ登録</title>
</head>
<body>
<th:block layout:fragment="content">
  <h1 th:text="#{pages.console.theme.edit.title}">テーマ登録</h1>
  <form action="#" method="post" th:action="@{/console/themes/{id}/edit(id=${theme.id})}" th:object="${form}">
    <input name="id" th:value="${theme.id}" type="hidden"/>

    <ul th:if="${#fields.hasGlobalErrors()}">
      <li th:each="err : ${#fields.globalErrors()}">
        <span th:text="${err}"></span>
      </li>
    </ul>

    <div>
      <label for="titleJa">タイトル（日本語）</label>
      <input id="titleJa" th:field="*{titleJa}" type="text"/>
      <div th:errors="*{titleJa}" th:if="${#fields.hasErrors('titleJa')}"></div>
    </div>

    <div>
      <label for="descriptionJa">説明（日本語）</label>
      <textarea id="descriptionJa" th:field="*{descriptionJa}"></textarea>
      <div th:errors="*{descriptionJa}" th:if="${#fields.hasErrors('descriptionJa')}"></div>
    </div>

    <div>
      <label for="titleEn">タイトル（英語）</label>
      <input id="titleEn" th:field="*{titleEn}" type="text"/>
      <div th:errors="*{titleEn}" th:if="${#fields.hasErrors('titleEn')}"></div>
    </div>

    <div>
      <label for="descriptionEn">説明（英語）</label>
      <textarea id="descriptionEn" th:field="*{descriptionEn}"></textarea>
      <div th:errors="*{descriptionEn}" th:if="${#fields.hasErrors('descriptionEn')}"></div>
    </div>

    <div>
      <label for="type">種別</label>
      <select id="type" th:field="*{type}">
        <option value="PHOTO">写真</option>
        <option value="MOVIE">動画</option>
        <option value="AUDIO">音声</option>
      </select>
      <div th:errors="*{type}" th:if="${#fields.hasErrors('type')}"></div>
    </div>

    <div>
      <label for="startDate">開始日</label>
      <input id="startDate" th:field="*{startDate}" type="date"/>
      <div th:errors="*{startDate}" th:if="${#fields.hasErrors('startDate')}"></div>
    </div>

    <button type="submit">更新</button>
  </form>

  <a th:href="@{/console/themes}">一覧へ戻る</a>
</th:block>
</body>
</html>
