-- ========================================
-- 初期スキーマ + パフォーマンス改善
-- ========================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- 基本テーブル定義
-- ========================================

-- language テーブル
CREATE TYPE language_code AS ENUM ('JA', 'EN');

CREATE OR REPLACE FUNCTION cast_to_language_code(text) RETURNS language_code AS $$
BEGIN
    RETURN $1::language_code;
EXCEPTION WHEN others THEN
    RAISE EXCEPTION 'Invalid language_code: %', $1;
END;
$$ LANGUAGE plpgsql;

CREATE CAST (varchar AS language_code) WITH FUNCTION cast_to_language_code(text) AS IMPLICIT;

CREATE TABLE languages (
    id UUID PRIMARY KEY,
    code language_code NOT NULL UNIQUE,
    display_name VARCHAR(50) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TRIGGER set_updated_at_languages
BEFORE UPDATE ON languages
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- roles テーブル
CREATE TYPE role_name AS ENUM ('ROLE_ADMIN');

CREATE OR REPLACE FUNCTION cast_to_role_name(text) RETURNS role_name AS $$
BEGIN
    RETURN $1::role_name;
EXCEPTION WHEN others THEN
    RAISE EXCEPTION 'Invalid role_name: %', $1;
END;
$$ LANGUAGE plpgsql;

CREATE CAST (varchar AS role_name) WITH FUNCTION cast_to_role_name(text) AS IMPLICIT;

CREATE TABLE roles (
    id UUID PRIMARY KEY,
    name role_name NOT NULL UNIQUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TRIGGER set_updated_at_roles
BEFORE UPDATE ON roles
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- ranks テーブル
CREATE TYPE rank_name AS ENUM ('RANK_SEED', 'RANK_SPROUT', 'RANK_BUD', 'RANK_BLOOM', 'RANK_GARDEN');

CREATE OR REPLACE FUNCTION cast_to_rank_name(text) RETURNS rank_name AS $$
BEGIN
    RETURN $1::rank_name;
EXCEPTION WHEN others THEN
    RAISE EXCEPTION 'Invalid rank_name: %', $1;
END;
$$ LANGUAGE plpgsql;

CREATE CAST (varchar AS rank_name) WITH FUNCTION cast_to_rank_name(text) AS IMPLICIT;

CREATE TABLE ranks (
    id UUID PRIMARY KEY,
    name rank_name NOT NULL UNIQUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TRIGGER set_updated_at_ranks
BEFORE UPDATE ON ranks
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- admins テーブル
CREATE TABLE admins (
    id UUID PRIMARY KEY,
    role_id UUID NOT NULL REFERENCES roles(id),
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TRIGGER set_updated_at_admins
BEFORE UPDATE ON admins
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE INDEX idx_admins_email ON admins (email);

-- users テーブル
CREATE TABLE users (
    id UUID PRIMARY KEY,
    rank_id UUID NOT NULL REFERENCES ranks(id),
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    enabled BOOLEAN NOT NULL DEFAULT false,
    image_url VARCHAR(255),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TRIGGER set_updated_at_users
BEFORE UPDATE ON users
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE INDEX idx_users_email_enabled ON users (email) WHERE enabled = true;
CREATE INDEX idx_users_rank_id ON users (rank_id);

-- verification_tokens テーブル
CREATE TABLE verification_tokens (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token VARCHAR(255) NOT NULL UNIQUE,
    expire_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_verification_tokens_token ON verification_tokens (token);
CREATE INDEX idx_verification_tokens_expire ON verification_tokens (expire_at);
CREATE INDEX idx_verification_tokens_user ON verification_tokens (user_id);

-- password_reset_tokens テーブル
CREATE TABLE password_reset_tokens (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token VARCHAR(255) NOT NULL UNIQUE,
    expire_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_password_reset_tokens_token ON password_reset_tokens (token);
CREATE INDEX idx_password_reset_tokens_expire ON password_reset_tokens (expire_at);
CREATE INDEX idx_password_reset_tokens_user ON password_reset_tokens (user_id);

-- themes テーブル
CREATE TYPE theme_type AS ENUM ('PHOTO', 'MOVIE', 'AUDIO');

CREATE OR REPLACE FUNCTION cast_to_theme_type(text) RETURNS theme_type AS $$
BEGIN
    RETURN $1::theme_type;
EXCEPTION WHEN others THEN
    RAISE EXCEPTION 'Invalid theme_type: %', $1;
END;
$$ LANGUAGE plpgsql;

CREATE CAST (varchar AS theme_type) WITH FUNCTION cast_to_theme_type(text) AS IMPLICIT;

CREATE TABLE themes (
    id UUID PRIMARY KEY,
    type theme_type NOT NULL,
    start_date DATE NOT NULL UNIQUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TRIGGER set_updated_at_themes
BEFORE UPDATE ON themes
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE INDEX idx_themes_start_date_desc ON themes (start_date DESC);
CREATE INDEX idx_themes_type_start_date ON themes (type, start_date);

-- theme_localizations テーブル
CREATE TABLE theme_localizations (
    id UUID PRIMARY KEY,
    theme_id UUID NOT NULL REFERENCES themes(id) ON DELETE CASCADE,
    language_code language_code NOT NULL,
    title VARCHAR(50) NOT NULL,
    description VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(theme_id, language_code)
);

CREATE TRIGGER set_updated_at_theme_localizations
BEFORE UPDATE ON theme_localizations
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE INDEX idx_theme_localizations_theme_lang ON theme_localizations (theme_id, language_code);

-- faves テーブル
CREATE TABLE faves (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    theme_id UUID NOT NULL REFERENCES themes(id) ON DELETE CASCADE,
    content VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (user_id, theme_id)
);

CREATE TRIGGER set_updated_at_faves
BEFORE UPDATE ON faves
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE INDEX idx_faves_user_theme ON faves (user_id, theme_id);
CREATE INDEX idx_faves_theme_created ON faves (theme_id, created_at DESC);
CREATE INDEX idx_faves_created ON faves (created_at DESC);

-- likes テーブル
CREATE TABLE likes (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    fave_id UUID NOT NULL REFERENCES faves(id) ON DELETE CASCADE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (user_id, fave_id)
);

CREATE INDEX idx_likes_fave_id ON likes (fave_id);
CREATE INDEX idx_likes_user_id ON likes (user_id);
CREATE INDEX idx_likes_created_at_desc ON likes (created_at DESC);
CREATE INDEX idx_likes_fave_created ON likes (fave_id, created_at DESC);

-- reviews テーブル
CREATE TYPE review_status AS ENUM ('PENDING', 'APPROVED', 'REJECTED');

CREATE OR REPLACE FUNCTION cast_to_review_status(text) RETURNS review_status AS $$
BEGIN
    RETURN $1::review_status;
EXCEPTION WHEN others THEN
    RAISE EXCEPTION 'Invalid review_status: %', $1;
END;
$$ LANGUAGE plpgsql;

CREATE CAST (varchar AS review_status) WITH FUNCTION cast_to_review_status(text) AS IMPLICIT;

CREATE TABLE reviews (
    id UUID PRIMARY KEY,
    fave_id UUID NOT NULL REFERENCES faves(id) ON DELETE CASCADE UNIQUE,
    admin_id UUID REFERENCES admins(id) ON DELETE SET NULL,
    status review_status NOT NULL DEFAULT 'PENDING',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TRIGGER set_updated_at_reviews
BEFORE UPDATE ON reviews
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE INDEX idx_reviews_status_created ON reviews (status, created_at DESC);
CREATE INDEX idx_reviews_fave_status ON reviews (fave_id, status);
CREATE INDEX idx_reviews_admin_id ON reviews (admin_id) WHERE admin_id IS NOT NULL;

-- ========================================
-- テストデータ挿入（既存と同じ）
-- ========================================

-- languages テーブル
INSERT INTO languages (id, code, display_name, created_at, updated_at) VALUES
('00000000-0000-0000-0000-000000000001', 'JA', '日本語', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('00000000-0000-0000-0000-000000000002', 'EN', 'English', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- roles テーブル
INSERT INTO roles (id, name, created_at, updated_at) VALUES
('11111111-1111-1111-1111-111111111111', 'ROLE_ADMIN', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ranks テーブル
INSERT INTO ranks (id, name, created_at, updated_at) VALUES
('22222222-2222-2222-2222-222222222221', 'RANK_SEED', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('22222222-2222-2222-2222-222222222222', 'RANK_SPROUT', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('22222222-2222-2222-2222-222222222223', 'RANK_BUD', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('22222222-2222-2222-2222-222222222224', 'RANK_BLOOM', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('22222222-2222-2222-2222-222222222225', 'RANK_GARDEN', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- admins テーブル
INSERT INTO admins (id, role_id, name, email, password, created_at, updated_at) VALUES
('33333333-3333-3333-3333-333333333331', '11111111-1111-1111-1111-111111111111', 'Admin User', '<EMAIL>', '$2a$10$WvhhQ6ypV0N.U9sIy4MZmOmspsyZDIJh9kOx5XcBS5MtExgCq6nLW', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- users テーブル
INSERT INTO users (id, rank_id, name, email, password, enabled, image_url, created_at, updated_at) VALUES
('44444444-4444-4444-4444-444444444441', '22222222-2222-2222-2222-222222222221', 'User Seed', '<EMAIL>', '$2a$10$WvhhQ6ypV0N.U9sIy4MZmOmspsyZDIJh9kOx5XcBS5MtExgCq6nLW', true, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('44444444-4444-4444-4444-444444444442', '22222222-2222-2222-2222-222222222222', 'User Sprout', '<EMAIL>', '$2a$10$WvhhQ6ypV0N.U9sIy4MZmOmspsyZDIJh9kOx5XcBS5MtExgCq6nLW', true, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('44444444-4444-4444-4444-444444444443', '22222222-2222-2222-2222-222222222223', 'User Bud', '<EMAIL>', '$2a$10$WvhhQ6ypV0N.U9sIy4MZmOmspsyZDIJh9kOx5XcBS5MtExgCq6nLW', true, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('44444444-4444-4444-4444-444444444444', '22222222-2222-2222-2222-222222222224', 'User Bloom', '<EMAIL>', '$2a$10$WvhhQ6ypV0N.U9sIy4MZmOmspsyZDIJh9kOx5XcBS5MtExgCq6nLW', true, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('44444444-4444-4444-4444-444444444445', '22222222-2222-2222-2222-222222222225', 'User Garden', '<EMAIL>', '$2a$10$WvhhQ6ypV0N.U9sIy4MZmOmspsyZDIJh9kOx5XcBS5MtExgCq6nLW', true, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- themes テーブル
INSERT INTO themes (id, type, start_date, created_at, updated_at) VALUES
('55555555-5555-5555-5555-555555555551', 'PHOTO', '2025-04-01', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('55555555-5555-5555-5555-555555555552', 'PHOTO', '2025-07-01', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('55555555-5555-5555-5555-555555555553', 'PHOTO', '2025-10-01', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('55555555-5555-5555-5555-555555555554', 'PHOTO', '2026-01-01', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- theme_localizations テーブル
INSERT INTO theme_localizations (id, theme_id, language_code, title, description, created_at, updated_at) VALUES
('66666666-6666-6666-6666-666666666551', '55555555-5555-5555-5555-555555555551', 'JA', '春の花々', '美しい春の花のコレクション', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('66666666-6666-6666-6666-666666666552', '55555555-5555-5555-5555-555555555552', 'JA', '夏の風', 'リラックスした夏のひととき', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('66666666-6666-6666-6666-666666666553', '55555555-5555-5555-5555-555555555553', 'JA', '秋の葉', 'カラフルな秋の風景', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('66666666-6666-6666-6666-666666666554', '55555555-5555-5555-5555-555555555554', 'JA', '冬の雪', '雪に覆われた冬の風景', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('66666666-6666-6666-6666-666666666561', '55555555-5555-5555-5555-555555555551', 'EN', 'Spring Flowers', 'Beautiful spring flower collection', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('66666666-6666-6666-6666-666666666562', '55555555-5555-5555-5555-555555555552', 'EN', 'Summer Breeze', 'Relaxing summer moments', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('66666666-6666-6666-6666-666666666563', '55555555-5555-5555-5555-555555555553', 'EN', 'Autumn Leaves', 'Colorful autumn scenery', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('66666666-6666-6666-6666-666666666564', '55555555-5555-5555-5555-555555555554', 'EN', 'Winter Snow', 'Snowy winter landscapes', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- faves テーブル
INSERT INTO faves (id, user_id, theme_id, content, created_at, updated_at) VALUES
('77777777-7777-7777-7777-777777777772', '44444444-4444-4444-4444-444444444442', '55555555-5555-5555-5555-555555555552', 'photos/test01.jpg', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('77777777-7777-7777-7777-777777777773', '44444444-4444-4444-4444-444444444443', '55555555-5555-5555-5555-555555555552', 'photos/test02.jpg', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('77777777-7777-7777-7777-777777777774', '44444444-4444-4444-4444-444444444444', '55555555-5555-5555-5555-555555555552', 'photos/test03.jpg', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('77777777-7777-7777-7777-777777777775', '44444444-4444-4444-4444-444444444445', '55555555-5555-5555-5555-555555555552', 'photos/test04.jpg', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- likes テーブル
INSERT INTO likes (id, user_id, fave_id, created_at) VALUES
('88888888-8888-8888-8888-888888888881', '44444444-4444-4444-4444-444444444441', '77777777-7777-7777-7777-777777777773', CURRENT_TIMESTAMP),
('88888888-8888-8888-8888-888888888882', '44444444-4444-4444-4444-444444444441', '77777777-7777-7777-7777-777777777774', CURRENT_TIMESTAMP),
('88888888-8888-8888-8888-888888888883', '44444444-4444-4444-4444-444444444442', '77777777-7777-7777-7777-777777777773', CURRENT_TIMESTAMP),
('88888888-8888-8888-8888-888888888884', '44444444-4444-4444-4444-444444444442', '77777777-7777-7777-7777-777777777775', CURRENT_TIMESTAMP),
('88888888-8888-8888-8888-888888888885', '44444444-4444-4444-4444-444444444443', '77777777-7777-7777-7777-777777777774', CURRENT_TIMESTAMP),
('88888888-8888-8888-8888-888888888886', '44444444-4444-4444-4444-444444444443', '77777777-7777-7777-7777-777777777775', CURRENT_TIMESTAMP),
('88888888-8888-8888-8888-888888888887', '44444444-4444-4444-4444-444444444444', '77777777-7777-7777-7777-777777777775', CURRENT_TIMESTAMP);

-- reviews テーブル
INSERT INTO reviews (id, fave_id, admin_id, status, created_at, updated_at) VALUES
('99999999-9999-9999-9999-999999999991', '77777777-7777-7777-7777-777777777772', NULL, 'PENDING', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('99999999-9999-9999-9999-999999999992', '77777777-7777-7777-7777-777777777773', '33333333-3333-3333-3333-333333333331', 'APPROVED', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('99999999-9999-9999-9999-999999999993', '77777777-7777-7777-7777-777777777774', '33333333-3333-3333-3333-333333333331', 'REJECTED', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('99999999-9999-9999-9999-999999999994', '77777777-7777-7777-7777-777777777775', NULL, 'PENDING', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
